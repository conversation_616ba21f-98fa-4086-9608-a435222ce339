using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.Stripe;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers.Stripe;

public class StripeGetUpdateSubscriptionLinkCommandConsumer :
    CommandConsumer<StripeGetUpdateSubscriptionLinkCommand, StripeGetUpdateSubscriptionLinkCommandResponse>
{
    private readonly IStripeBillingPortalService _stripeBillingPortalService;

    public StripeGetUpdateSubscriptionLinkCommandConsumer(IServiceScopeFactory serviceScopeFactory,
        IStripeBillingPortalService stripeBillingPortalService) : base(serviceScopeFactory)
    {
        _stripeBillingPortalService = stripeBillingPortalService;
    }

    protected override async Task<StripeGetUpdateSubscriptionLinkCommandResponse> ConsumeCommand(
        StripeGetUpdateSubscriptionLinkCommand command, CancellationToken cancellationToken)
    {
        var subscriptionUpdateLink = await _stripeBillingPortalService.CreateUpdateSubscriptionLinkAsync(
            command.StripeAccountId, command.Mid, command.OrderId, command.InvoiceId);

        return new StripeGetUpdateSubscriptionLinkCommandResponse(subscriptionUpdateLink);
    }
}