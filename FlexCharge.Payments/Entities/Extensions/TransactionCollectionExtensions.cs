using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Payments.Domain.Payments.IntegrityManager;

namespace FlexCharge.Payments.Entities.Extensions;

public static class TransactionCollectionExtensions
{
    /// <summary>
    /// Returns a list of payments from a list of transactions
    /// Payment is a group of related transactions (e.g. An Authorization and a Capture)
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    public static IReadOnlyList<Payment> GetPayments(this IEnumerable<Transaction> transactions)
    {
        var transactionById = transactions.ToDictionary(x => x.Id, x => x);

        #region Helper local functions

        Transaction FindInitialParentTransaction(Transaction transaction)
        {
            Transaction firstParentTransactionId = transaction;
            while (firstParentTransactionId.ParentId != Guid.Empty)
            {
                firstParentTransactionId = transactionById[firstParentTransactionId.ParentId];
            }

            return firstParentTransactionId;
        }

        Dictionary<Guid, List<Transaction>> GroupTransactionsByInitialParentTransactionId()
        {
            Dictionary<Guid, List<Transaction>> groupedRelatedTransactions = new();

            // Adding initial parent transactions
            foreach (var transaction in transactions)
            {
                if (transaction.ParentId == Guid.Empty)
                {
                    groupedRelatedTransactions.Add(transaction.Id, new List<Transaction> {transaction});
                }
            }

            // Adding related transactions
            foreach (var transaction in transactions)
            {
                if (transaction.ParentId != Guid.Empty)
                {
                    var initialParentTransaction = FindInitialParentTransaction(transaction);

                    groupedRelatedTransactions[initialParentTransaction.Id].Add(transaction);
                }
            }

            return groupedRelatedTransactions;
        }

        #endregion

        // Chains of transactions that are related to each other
        Dictionary<Guid, List<Transaction>> relatedTransactionsGroups =
            GroupTransactionsByInitialParentTransactionId();

        List<Payment> payments = new();

        foreach (var relatedTransactions in relatedTransactionsGroups)
        {
            payments.Add(new Payment(relatedTransactions.Key, relatedTransactions.Value));
        }

        return payments;
    }
}