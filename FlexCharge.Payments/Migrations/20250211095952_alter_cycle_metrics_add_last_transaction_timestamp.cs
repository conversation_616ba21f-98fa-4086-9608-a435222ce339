using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class altercyclemetricsaddlasttransactiontimestamp : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSandbox",
                table: "Gateways");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastTransactionTimeStamp",
                table: "Metrics",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastTransactionTimeStamp",
                table: "Metrics");

            migrationBuilder.AddColumn<bool>(
                name: "IsSandbox",
                table: "Gateways",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
