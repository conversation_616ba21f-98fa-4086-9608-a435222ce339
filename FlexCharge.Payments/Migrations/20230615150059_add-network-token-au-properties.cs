using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class addnetworktokenauproperties : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "AccountUpdaterEnabled",
                table: "PaymentInstruments",
                type: "boolean",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<bool>(
                name: "NetworkTokenEnabled",
                table: "PaymentInstruments",
                type: "boolean",
                nullable: false,
                defaultValue: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountUpdaterEnabled",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "NetworkTokenEnabled",
                table: "PaymentInstruments");
        }
    }
}
