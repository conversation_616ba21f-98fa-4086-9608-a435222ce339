using System;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

public class PartnerBaseRequest : IPartnerRequest
{
    public Guid Pid { get; set; }
    public string ProviderNameIdentifier { get; set; }

    public int Amount { get; set; }
    public string CurrencyCode { get; set; }
}