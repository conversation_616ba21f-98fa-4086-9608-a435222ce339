using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FlexCharge.Common.Response;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public class VoidPaymentResult : BaseResult, IVoidPaymentResult
    {
        public Guid PaymentInstrumentId { get; set; }
        public string AuthorizationCode { get; set; }
    }
}
