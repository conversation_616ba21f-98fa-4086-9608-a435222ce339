using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.CompleteAuthorization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response.Errors;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Refund;

public class PaysafeCancelRefundResponse : IPaysafeResponse
{
    public string Id { get; set; }
    public Error Error { get; set; }
    public ActionLink[] Links { get; set; }
    public CompleteStatus Status { get; set; }
    public string TxnTime { get; set; }
}