using System;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cache;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DistributedCache;
using FlexCharge.Payments.Services.AccountUpdater.Models;
using FlexCharge.Payments.Services.AccountUpdater.VGS;
using FlexCharge.PaymentsUtils;
using FlexCharge.Utils;
using FluentValidation.Results;
using MassTransit;
using FlexCharge.Common.MassTransit;
using Microsoft.Extensions.Caching.Distributed;
using ValidationResult = FluentValidation.Results.ValidationResult;


namespace FlexCharge.Payments.Services.AccountUpdater.Models;

public abstract class RealTimeAccountUpdaterServiceBase<TRequest, TConfig> : IRealTimeAccountUpdater
{
    // private IPublishEndpoint publisher;
    // private readonly IRequestClient<UpdateCardCommand> _updateCardCommandClient;
    private readonly IPublishEndpoint _publisher;
    private readonly IDistributedCache _cache;
    private readonly IActivityService _activityService;

    protected RealTimeAccountUpdaterServiceBase(
        // IRequestClient<UpdateCardCommand> updateCardCommandClient,
        IPublishEndpoint publisher,
        IDistributedCache cache,
        IActivityService activityService)
    {
        // _updateCardCommandClient = updateCardCommandClient;
        _publisher = publisher;
        _cache = cache;
        _activityService = activityService;
    }

    protected abstract ValueTask<TRequest> GetRequest(AccountUpdaterCard ucard,
        TConfig configuration);


    protected abstract ValueTask<ValidationResult> ValidateRequest(TRequest request);

    private static string _salt = "F0229718-B957-43CB-A942-3ADF86127F0A";

    private CacheKey CreateAccountUpdaterRequestKey(AccountUpdaterCard card)
    {
        var key = CryptoSigningHelper.ComputeContentHash(
            $"{_salt}-{card.CardNumber}-{card.ExpiryMonth}-{card.ExpiryYear}");

        var result = CacheKeyFactory.CreateAccountUpdaterRequestKey(ProviderName, key);

        return result;
    }


    public async Task<AccountUpdaterResponse> UpdateCardAsync(
        AccountUpdaterCard auCard,
        Guid paymentInstrumentId,
        Guid orderId,
        Guid mid)
    {
        using var workspan = Workspan.Start<RealTimeAccountUpdaterServiceBase<TRequest, TConfig>>();

        #region GetActivity local function

        AccountUpdaterActivities GetActivity(FlexAccountUpdaterMessage message)
        {
            AccountUpdaterActivities result = message switch
            {
                FlexAccountUpdaterMessage.AccountNumberUpdated => AccountUpdaterActivities
                    .AccountUpdater_CardUpdated,
                FlexAccountUpdaterMessage.ExpirationUpdated => AccountUpdaterActivities
                    .AccountUpdater_ExpiryUpdated,
                FlexAccountUpdaterMessage.NotParticipatingAccount => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NotEligible,
                FlexAccountUpdaterMessage.NoMatch => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NoMatch,
                FlexAccountUpdaterMessage.NoUpdate => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NoChanges,
                FlexAccountUpdaterMessage.BlockedMerchant => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NotEligible,
                FlexAccountUpdaterMessage.InactiveCard => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NotEligible,
                FlexAccountUpdaterMessage.CardholderOptOut => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NotEligible,
                FlexAccountUpdaterMessage.Rejected => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NotEligible,
                FlexAccountUpdaterMessage.MerchantIsNotRegistered => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_NotEligible,
                FlexAccountUpdaterMessage.AccountIsClosed => AccountUpdaterActivities
                    .AccountUpdater_CardNotUpdated_AccountClosed,

                _ => AccountUpdaterActivities.AccountUpdater_CardNotUpdated_Other
            };


            if (result == AccountUpdaterActivities.AccountUpdater_CardNotUpdated_Other)
            {
                workspan.Log.Information("Unknown AccountUpdater message: {Message}", message);
            }

            return result;
        }

        #endregion

        AccountUpdaterResponse result = null;
        try
        {
            await _activityService.CreateActivityAsync(
                AccountUpdaterActivities.AccountUpdater_RealTimeUpdate_Requested,
                set => set
                    .CorrelationId(orderId)
                    .TenantId(mid)
                    .Meta(meta => meta
                        .SetValue("PaymentInstrumentId", paymentInstrumentId)
                        .SetValue("RealTime", true)
                    )
            );

            var configuration = await GetConfiguration();

            if (configuration == null)
            {
                workspan.Log.Fatal("RealTimeAccountUpdater configuration is null");
                return null;
            }

            var request = await GetRequest(auCard, configuration);

            var validationResult = await ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return new AccountUpdaterResponse()
                {
                    FlexValidationErrors = validationResult.Errors.ToList(),
                }; //!!!
            }

            var accountUpdaterRequestCacheKey = CreateAccountUpdaterRequestKey(auCard);

            FlexAccountUpdaterMessage? previousFlexMessage =
                await _cache.GetEnumValueAsync<FlexAccountUpdaterMessage>(accountUpdaterRequestCacheKey.Key);

            if (previousFlexMessage.HasValue &&
                previousFlexMessage !=
                FlexAccountUpdaterMessage.GenericError) // we want to retry in case of generic error
            {
                workspan.Log.Information("Account updater skipped - already in cache: {PreviousMessage}",
                    previousFlexMessage);

                await _activityService.CreateActivityAsync(
                    AccountUpdaterActivities.AccountUpdater_UpdateSkipped_AlreadyInCache,
                    set => set
                        .CorrelationId(orderId)
                        .TenantId(mid)
                        .Meta(meta => meta
                            .SetValue("PaymentInstrumentId", paymentInstrumentId)
                            .SetValue("RealTime", true)
                        )
                );

                return null; //!!!
            }

            Stopwatch paymentProviderExecutionTime = Stopwatch.StartNew();

            result = await UpdateCardImplementationAsync(request, configuration);

            paymentProviderExecutionTime.Stop();


            if (result?.Result != null) //todo!-add condition for error value
            {
                #region Processing account updater result

                AccountUpdaterCard card = result.Result.FlexMessage switch
                {
                    FlexAccountUpdaterMessage.AccountNumberUpdated
                        or FlexAccountUpdaterMessage.ExpirationUpdated => result.Result.AccountUpdaterCard,
                    _ => null
                };


                // We don't need any result here, as updated card information is already in Payments MS
                // and will be used for the next transaction
                await _publisher.RunCommandWithoutResponseAsync(
                    new UpdateCardCommand()
                    {
                        Card = card is null
                            ? null
                            : new()
                            {
                                CardNumber = card.CardNumber,
                                ExpiryMonth = card.ExpiryMonth,
                                ExpiryYear = card.ExpiryYear,
                                CardHolderName = card.CardHolderName
                            },

                        AccountUpdaterProvider = ProviderName,
                        PaymentInstrumentId = paymentInstrumentId,
                        FlexMessage = result.Result.FlexMessage,
                        ProviderMessage = result.Result.ProviderMessage,
                        AccountUpdaterType = AccountUpdaterType.RealTime
                    });


                await _cache.SetEnumValueAsync(accountUpdaterRequestCacheKey.Key, result.Result.FlexMessage,
                    accountUpdaterRequestCacheKey.CacheOptions);

                var activity = GetActivity(result.Result.FlexMessage);
                await _activityService.CreateActivityAsync(
                    activity,
                    set => set
                        .CorrelationId(orderId)
                        .TenantId(mid)
                        .Meta(meta => meta
                            .ServiceProvider(ProviderName)
                            .ProcessingTime(paymentProviderExecutionTime.Elapsed.TotalMilliseconds)
                            .SetValue("AccountUpdaterMessage", result.Result.FlexMessage.ToString())
                            .SetValue("PaymentInstrumentId", paymentInstrumentId)
                            .SetValue("RealTime", true)
                        )
                );

                #endregion
            }
            else
            {
                var jsonErrors = JsonSerializer.Serialize(result?.ProviderErrors);
                workspan.RecordError("Technical errors on updating card: {Errors}", jsonErrors);


                if (result?.ProviderErrors is not null)
                {
                    await _cache.SetEnumValueAsync(accountUpdaterRequestCacheKey.Key,
                        FlexAccountUpdaterMessage.ProviderValidationError,
                        accountUpdaterRequestCacheKey.CacheOptions);

                    await _activityService.CreateActivityAsync(
                        AccountUpdaterErrorActivities.AccountUpdater_Request_Error,
                        set => set
                            .CorrelationId(orderId)
                            .TenantId(mid)
                            .Meta(meta => meta
                                .Error(jsonErrors)
                                .SetValue("TraceId", result.ProviderTraceId)
                                .SetValue("PaymentInstrumentId", paymentInstrumentId)
                                .SetValue("RealTime", true)
                            )
                    );
                }
                else if (result?.FlexValidationErrors is not null)
                {
                    await _cache.SetEnumValueAsync(accountUpdaterRequestCacheKey.Key,
                        FlexAccountUpdaterMessage.FlexValidationError,
                        accountUpdaterRequestCacheKey.CacheOptions);

                    await _activityService.CreateActivityAsync(
                        AccountUpdaterErrorActivities.AccountUpdater_InternalValidation_Error,
                        set => set
                            .CorrelationId(orderId)
                            .TenantId(mid)
                            .Meta(meta => meta
                                .Error(JsonSerializer.Serialize(result.FlexValidationErrors))
                                .SetValue("PaymentInstrumentId", paymentInstrumentId)
                                .SetValue("RealTime", true)
                            )
                    );
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            workspan.RecordFatalException(ex);

            await _activityService.CreateActivityAsync(
                AccountUpdaterErrorActivities.AccountUpdater_Request_Error,
                set => set
                    .CorrelationId(orderId)
                    .TenantId(mid)
                    .Meta(meta => meta
                        .Error(ex.Message)
                        .SetValue("TraceId", result?.ProviderTraceId)
                        .SetValue("PaymentInstrumentId", paymentInstrumentId)
                        .SetValue("RealTime", true)
                    )
            );

            return null;
        }
    }

    public abstract string ProviderName { get; }


    protected abstract Task<AccountUpdaterResponse> UpdateCardImplementationAsync(TRequest card, TConfig config);

    protected abstract Task<TConfig> GetConfiguration();
}