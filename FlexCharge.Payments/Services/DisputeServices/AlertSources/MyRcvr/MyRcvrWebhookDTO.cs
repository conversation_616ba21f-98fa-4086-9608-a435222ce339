using System;
using System.Collections.Generic;

namespace FlexCharge.Payments.DTO;

public class MyRcvrWebhookDTO
{
    public string transactionCurrency { get; set; }
    public DateTime transactionDateTime { get; set; }
    public decimal transactionAmount { get; set; }
    public string descriptor { get; set; }
    public string authorizationCode { get; set; }
    public string accountNumber { get; set; }
    public string transactionID { get; set; }
    public string arn { get; set; }
    public string merchantOrderID { get; set; }
    public DateTime eventDateTime { get; set; }
    public string eventType { get; set; }
    public string requestID { get; set; }
    public string disputeCode { get; set; }
    public string? Issuer { get; set; }
}

public class MyRcvrEventDTO
{
    public DateTime eventDateTime { get; set; }
    public string eventType { get; set; }
    public string requestID { get; set; }
    public string disputeCode { get; set; }
}