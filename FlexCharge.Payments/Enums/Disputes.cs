using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Authentication.Filters;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Sms;
using FlexCharge.Payments.Authorization;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper;
using CsvHelper.Configuration;
using Dafny.Com.Amazonaws.Kms;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DTO.reports;
using FlexCharge.Payments.DTO.Reports;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.CheckoutDisputeService;
using FlexCharge.Payments.Services.DisputeServices;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Payments.Services.KountDisputeService;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Utils;
using System.Reflection;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Common.Exports;

namespace FlexCharge.Payments.Enums;

public class DisputeManagementSystem
{
    public const string None = "None";
    public const string FlexFactor = "FlexFactor";
    public const string Kount = "Kount";
    public const string KountDCM = "KountDCM";
    public const string Payarc = "Payarc";
    public const string Payshield = "Payshield";
    public const string Fiserv = "Fiserv";
    public const string Nuvei = "Nuvei";
    public const string Stripe = "Stripe";
    public const string Checkout = "Checkout";
    public const string Rapyd = "Rapyd";
    public const string Adyen = "Adyen";
    public const string Stax = "Stax";
    public const string Chargeback = "Chargeback";
    public const string Paysafe = "Paysafe";
    public const string PaysafeAmrFraud = "PaysafeAmrFraud";
    public const string MyRCVR = "MyRCVR";
    public const string Eurekapayments = "Eurekapayments";
    public const string Luqra = "Luqra";
    public const string Quantum = "Quantum";
    public const string Paycosmo = "Paycosmo";
    public const string Maverick = "Maverick";
    public const string EMS = "EMS";
    public const string Disputifer = "Disputifer";
    public const string Merlink = "Merlink";
    public const string DisputiferCDRN = "DisputiferCDRN";
    public const string DisputiferEthoca = "DisputiferEthoca";
    public const string DisputiferRDR = "DisputiferRDR";
    public const string VerifiRDR = "VerifiRDR";
}

public class AlertProviders
{
    public const string FlexFactor = "FlexFactor";
    public const string Kount = "Kount";
    public const string Payarc = "Payarc";
    public const string Payshield = "Payshield";
    public const string Chargeblast = "Chargeblast";
    public const string Fiserv = "Fiserv";
    public const string Nuvei = "Nuvei";
    public const string Stripe = "Stripe";
    public const string Checkout = "Checkout";
    public const string Rapyd = "Rapyd";
    public const string Adyen = "Adyen";
    public const string Stax = "Stax";
    public const string Chargeback = "Chargeback";
    public const string Paysafe = "Paysafe";
    public const string MyRCVR = "MyRCVR";
    public const string Eurekapayments = "Eurekapayments";
    public const string Luqra = "Luqra";
    public const string Quantum = "Quantum";
    public const string Paycosmo = "Paycosmo";
    public const string PayScout = "PayScout";
    public const string Maverick = "Maverick";
    public const string EMS = "EMS";
    public const string Disputifer = "Disputifer";
    public const string Verifi = "Verifi";
    public const string Ethoca = "Ethoca";
    public const string Merlink = "Merlink";
    public const string RMS = "RMS";
}

public class DisputeStage
{
    public const string RDR = "RDR";
    public const string CHARGEBACK = "CHARGEBACK";
    public const string EARLY_WARNING = "EARLY_WARNING";
    public const string DISPUTE_NOTICE = "DISPUTE_NOTICE";
    public const string FRAUD_NOTICE = "FRAUD_NOTICE";
    public const string PRE_DISPUTE = "PRE-DISPUTE";
    public const string REVERSED = "REVERSED";
    public const string UNMAPPED = "UNMAPPED";

    //Write a function that returns if its a early warning stage
    public static bool IsEarlyWarningStage(string stage)
    {
        return stage is EARLY_WARNING or DISPUTE_NOTICE or FRAUD_NOTICE;
    }
}

public class DisputeReportType
{
    public const string FlexDefaultRdr = "flex-default-rdr";
    public const string FlexDefaultChargeback = "flex-default-chargeback";
    public const string MerlinkChargeback = "merlink-chargeback";
    public const string MerlinkRdr = "merlink-rdr";

    public const string FiservDefault = "default";
    public const string StripeDefault = "default";
    public const string NuveiDefault = "default";
    public const string PaysafeDefault = "default";
    public const string PaysafeOld = "old";
    public const string PaysafeAMRFraud = "AMRFraud";
    public const string CheckoutFraud = "fraud";
    public const string PayarcDefault = "default";
    public const string DisputiferCdrn = "cdrn";
    public const string DisputiferRdr = "rdr";
    public const string DisputiferEthoca = "ethoca";
    public const string KountRdr = "rdr";
    public const string KountEthoca = "ethoca";
    public const string EurekapaymentsChargeback = "chargeback";
    public const string LuqraChargeback = "chargeback";
    public const string MaverickChargeback = "chargeback";
    public const string PaycosmoChargeback = "chargeback";
    public const string QuantumChargeback = "chargeback";
    public const string MyRCVRChargeback = "chargeback";
    public const string MyRCVRRdr = "rdr";
    public const string StaxDefault = "default";
    public const string EMSDefault = "default";
    public const string VerifiRdr = "rdr";
}

public class ReportSourceTypeToDtoTypeMap
{
    public static readonly Dictionary<(string source, string type), Type> sourceTypeToDtoTypeMap =
        new Dictionary<(string source, string type), Type>
        {
            {(AlertProviders.Fiserv, DisputeReportType.FiservDefault), typeof(FservReportItemDTO)},
            {(AlertProviders.Stripe, DisputeReportType.StripeDefault), typeof(StripeReportItemDTO)},
            {(AlertProviders.Nuvei, DisputeReportType.NuveiDefault), typeof(NuveiReportItemDTO)},
            {(AlertProviders.Paysafe, DisputeReportType.PaysafeDefault), typeof(PaysafeReportItemDTO)},
            {(AlertProviders.Paysafe, DisputeReportType.PaysafeOld), typeof(PaysafeOldReportItemDTO)},
            {(AlertProviders.Paysafe, DisputeReportType.PaysafeAMRFraud), typeof(PaysafeAmrFraudReportItemDTO)},
            {(AlertProviders.Checkout, DisputeReportType.CheckoutFraud), typeof(CheckoutReportItemDTO)},
            {(AlertProviders.Payarc, DisputeReportType.PayarcDefault), typeof(PayarcReportItemDTO)},
            {(AlertProviders.Disputifer, DisputeReportType.DisputiferCdrn), typeof(DisputiferReportItemDTO)},
            {(AlertProviders.Disputifer, DisputeReportType.DisputiferRdr), typeof(DisputiferReportItemDTO)},
            {(AlertProviders.Disputifer, DisputeReportType.DisputiferEthoca), typeof(DisputiferReportItemDTO)},
            {(AlertProviders.Kount, DisputeReportType.KountRdr), typeof(KountReportItemDTO)},
            {(AlertProviders.Kount, DisputeReportType.KountEthoca), typeof(KountReportItemDTO)},
            {
                (AlertProviders.Eurekapayments, DisputeReportType.EurekapaymentsChargeback),
                typeof(EurekapaymentsReportItemDTO)
            },
            {(AlertProviders.Luqra, DisputeReportType.LuqraChargeback), typeof(LuqraReportItemDTO)},
            {(AlertProviders.Maverick, DisputeReportType.MaverickChargeback), typeof(MaverickReportItemDTO)},
            {(AlertProviders.Paycosmo, DisputeReportType.PaycosmoChargeback), typeof(PaycosmoReportItemDTO)},
            {(AlertProviders.Quantum, DisputeReportType.QuantumChargeback), typeof(QuantumReportItemDTO)},
            {(AlertProviders.MyRCVR, DisputeReportType.MyRCVRChargeback), typeof(MyrcvrChargebackItemDTO)},
            {(AlertProviders.MyRCVR, DisputeReportType.MyRCVRRdr), typeof(MyRcvrRDRItemDTO)},
            {(AlertProviders.Stax, DisputeReportType.StaxDefault), typeof(StaxReportItemDTO)},
            {(AlertProviders.EMS, DisputeReportType.EMSDefault), typeof(EmsReportItemDTO)},
            {(AlertProviders.Verifi, DisputeReportType.VerifiRdr), typeof(VerifiRDRReportItemDTO)},
        };
}

public class AlertSystem
{
    public const string CDRN = "CDRN";
    public const string Ethoca = "Ethoca";
}

public class WebhookProviders
{
    public const string Stripe = "stripe";
    public const string Nuvei = "nuvei";
    public const string Checkout = "checkout";
    public const string Kount = "kount";
    public const string Payarc = "payarc";
    public const string MyRCVR = "myrcvr";
    public const string Disputifer = "disputifer";
    public const string Chargeblast = "chargeblast";
}

public class DisputeQueueNames
{
    public const string Payarc = "Payarc";
    public const string KountDCM = "KountDCM";
    public const string KountDCM_Dispute_Notices = "KountDCM_Dispute_Notice";
    public const string KountDCM_Fraud_Notices = "KountDCM_Fraud_Notice";
}