using System;
using FlexCharge.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Exceptions;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class AdministratorToolsController : ControllerBase
    {
        private readonly AppOptions _globalData;
        private readonly PostgreSQLDbContext _context;
        private readonly IServiceProvider _serviceProvider;
        private readonly IRequestClient<DeleteNetworkTokenCommand> _deleteNetworkTokenRequestClient;
        private readonly IRequestClient<GetNetworkTokenStatusCommand> _getNetworkTokenStatusRequestClient;

        public AdministratorToolsController(IOptions<AppOptions> globalData,
            PostgreSQLDbContext context, IServiceProvider serviceProvider,
            IRequestClient<DeleteNetworkTokenCommand> deleteNetworkTokenRequestClient,
            IRequestClient<GetNetworkTokenStatusCommand> getNetworkTokenStatusRequestClient)
        {
            _globalData = globalData.Value;
            _context = context;
            _serviceProvider = serviceProvider;
            _deleteNetworkTokenRequestClient = deleteNetworkTokenRequestClient;
            _getNetworkTokenStatusRequestClient = getNetworkTokenStatusRequestClient;
        }

        protected T GetRequiredService<T>()
        {
            return _serviceProvider.GetRequiredService<T>();
        }

        [HttpPost("test-dynamic-descriptor")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        public async Task<IActionResult> TestDynamicDescriptor([FromBody] TestDynamicDescriptorDTO payload)
        {
            try
            {
                var orderId = Guid.NewGuid();
                var payerId = Guid.NewGuid();

                var supportedGateway =
                    await _context.SupportedGateways.Where(x => x.Id == payload.ProviderId).FirstOrDefaultAsync();

                if (supportedGateway == null)
                {
                    throw new FlexValidationException("ProviderId", "Provider not found");
                }

                var paymentProviderResolver = GetRequiredService<ServiceCollectionExtensions.PaymentProviderResolver>();
                var paymentProvider = paymentProviderResolver(supportedGateway.NameIdentifier);

                var saleResult = await paymentProvider.SaleAsync(new SaleRequest
                {
                    Gateway = new Gateway()
                    {
                        SupportedGatewayId = supportedGateway.Id,
                        Name = supportedGateway.Name,
                        NameIdentifier = supportedGateway.NameIdentifier,
                        ProcessorId = supportedGateway.ProcessorId,
                        SupportedGateway = supportedGateway,
                        Capabilities = supportedGateway.Capabilities
                    },
                    SupportedGateway = supportedGateway,
                    Amount = payload.Amount,
                    CurrencyCode = "840",
                    OrderId = orderId,
                    PayerId = payerId,
                    Descriptor = new DescriptorDTO()
                    {
                        Name = payload.Descriptor,
                        Url = "https://www.flexfactor.io",
                        Country = "840"
                    },
                    BillingAddress = new BillingAddress()
                    {
                        Zip = "95035",
                        Address1 = "691S Blvd, Ste 212",
                        Country = "USA",
                        City = "Milpitas",
                        State = "CA"
                    },
                    CreditCard = new SaleRequestCreditCard
                    {
                        FirstName = "Guy",
                        LastName = "Vatman",
                        Number = payload.CardNumber,
                        VerificationValue = payload.Cvv,
                        Month = int.Parse(payload.ExpMonth),
                        Year = int.Parse(payload.ExpYear)
                    }
                });

                return Ok(saleResult);
            }
            catch (FlexValidationException e)
            {
                ModelState.AddModelError(e.Property, e.Message);
                return ValidationProblem();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return BadRequest(e);
            }
        }

        [HttpPost("delete-network-token")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        public async Task<IActionResult> DeleteNetworkToken([FromBody] string token)
        {
            try
            {
                var response = await _deleteNetworkTokenRequestClient.GetResponse<DeleteNetworkTokenCommandResponse>(
                    new DeleteNetworkTokenCommand
                    {
                        Token = token
                    });

                return Ok(response.Message);
            }
            catch (FlexValidationException e)
            {
                ModelState.AddModelError(e.Property, e.Message);
                return ValidationProblem();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return BadRequest(e);
            }
        }

        [HttpPost("check-network-token-status")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        public async Task<IActionResult> CheckNetworkTokenStatus([FromBody] string token)
        {
            try
            {
                var response =
                    await _getNetworkTokenStatusRequestClient.GetResponse<GetNetworkTokenStatusCommandResponse>(
                        new GetNetworkTokenStatusCommand()
                        {
                            Token = token
                        });

                return Ok(response.Message);
            }
            catch (FlexValidationException e)
            {
                ModelState.AddModelError(e.Property, e.Message);
                return ValidationProblem();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return BadRequest(e);
            }
        }
    }
}