using FlexCharge.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Shared.Merchants.Sites;
using FlexCharge.Common.Shared.Tracking;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Tracking.DistributedCache;
using FlexCharge.Tracking.DTO;
using FlexCharge.Tracking.Services.MerchantsInformationCacheService;
using FlexCharge.Tracking.Utils;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Tracking.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class ConfigurationController : BaseController
    {
        private readonly IMapper _mapper;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly AppOptions _globalData;
        private readonly IOptions<LogoImageValidationOptions> _logoImageValidationOptions;
        private readonly IOptions<BackgroundImageValidationOptions> _backgroundImageValidationOptions;
        private readonly IOptions<FaviconImageValidationOptions> _faviconImageValidationOptions;
        private readonly ICloudStorage _cloudStorage;
        private readonly IExternalRequestsDistributedCache _externalRequestsDistributedCache;
        private readonly IMerchantInformationCacheService _merchantInformationCacheService;
        private readonly IMerchantSitesService _merchantSitesService;

        public ConfigurationController(
            IOptions<AppOptions> globalData,
            IMapper mapper,
            PostgreSQLDbContext dbContext,
            IOptions<LogoImageValidationOptions> logoImageValidationOptions,
            IOptions<BackgroundImageValidationOptions> backgroundImageValidationOptions,
            IOptions<FaviconImageValidationOptions> faviconImageValidationOptions,
            ICloudStorage cloudStorage,
            IExternalRequestsDistributedCache externalRequestsDistributedCache,
            IMerchantInformationCacheService merchantInformationCacheService,
            IMerchantSitesService merchantSitesService)
        {
            _mapper = mapper;
            _dbContext = dbContext;
            _globalData = globalData.Value;
            _cloudStorage = cloudStorage;
            _externalRequestsDistributedCache = externalRequestsDistributedCache;
            _merchantInformationCacheService = merchantInformationCacheService;
            _logoImageValidationOptions = logoImageValidationOptions;
            _backgroundImageValidationOptions = backgroundImageValidationOptions;
            _faviconImageValidationOptions = faviconImageValidationOptions;
            _merchantSitesService = merchantSitesService;
        }

        [HttpGet("{mid:guid}/widget")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ConfigurationResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetWidgetConfig([FromQuery] Guid? siteId, [FromRoute] Guid mid)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, mid, _globalData);

            try
            {
                if (mid == null || mid == Guid.Empty)
                {
                    ModelState.AddModelError("General", "Merchant ID is required");
                    return ValidationProblem();
                }

                MerchantInformation? merchantInformation =
                    await _merchantInformationCacheService.GetMerchantInformationAsync(mid);

                if (siteId == null || siteId == Guid.Empty || merchantInformation?.IgnoreSiteIdFromClient == true)
                {
                    var url = UrlHelper.GetOriginFromHeader(Request);

                    if (string.IsNullOrWhiteSpace(url))
                    {
                        workspan.Log.Error("Get configuration > Origin or Referer header is missing");
                        return BadRequest("Unknown site");
                    }
                    else
                    {
                        siteId = await _merchantSitesService.GetSiteIdByHostOrDefault(mid, new Uri(url));

                        if (siteId == null)
                        {
                            return NotFound();
                        }
                    }
                }

                var configuration =
                    await _merchantInformationCacheService.GetMerchantSiteConfigurationAsync(mid, siteId.Value);

                if (configuration is null)
                {
                    return Ok(new ConfigurationResponse());
                }

                var vm = new ConfigurationResponse()
                {
                    Id = configuration.Id,
                    LogoFile = configuration.Logo,
                    IsLogoVisible = configuration.IsLogoVisible,
                    SuccessUrl = configuration.SuccessUrl,
                    RejectUrl = configuration.RejectUrl,
                    CancelUrl = configuration.CancelUrl,
                    CheckoutUrl = configuration.CheckoutUrl.RootElement.GetRawText(),
                    ConsentFlowConfiguration = configuration.ConsentFlowConfiguration,
                    FontFamily = configuration.FontFamily,
                    FontColor = configuration.PrimaryColor,
                    ButtonColor = configuration.SecondaryColor,
                    ButtonsCornersRadius = int.Parse(configuration.ButtonsCornersRadius),
                    ModalCornersRadius = int.Parse(configuration.ModalCornersRadius),
                    ScrapingData = configuration.ScrapingData,
                    SubmitData = configuration.SubmitData,
                    TokenizationPublicKey = configuration.TokenizationPublicKey,
                    BackgroundImage = configuration.BackgroundImage,
                    BackgroundColor = configuration.BackgroundColor,
                    SiteId = siteId.Value
                };

                if (merchantInformation != null)
                {
                    vm.IsCrawlingEnabled = merchantInformation.IsCrawlingEnabled;
                    vm.IsIframeMessagesCollectEnabled = merchantInformation.IsIframeMessagesCollectEnabled;
                }

                return Ok(vm);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                return BadRequest();
            }
        }

        [HttpGet("{mid:guid}/checkout/{siteId:guid}")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(CheckoutConfigurationResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCheckoutConfig([FromRoute] Guid? siteId, [FromRoute] Guid? mid)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, mid, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var configuration =
                    await _merchantInformationCacheService.GetMerchantSiteConfigurationAsync(mid.Value, siteId.Value);

                if (configuration is null)
                {
                    return Ok(new CheckoutConfigurationResponse());
                }
                else
                {
                    var vm = new CheckoutConfigurationResponse();
                    vm.Id = configuration.Id;
                    vm.CheckoutPageBackgroundImage = configuration.CheckoutPageBackgroundImage;
                    vm.CheckoutPagePrimaryColor = configuration.CheckoutPagePrimaryColor;
                    vm.CheckoutPageSecondaryColor = configuration.CheckoutPageSecondaryColor;
                    vm.CheckoutPageFontColor = configuration.CheckoutPageFontColor;
                    vm.LogoFile = configuration.Logo;
                    vm.Favicon = configuration.Favicon;
                    vm.IsCheckoutOneColumn = configuration.IsLogoVisible;
                    vm.CancelUrl = configuration.CancelUrl;

                    return Ok(vm);
                }
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                return BadRequest();
            }
        }

        #region Obsolete Endpoints

        [Obsolete]
        [HttpGet("{id:guid}")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ConfigurationResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetWidgetConfig([FromRoute] Guid? id)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, id, _globalData);

            try
            {
                throw new Exception();

                #region Commented - Obsolete

                // var merchantConfigurationKey =
                //     id.HasValue ? CacheKeyFactory.CreateMerchantConfigurationKey(id.Value) : null;
                //
                // var configuration = merchantConfigurationKey != null
                //     ? await _distributedCache.GetValueAsync<GetMerchantConfigurationCommandResponse>(merchantConfigurationKey.Key)
                //     : null;
                //
                //
                // if (configuration == null)
                // {
                //     configuration =
                //         await _dbContext.Configurations.Where(x => x.MerchantId == id).FirstOrDefaultAsync();
                //
                //     #region Storing configuration in distributed cache
                //
                //     if (merchantConfigurationKey != null)
                //     {
                //         await _distributedCache.SetAsync(merchantConfigurationKey.Key, configuration,
                //             merchantConfigurationKey.CacheOptions);
                //     }
                //
                //     #endregion
                // }
                //
                // if (configuration is null)
                // {
                //     return Ok(new ConfigurationResponse());
                // }
                //
                //
                // var vm = new ConfigurationResponse();
                // vm.Id = configuration.Id;
                // vm.LogoFile = configuration.Logo;
                // vm.IsLogoVisible = configuration.IsLogoVisible;
                // vm.SuccessUrl = configuration.SuccessUrl;
                // vm.RejectUrl = configuration.RejectUrl;
                // vm.CancelUrl = configuration.CancelUrl;
                // vm.CheckoutUrl = configuration.CheckoutUrl.RootElement.GetRawText();
                // vm.ConsentFlowConfiguration = configuration.ConsentFlowConfiguration;
                // vm.FontFamily = configuration.FontFamily;
                // vm.FontColor = configuration.PrimaryColor;
                // vm.ButtonColor = configuration.SecondaryColor;
                // vm.ButtonsCornersRadius = int.Parse(configuration.ButtonsCornersRadius);
                // vm.ModalCornersRadius = int.Parse(configuration.ModalCornersRadius);
                // vm.ScrapingData = configuration.ScrapingData;
                // vm.SubmitData = configuration.SubmitData;
                // vm.TokenizationPublicKey = configuration.TokenizationPublicKey;
                // vm.BackgroundImage = configuration.BackgroundImage;
                // vm.BackgroundColor = configuration.BackgroundColor;
                //
                // return Ok(vm);

                #endregion
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                return BadRequest();
            }
        }

        [Obsolete]
        [HttpGet("{id:guid}/checkout")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(CheckoutConfigurationResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCheckoutConfig([FromRoute] Guid? id)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, id, _globalData);

            try
            {
                throw new Exception();

                #region Commented - Obsolete

                // var merchantConfigurationKey =
                //     id.HasValue ? CacheKeyFactory.CreateMerchantConfigurationKey(id.Value) : null;
                //
                // var configuration = merchantConfigurationKey != null
                //     ? await _distributedCache.GetValueAsync<GetMerchantConfigurationCommandResponse>(merchantConfigurationKey.Key)
                //     : null;
                //
                //
                // if (configuration == null)
                // {
                //     configuration =
                //         await _dbContext.Configurations.Where(x => x.MerchantId == id).FirstOrDefaultAsync();
                //
                //     #region Storing configuration in distributed cache
                //
                //     if (merchantConfigurationKey != null)
                //     {
                //         await _distributedCache.SetAsync(merchantConfigurationKey.Key, configuration,
                //             merchantConfigurationKey.CacheOptions);
                //     }
                //
                //     #endregion
                // }
                //
                // if (configuration is null)
                // {
                //     return Ok(new CheckoutConfigurationResponse());
                // }
                //
                // var vm = new CheckoutConfigurationResponse();
                // vm.Id = configuration.Id;
                // vm.CheckoutPageBackgroundImage = configuration.CheckoutPageBackgroundImage;
                // vm.CheckoutPagePrimaryColor = configuration.CheckoutPagePrimaryColor;
                // vm.CheckoutPageSecondaryColor = configuration.CheckoutPageSecondaryColor;
                // vm.CheckoutPageFontColor = configuration.CheckoutPageFontColor;
                // vm.LogoFile = configuration.Logo;
                // vm.Favicon = configuration.Favicon;
                // vm.IsCheckoutOneColumn = configuration.IsLogoVisible;
                //
                // return Ok(vm);

                #endregion
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                return BadRequest();
            }
        }

        #endregion

        [HttpPut("{id:guid}")]
        [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(200)]
        public async Task<IActionResult> PutWidget([FromRoute] Guid id, ConfigurationRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                Entities.Configuration configuration;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    configuration = await _dbContext.Configurations.Where(x => x.Id == id).SingleOrDefaultAsync(token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var pid = GetPID();

                    if (pid == null || pid == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    configuration = await _dbContext.Configurations
                        .Join(_dbContext.Merchants,
                            configuration => configuration.MerchantId,
                            merchant => merchant.Mid,
                            (configuration, merchant) => new
                            {
                                Configuration = configuration,
                                Pid = merchant.Pid
                            })
                        .Where(joined => joined.Configuration.Id == id && joined.Pid == pid)
                        .Select(joined => joined.Configuration)
                        .SingleOrDefaultAsync(token);
                }
                else
                {
                    configuration = await _dbContext.Configurations.Where(x => x.MerchantId == GetMID() && x.Id == id)
                        .SingleOrDefaultAsync(token);
                }

                // var configs = await _senseJsExternalPostgreSQLDbContext.Configurations.Where(x => x.MerchantId == GetMID())
                //     .SingleOrDefaultAsync(token);

                if (configuration is null)
                {
                    ModelState.AddModelError("General", "Configuration not found");
                    return ValidationProblem();
                }

                var logoImageValidationOptions = _logoImageValidationOptions.Value;
                if (!string.IsNullOrWhiteSpace(payload.LogoFile))
                {
                    configuration.Logo = await StoreImageAsync(id, payload.LogoFile, logoImageValidationOptions, "logo",
                        configuration.SiteId);
                }
                else if (payload.LogoFile == string.Empty)
                {
                    //delete img ref (still need to delete from s3)
                    configuration.Logo = null;
                }

                var backgroundImageValidationOptions = _backgroundImageValidationOptions.Value;
                if (!string.IsNullOrWhiteSpace(payload.BackgroundImage))
                {
                    configuration.BackgroundImage = await StoreImageAsync(id, payload.BackgroundImage,
                        backgroundImageValidationOptions, "widget_background", configuration.SiteId);
                }
                else if (payload.BackgroundImage == string.Empty)
                {
                    //delete img ref (still need to delete from s3
                    configuration.BackgroundImage = null;
                }

                configuration.IsLogoVisible = payload.IsLogoVisible;

                if (
                    HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN) ||
                    HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    configuration.PrimaryColor = payload.FontColor;
                    configuration.SecondaryColor = payload.ButtonColor;
                    configuration.FontFamily = payload.FontFamily;

                    if (payload.ButtonsCornersRadius.HasValue)
                        configuration.ButtonsCornersRadius = payload.ButtonsCornersRadius.ToString();

                    if (payload.ModalCornersRadius.HasValue)
                        configuration.ModalCornersRadius = payload.ModalCornersRadius.ToString();
                }

                configuration.SuccessUrl = payload.SuccessUrl;
                configuration.CancelUrl = payload.CancelUrl;
                configuration.RejectUrl = payload.RejectUrl;
                configuration.BackgroundColor = payload.BackgroundColor;


                if (payload.ConsentFlowConfiguration.HasValue)
                    configuration.ConsentFlowConfiguration = payload.ConsentFlowConfiguration.Value;


                _dbContext.Configurations.Update(configuration);
                await _dbContext.SaveChangesAsync(token);

                #region Invalidating configuration in distributed cache

                var merchantSiteConfigurationKey =
                    TrackingSharedCacheKeyFactory.CreateMerchantSiteConfigurationKey(configuration.MerchantId,
                        configuration.SiteId);
                await _externalRequestsDistributedCache.RemoveAsync(merchantSiteConfigurationKey.Key);

                #endregion

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpGet("{id:guid}/modules")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetModules([FromRoute] Guid id,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, id, _globalData);
            var response = new GetModulesResponseDTO();

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var configuration = await GetConfigurationAsync(id, token);

                if (configuration is null)
                {
                    ModelState.AddModelError("General", "Configuration not found");
                    return ValidationProblem();
                }

                response.Modules = configuration.Dependencies == null
                    ? null
                    : JsonConvert.DeserializeObject<List<string>>(configuration.Dependencies);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPut("{id:guid}/modules")]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(200)]
        public async Task<IActionResult> UpdateModules([FromRoute] Guid id, UpdateModulesRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var configuration = await GetConfigurationAsync(id, token);

                if (configuration is null)
                {
                    ModelState.AddModelError("General", "Configuration not found");
                    return ValidationProblem();
                }

                #region Invalidating configuration in distributed cache

                var merchantSiteConfigurationKey =
                    TrackingSharedCacheKeyFactory.CreateMerchantSiteConfigurationKey(configuration.MerchantId,
                        configuration.SiteId);
                await _externalRequestsDistributedCache.RemoveAsync(merchantSiteConfigurationKey.Key);

                #endregion

                configuration.Dependencies = JsonConvert.SerializeObject(payload.Modules);

                _dbContext.Configurations.Update(configuration);
                await _dbContext.SaveChangesAsync(token);


                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpPut("{id:guid}/checkout")]
        [Authorize(policy: MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(200)]
        public async Task<IActionResult> PutCheckoutWidget([FromRoute] Guid id, CheckoutConfigurationRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, payload, _globalData);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var configuration = await GetConfigurationAsync(id, token);

                // var configs = await _senseJsExternalPostgreSQLDbContext.Configurations.Where(x => x.MerchantId == GetMID())
                //     .SingleOrDefaultAsync(token);

                if (configuration is null)
                {
                    ModelState.AddModelError("General", "Configuration not found");
                    return ValidationProblem();
                }

                var logoImageValidationOptions = _logoImageValidationOptions.Value;
                if (!string.IsNullOrWhiteSpace(payload.LogoFile))
                {
                    configuration.Logo = await StoreImageAsync(id, payload.LogoFile, logoImageValidationOptions, "logo",
                        configuration.SiteId);
                }
                else if (payload.LogoFile == string.Empty)
                {
                    //delete img ref (still need to delete from s3)
                    configuration.Logo = null;
                }

                var backgroundImageValidationOptions = _backgroundImageValidationOptions.Value;
                if (!string.IsNullOrWhiteSpace(payload.CheckoutPageBackgroundImage))
                {
                    configuration.CheckoutPageBackgroundImage = await StoreImageAsync(id,
                        payload.CheckoutPageBackgroundImage,
                        backgroundImageValidationOptions, "checkout_background", configuration.SiteId);
                }
                else if (payload.CheckoutPageBackgroundImage == string.Empty)
                {
                    //delete img ref (still need to delete from s3
                    configuration.CheckoutPageBackgroundImage = null;
                }

                var faviconImageValidationOptions = _faviconImageValidationOptions.Value;
                if (!string.IsNullOrWhiteSpace(payload.Favicon))
                {
                    configuration.Favicon =
                        await StoreImageAsync(id, payload.Favicon, faviconImageValidationOptions, "favicon",
                            configuration.SiteId);
                }
                else if (payload.Favicon == string.Empty)
                {
                    //delete img ref (still need to delete from s3
                    configuration.Favicon = null;
                }

                configuration.CheckoutPagePrimaryColor = payload.CheckoutPagePrimaryColor;
                configuration.CheckoutPageSecondaryColor = payload.CheckoutPageSecondaryColor;
                configuration.CheckoutPageFontColor = payload.CheckoutPageFontColor;
                configuration.IsCheckoutOneColumn = payload.IsCheckoutOneColumn;

                _dbContext.Configurations.Update(configuration);
                await _dbContext.SaveChangesAsync(token);

                #region Invalidating configuration in distributed cache

                var merchantSiteConfigurationKey =
                    TrackingSharedCacheKeyFactory.CreateMerchantSiteConfigurationKey(configuration.MerchantId,
                        configuration.SiteId);
                await _externalRequestsDistributedCache.RemoveAsync(merchantSiteConfigurationKey.Key);

                #endregion


                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return BadRequest();
            }
        }

        [HttpGet("{mid:guid}/siteConfig")] // Get siteId
        [AllowAnonymous]
        [ProducesResponseType(typeof(CheckoutConfigurationResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSiteId([FromRoute] Guid mid)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, mid, _globalData)
                .Baggage("Mid", mid);

            try
            {
                var url = UrlHelper.GetOriginFromHeader(Request);

                workspan
                    .Baggage("Url", url);

                if (string.IsNullOrWhiteSpace(url))
                {
                    workspan.Log.Error("GetSiteId > Origin or Referer header is missing");
                    return BadRequest("Unknown site");
                }

                var siteId = await _merchantSitesService.GetSiteIdByHostOrDefault(mid, new Uri(url));

                workspan
                    .Tag("SiteId", siteId);

                if (siteId == null)
                    return NotFound();

                return Ok(siteId);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                return BadRequest();
            }
        }

        private async Task<string> StoreImageAsync(Guid merchantId, string fileBase64,
            ImageValidationOptions logoValidationOptions, string fileNamePrefix, Guid? siteId = null)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, merchantId, _globalData);
            string imageUrl;

            string imageStorageName = null, imageStorageFolder = null, fileName = null;

            // Removing "data:image/png;base64," data format prefix, if exists
            if (fileBase64.StartsWith("data:"))
            {
                int commaIndex = fileBase64.IndexOf(',');
                if (commaIndex > 0)
                {
                    fileBase64 = fileBase64.Substring(commaIndex + 1);
                }
            }

            (byte[] image, int Width, int Height) newImage;
            try
            {
                var imageBytes = Convert.FromBase64String(fileBase64);
                using (var readStream = new MemoryStream(imageBytes))
                {
                    newImage = await ImageValidation.LoadAndVerifyImageAsync(readStream, logoValidationOptions,
                        sanitize: true);
                }

                imageStorageName = Environment.GetEnvironmentVariable("AWS_S3_STATIC_FILES_BUCKET");
                var siteIdPostfix = "";
                if (siteId.HasValue)
                    siteIdPostfix = $"/{siteId.Value}";
                imageStorageFolder =
                    $"{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}/{merchantId}{siteIdPostfix}";

                fileName = $"{fileNamePrefix}_w{newImage.Width}_h{newImage.Height}.png";
                using (var image_stream = new MemoryStream(newImage.image))
                {
                    await _cloudStorage.UploadFileAsync(image_stream, imageStorageName,
                        fileName,
                        folder: imageStorageFolder,
                        allowPublicAccess: false);
                }

                //imageUrl = $"{imageStorageName}/{imageStorageFolder}/{fileName}";
                imageUrl = await _cloudStorage.CreatePublicFileUrlThroughCDNAsync(imageStorageName, fileName,
                    imageStorageFolder);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                throw;
            }

            return imageUrl;
        }

        private async Task<Entities.Configuration> GetConfigurationAsync(Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<TrackingController>(this, id, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    return await _dbContext.Configurations.Where(x => x.Id == id).SingleOrDefaultAsync(token);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var pid = GetPID();

                    if (pid != null && pid != Guid.Empty)
                    {
                        return await _dbContext.Configurations
                            .Join(_dbContext.Merchants,
                                configuration => configuration.MerchantId,
                                merchant => merchant.Mid,
                                (configuration, merchant) => new
                                {
                                    Configuration = configuration,
                                    Pid = merchant.Pid
                                })
                            .Where(joined => joined.Configuration.Id == id && joined.Pid == pid)
                            .Select(joined => joined.Configuration)
                            .SingleOrDefaultAsync(token);
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return await _dbContext.Configurations.Where(x => x.MerchantId == GetMID() && x.Id == id)
                        .SingleOrDefaultAsync(token);
                }
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }
    }
}