using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Audit.Migrations
{
    /// <inheritdoc />
    public partial class changedcompoundindexinPendingAuditstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PendingAudits_RuleSet_CorrelationId_TenantId_IsRollingTime",
                table: "PendingAudits");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_CorrelationId",
                table: "PendingAudits",
                column: "CorrelationId");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_NextAuditTime",
                table: "PendingAudits",
                column: "NextAuditTime");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_RuleSet",
                table: "PendingAudits",
                column: "RuleSet");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_RuleSet_CorrelationId_AuditTimeRange_IsRollin~",
                table: "PendingAudits",
                columns: new[] { "RuleSet", "CorrelationId", "AuditTimeRange", "IsRollingTime" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PendingAudits_CorrelationId",
                table: "PendingAudits");

            migrationBuilder.DropIndex(
                name: "IX_PendingAudits_NextAuditTime",
                table: "PendingAudits");

            migrationBuilder.DropIndex(
                name: "IX_PendingAudits_RuleSet",
                table: "PendingAudits");

            migrationBuilder.DropIndex(
                name: "IX_PendingAudits_RuleSet_CorrelationId_AuditTimeRange_IsRollin~",
                table: "PendingAudits");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_RuleSet_CorrelationId_TenantId_IsRollingTime",
                table: "PendingAudits",
                columns: new[] { "RuleSet", "CorrelationId", "TenantId", "IsRollingTime" },
                unique: true);
        }
    }
}
