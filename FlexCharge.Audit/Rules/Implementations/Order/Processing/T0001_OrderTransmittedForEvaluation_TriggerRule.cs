using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Order.Processing;

public class T0001_OrderTransmittedForEvaluation_TriggerRule :
    TriggerRuleBase<OrderTransmittedForEvaluation_TriggerFact>
{
    public override void DefineTriggerWhen()
    {
        When()
            // MATCH FACTS
            .Match<Transmit_Evaluate_Fact>()
            ;
    }


    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<Transmit_Evaluate_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", "2000")
                .SetValue("ExternalOrderId", Guid.NewGuid())
            ));

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}