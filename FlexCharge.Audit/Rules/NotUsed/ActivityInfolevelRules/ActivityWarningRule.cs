// using NRules.Fluent.Dsl;
//
// namespace FlexCharge.Audit.Rules.InfolevelRules;
//
// public class ActivityWarningRule : Rule
// {
//     public override void Define()
//     {
//         ActivityFact activity = null;
//
//         When()
//             .Activity(() => activity, a => a.InformationLevel == InfoLevel.Warning);
//         Then()
//             .Warning(() => $"Activity {activity.Name} is warning");
//     }
// }