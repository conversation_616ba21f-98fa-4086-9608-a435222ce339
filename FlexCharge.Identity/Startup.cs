using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Swagger;
using FlexCharge.Services.Identity.Services;
using FlexCharge.Identity.Entities;
using FlexCharge.Identity.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using Amazon;
using Amazon.CognitoIdentityProvider;
using Amazon.Extensions.CognitoAuthentication;
using Amazon.Runtime;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cors;
using FlexCharge.Common.Emails;
using FlexCharge.Identity.Consumers;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;


namespace FlexCharge.Identity
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;

            Console.WriteLine("In ConfigureServices via Environment class: " +
                              Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"));
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddActivities();

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            //Identity
            //services.AddTransient<IRefreshTokenRepository, RefreshTokenRepository>();
            services.AddTransient<IPasswordHasher<User>, PasswordHasher<User>>();
            services.AddTransient<IJwtHandler, JwtHandler>();
            services.AddTransient<IClaimsProvider, ClaimsProvider>();
            services.AddTransient<IEmailSender, SendGridEmailSender>();
            // services.AddTransient<IUserRepository, UserRepository>();
            //services.AddTransient<IApiIdentityService, ApiIdentityService>();
            services.AddTransient<IRefreshTokenService, CognitoRefreshTokenService>();
            //services.AddTransient<IAccessTokenService, AccessTokenService>();
            //services.AddTransient<IApiKeyService, ApiKeyService>();
            services.AddTransient<IIdentityService, CognitoIdentityService>();
            services.AddTransient<IUsersService, UsersService>();
            services.AddTransient<IRecaptchaService, RecaptchaService>();
            services.AddRecaptcha();
            //services.AddTransient<IRecaptchaService, RecaptchaService>();

            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            // Check for null just so when invoking migrations we dont get null reference exception
            //An error occurred while accessing the Microsoft.Extensions.Hosting services. Continuing without the application service provider. Error: Object reference not set to an instance of an object.
            //Unable to create an object of type 'PostgreSqlDbContext'. For the different patterns supported at design time, see https://go.microsoft.com/fwlink/?linkid=851728
            if (Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY") != null)
            {
                AmazonCognitoIdentityProviderClient _client =
                    new AmazonCognitoIdentityProviderClient(
                        new BasicAWSCredentials(Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_KEY"),
                            Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_SECRET")),
                        RegionEndpoint.USEast1);

                CognitoUserPool cognitoUserPool = new CognitoUserPool(
                    Environment.GetEnvironmentVariable("AWS_COGNITO_USER_POOL_ID"),
                    Environment.GetEnvironmentVariable("AWS_IAM_COGNITO_CLIENT_ID"), _client);

                services.AddSingleton<IAmazonCognitoIdentityProvider>(_client);
                services.AddSingleton<CognitoUserPool>(cognitoUserPool);
            }

            services.AddCognitoIdentity();

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.AddOptions();

            services.AddJwt();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString = "Host=localhost;Database=fc.identity;Username=identity-service-staging;Password=*****";
#endif

            services
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString,
                    npgsqlOptionsAction: options => options.EnableRetryOnFailure(3));
            //.AddNpgsqlDbContext<PostgreSQLDbContext>(options => options.UseNpgsql("Host=localhost;Database=fc.identity;Username=identity-service-staging;Password=*****"));


            services.AddMassTransit<Startup>();

            services.AddRedisCache();

            services.AddAutoMapper(typeof(Startup));
            services.AddSwaggerDocs();

            services.AddEmailClient();

            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.USER));
                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN, SuperAdminGroups.MERCHANT_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ALL_MERCHANTS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS_AND_SALESAGENCY_ADMINS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.SALESAGENCY_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.ADMIN_RISK,
                                SuperAdminGroups.ADMIN_OPERATIONS,
                                SuperAdminGroups.ADMIN_SALES,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                }
            );

            //services.AddCorsPolicy();

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });


            services.AddControllers();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            app.UseSwaggerDocs();
            //app.UseHttpsRedirection();

            app.UseRouting();

            //app.UseAuthentication();
            app.UseAuthorization();
            //app.UseAccessTokenValidator();

            app.UseAutoMigrations<PostgreSQLDbContext>();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            app.UseMassTransit();
        }
    }
}