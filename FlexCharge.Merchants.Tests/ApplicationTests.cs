// using System;
// using System.Collections.Generic;
// using System.IO;
// using System.Linq;
// using System.Threading.Tasks;
// using AutoMapper;
// using FlexCharge.Common.Cloud;
// using FlexCharge.Common.Emails;
// using FlexCharge.Merchants.DTO;
// using FlexCharge.Merchants.Entities;
// using FlexCharge.Merchants.Services.ApplicationServices;
// using FlexCharge.Utils;
// using MassTransit;
// using Microsoft.AspNetCore.Http;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore.Query.Internal;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using Moq;
// using NUnit.Framework;
//
//
// namespace FlexCharge.Merchants.Tests;
//
// [TestFixture]
// public class ApplicationTests
// {
//     public IConfiguration _config { get; set; }
//
//     [SetUp]
//     public void Setup()
//     {
//         _config = new ConfigurationBuilder()
//             .SetBasePath(Directory.GetCurrentDirectory())
//             .AddJsonFile(@"appsettings.Staging.json", false, false)
//             .AddEnvironmentVariables().Build();
//     }
//
//     [Test]
//     public void Test1()
//     {
//         Assert.Pass();
//     }
//
//     // [Test]
//     // public async Task Test_Application_Create_HappyFlow()
//     // {
//     //     var AppDTO = new ApplicationNewRequest
//     //     {
//     //         Send = false,
//     //         ExternalId = null,
//     //         LegalEntityName = null,
//     //         Dba = null,
//     //         Type = null,
//     //         TaxId = null,
//     //         Descriptor = null,
//     //         BusinessEstablishedDate = null,
//     //         Mcc = null,
//     //         Industry = null,
//     //         Pcidss = false,
//     //         EcommercePlatform = null,
//     //         Website = null,
//     //         Status = 0,
//     //         Description = null,
//     //         TransactionInformation = null,
//     //         Address = new AddressDTO
//     //         {
//     //             Addressline1 = null,
//     //             Addressline2 = null,
//     //             ZipCode = null,
//     //             State = null,
//     //             StateCode = null,
//     //             City = null,
//     //             Country = null
//     //         },
//     //         PrimaryContact = new PrimaryContact
//     //         {
//     //             FirstName = null,
//     //             LastName = null,
//     //             Email = null,
//     //             Phone = null
//     //         },
//     //         BankAccountInformation = null
//     //     };
//     //     
//     //     var mockContext = new Mock<PostgreSQLDbContext>();
//     //     //mockContext.Setup(m => m.Applications).Returns(GetSampleApplications()); 
//     //
//     //     var mockMapper = new Mock<IMapper>();
//     //     mockMapper.Setup(x => x.Map<Application, ApplicationNewRequest>(It.IsAny<Application>()))
//     //         .Returns(AppDTO);    
//     //     
//     //     var publisher = new Mock<IPublishEndpoint>();
//     //     var httpContext = new Mock<IHttpContextAccessor>();
//     //     var logger = new Mock<ILogger<ApplicationService>>();
//     // var _fileOtions = Options.Create(new LogoImageValidationOptions
//     //         {
//     //             MinFileSize = 1024,
//     //             MaxFileSize = 1048576,
//     //             MinWidth = 32,
//     //             MaxWidth = 2048,
//     //             MaxHeight = 32,
//     //             MinHeight = 2048,
//     //             MinAspectRatio = 0.01F,
//     //             MaxAspectRatio = 100
//     //         });
//     //
//     //     var storage = new Mock<ICloudStorage>();
//     //     var emailSender = new Mock<IEmailSender>();
//     //    
//     //     var applicationService = new ApplicationService(mockContext.Object, mockMapper.Object, logger.Object,
//     //         publisher.Object, httpContext.Object, fileOptions.Object, storage.Object,emailSender.Object,_config);
//     //
//     //     //applicationService.Setup(x => x.CreateAsync(It.IsAny<ApplicationNewRequest>())).Throws(new Exception());
//     //     var act = await applicationService.CreateAsync(AppDTO);
//     //
//     //     //mockContext.Verify(x => x.SaveChanges(), Times.Once);
//     //     Assert.Pass();
//     // }
//     //
//     //
//     // public IQueryable<Application> GetSampleApplications()
//     // {
//     //     return default;
//     //     //return new EntityQueryable<Application>();
//     // }
// }