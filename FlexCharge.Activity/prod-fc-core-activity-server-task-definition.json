{"containerDefinitions": [{"name": "core-activity", "image": "556663010871.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-activity:8f31fb9993e42e8ef513fd02312a27dc8cd8a701", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "activity_service_prod"}, {"name": "DB_DATABASE", "value": "fc_activity"}, {"name": "DB_HOST", "value": "flexcharge-prod.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_xHv2n5DhZ"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "NEW_RELIC_APP_NAME", "value": "Activity-production"}, {"name": "OTEL_SERVICE_NAME", "value": "Activity-production"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_DB_ACTIVITY_PASSWORD-zNRgXt"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_COGNITO_CLIENT_ID-KMMao8"}, {"name": "AWS_IAM_COGNITO_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_COGNITO_CLIENT_SECRET-rp49Rz"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SNS_IAM_ACCESS_KEY-lbCS4b"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PROD_SNS_IAM_SECRET_KEY-a0ce7k"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:556663010871:secret:PRODUCTION-API-CLIENT-JWT-SIGNING-KEY-iGzRih"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-activity-server-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-activity-server-prod", "taskRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Production-Role", "executionRoleArn": "arn:aws:iam::556663010871:role/ecsTaskExecutionWithSecretAccess-Production-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096"}