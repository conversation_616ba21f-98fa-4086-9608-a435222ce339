namespace FlexCharge.Common.Authentication
{
    public class MyClaimTypes
    {
        public const string ROLE = "role";

        public const string COGNITO_GROUP = "cognito:groups";

        // public const string CAMPAIGN_ID = "campaign_role";
        // public const string CAMPAIGN_ROLE = "campaign_role";
        public const string PERMISSION = "permission";

        public const string SCOPE = "scope";

        //public const string USERPHONE = "usp";
        public const string ACCOUNT_ID = "custom:aid";
        public const string MERCHANT_ID = "custom:mid";
        public const string PARTNER_ID = "custom:pid";
        public const string INTEGRATION_PARTNER_ID = "custom:ipid";
        public const string SALESAGENCY_ID = "custom:said";
        public const string IS_PII_ENABLED = "custom:isPiiMasked";
        public const string GIVEN_NAME = "given_name";
        public const string FAMILY_NAME = "family_name";

        // public const string CONTACT = "ct";
        // public const string CONTACT_NAME = "ctn";
        // public const string CONTACT_EMAIL = "cte";
    }
}