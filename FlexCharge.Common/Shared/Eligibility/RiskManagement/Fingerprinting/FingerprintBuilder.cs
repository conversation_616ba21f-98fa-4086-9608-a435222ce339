using System;
using System.Collections.Generic;

namespace FlexCharge.Eligibility.RiskManagement.Fingerprinting;

public class FingerprintBuilder : IDisposable
{
    private readonly FingerprintType _type;
    private readonly IList<IFingerprint> _fingerprintsList;
    private readonly IDictionary<FingerprintType, IFingerprint> _sourceFingerprints;
    private readonly FingerprintCategory _category;
    private string? _meta;
    private List<IFingerprint> _fingerprintParts = new();
    private bool _IsFingerprintValid;

    public FingerprintBuilder(IList<IFingerprint> fingerprintsList,
        IDictionary<FingerprintType, IFingerprint> sourceFingerprints, FingerprintCategory category,
        FingerprintType type)
    {
        _type = type;
        _fingerprintsList = fingerprintsList;
        _sourceFingerprints = sourceFingerprints;
        _category = category;
        _IsFingerprintValid = true;
    }

    public void Dispose()
    {
        Build();
    }

    private void Build()
    {
        if (_IsFingerprintValid) // all fingerprint parts have values
        {
            var fingerprint = new Fingerprint(_category, _type, _fingerprintParts.ToArray());
            _fingerprintsList.Add(fingerprint);

            if (!string.IsNullOrWhiteSpace(_meta))
                fingerprint.Meta = _meta;
        }
    }

    public void AddPart(FingerprintType fingerprintType)
    {
        if (_sourceFingerprints.TryGetValue(fingerprintType, out var sourceFingerprint))
        {
            AddPart(sourceFingerprint);
        }
        else _IsFingerprintValid = false;
    }

    public void AddPart(IFingerprint fingerprint)
    {
        _fingerprintParts.Add(fingerprint);
    }

    public void SetMeta(string meta)
    {
        _meta = meta;
    }
}