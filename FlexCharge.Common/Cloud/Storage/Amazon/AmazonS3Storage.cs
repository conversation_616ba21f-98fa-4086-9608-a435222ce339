using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using FlexCharge.Common.Telemetry;
using MassTransit.AmazonSqsTransport;
using Microsoft.Extensions.Options;

namespace FlexCharge.Common.Cloud.Storage;

public class AmazonS3Storage : AmazonS3ServiceBase, ICloudStorage
{
    private readonly IOptions<AmazonS3StorageOptions> _amazonS3StorageOptions;
    private readonly IAmazonS3 _amazonS3Client;

    public AmazonS3Storage(IOptions<AmazonS3StorageOptions> amazonS3StorageOptions,
        IAmazonS3 amazonS3Client)
    {
        _amazonS3StorageOptions = amazonS3StorageOptions;
        _amazonS3Client = amazonS3Client;
    }

    public async Task UploadFileAsync(Stream fileStreamToUpload, string storageName, string filePath,
        bool uploadFileInChunks = false)
    {
        await UploadFileAsync(fileStreamToUpload, storageName, filePath,
            false, uploadFileInChunks,
            _amazonS3Client);
    }


    public async Task UploadFileAsync(Stream fileStreamToUpload, string storageName,
        string fileName, string folder, bool allowPublicAccess, bool uploadFileInChunks = false)
    {
        await UploadFileAsync(fileStreamToUpload, storageName, GetS3StorageKey(fileName, folder),
            allowPublicAccess, uploadFileInChunks,
            _amazonS3Client);
    }

    private static async Task UploadFileAsync(Stream fileStreamToUpload, string storageName, string filePath,
        bool allowPublicAccess, bool uploadFileInChunks, IAmazonS3 client)
    {
        if (!uploadFileInChunks)
        {
            TransferUtility utility = new TransferUtility(client);

            TransferUtilityUploadRequest request = new TransferUtilityUploadRequest()
            {
                BucketName = storageName,
                Key = filePath,
                // InputStream = new MemoryStream(localPath),
                // AutoCloseStream = true,
                // StorageClass = S3StorageClass.ReducedRedundancy                
            };

            if (allowPublicAccess) request.CannedACL = S3CannedACL.PublicRead;

            request.InputStream = fileStreamToUpload;
            await utility.UploadAsync(request);
        }
        else
        {
            await AmazonS3MultiPartUpload.UploadFileInChunksAsync(storageName, filePath, fileStreamToUpload, client);

            if (allowPublicAccess)
            {
                var putAclRequest = new PutACLRequest
                {
                    BucketName = storageName,
                    Key = filePath,
                    CannedACL = S3CannedACL.PublicRead
                };

                await client.PutACLAsync(putAclRequest);
            }
        }
    }

    public async Task DeleteFileAsync(string storageName, string fileName, string folder)
    {
        DeleteObjectRequest request = new DeleteObjectRequest
        {
            BucketName = storageName,
            Key = GetS3StorageKey(fileName, folder)
        };

        var deleteObjectResponse = await _amazonS3Client.DeleteObjectAsync(request);
        deleteObjectResponse.EnsureSuccessfulResponse();
    }

    public async Task DeleteFileAsync(string storageName, string sourceFilePath)
    {
        if (await DoesFileExistAsync(storageName, sourceFilePath))
        {
            var deleteObjectRequest = new DeleteObjectRequest
            {
                BucketName = storageName,
                Key = sourceFilePath
            };

            var deleteObjectResponse = await _amazonS3Client.DeleteObjectAsync(deleteObjectRequest);
            deleteObjectResponse.EnsureSuccessfulResponse();
        }
    }

    private static string GetS3StorageKey(string fileName, string subDirectory)
    {
        return !string.IsNullOrWhiteSpace(subDirectory) ? $"{subDirectory}/{fileName}" : fileName;
    }

    public Task<string> CreateExpiringPublicFileUrl(DateTime linkExpires, string storageName, string fileName,
        string folder)
    {
        return CreateExpiringPublicFileUrl(linkExpires, storageName, fileName, folder, _amazonS3Client);
    }

    private static Task<string> CreateExpiringPublicFileUrl(DateTime linkExpires, string storageName, string fileName,
        string folder, IAmazonS3 client)
    {
        string url = GeneratePreSignedUrl(client, storageName, GetS3StorageKey(fileName, folder),
            linkExpires);
        return Task.FromResult(url);
    }

    private static string GeneratePreSignedUrl(IAmazonS3 s3Client, string bucketName, string objectKey,
        DateTime linkExpires)
    {
        string urlString = "";

        GetPreSignedUrlRequest s3_request = new GetPreSignedUrlRequest
        {
            BucketName = bucketName,
            Key = objectKey,
            Expires = linkExpires
        };
        urlString = s3Client.GetPreSignedURL(s3_request);

        return urlString;
    }

    public Task<string> CreatePublicFileUrlThroughCDNAsync(string storageName, string fileName, string folder)
    {
        if (string.IsNullOrWhiteSpace(_amazonS3StorageOptions?.Value?.PublicFilesCDN))
            throw new Exception("Amazon S3 CDN for public files must be configured");

        string cdnUrl = _amazonS3StorageOptions?.Value?.PublicFilesCDN;
        StringBuilder url = new StringBuilder();

        url.Append(cdnUrl);
        url.Append("/");

        if (!string.IsNullOrWhiteSpace(folder))
        {
            url.Append(folder);
            url.Append("/");
        }

        url.Append(fileName);

        return Task.FromResult(url.ToString());
    }

    public async Task CreateFolderAsync(string storageName, string folderPath)
    {
        folderPath = CheckAndCorrectFolderPath(folderPath);

        if (await DoesFolderExistAsync(storageName, folderPath)) return;

        PutObjectRequest request = new PutObjectRequest()
        {
            BucketName = storageName,
            StorageClass = S3StorageClass.Standard,
            ServerSideEncryptionMethod = ServerSideEncryptionMethod.None,
            Key = folderPath,
            ContentBody = string.Empty
        };

        // add try catch in case you have exceptions shield/handling here 
        PutObjectResponse response = await _amazonS3Client.PutObjectAsync(request);
        response.EnsureSuccessfulResponse();
    }

    public async Task<List<string>?> ListFilesAndFoldersRecursiveAsync(
        string storageName, string folderPath,
        int? maxResults = null)
    {
        folderPath = CheckAndCorrectFolderPath(folderPath);

        if (!await DoesFolderExistAsync(storageName, folderPath)) return null;

        ListObjectsV2Request request = new()
        {
            BucketName = storageName,
            Prefix = folderPath,
            StartAfter = folderPath,
            MaxKeys = maxResults != null ? maxResults.Value : 1000, // AWS will return up to 1000 keys anyway
        };

        // add try catch in case you have exceptions shield/handling here 
        var response = await _amazonS3Client.ListObjectsV2Async(request);
        response.EnsureSuccessfulResponse();

        var results = response.S3Objects
            .Select(x => x.Key);

        return results
            .ToList();
    }

    private string CheckAndCorrectFolderPath(string folderPath)
    {
        //Ensure folder path ends with a slash
        if (!folderPath.EndsWith('/')) folderPath = folderPath + '/';

        return folderPath;
    }

    public async Task CreateFolderIfMissingAsync(string storageName, string folderPath)
    {
        folderPath = CheckAndCorrectFolderPath(folderPath);

        if (!(await DoesFolderExistAsync(storageName, folderPath)))
        {
            await CreateFolderAsync(storageName, folderPath);
        }
    }

    public async Task<bool> DoesFolderExistAsync(string storageName, string folderPath)
    {
        folderPath = CheckAndCorrectFolderPath(folderPath);

        return await ObjectExistsAsync(_amazonS3Client, storageName, folderPath);
    }


    public async Task MoveFileAsync(string storageName, string sourceFilePath, string destinationFilePath,
        bool overwrite)
    {
        //see: https://github.com/aws/aws-sdk-net/blob/master/sdk/src/Services/S3/Custom/_bcl/IO/S3FileInfo.cs

        await CopyFileAsync(storageName, sourceFilePath, destinationFilePath, overwrite);
        await DeleteFileAsync(storageName, sourceFilePath);
    }

    public async Task<Stream> GetFileStreamAsync(string storageName, string filePath)
    {
        TransferUtility transferUtility = new TransferUtility(_amazonS3Client);

        //Read file contents from amazon s3
        var request = new TransferUtilityOpenStreamRequest
        {
            BucketName = storageName,
            Key = filePath
        };

        var stream = await transferUtility.OpenStreamAsync(request);
        return stream;
    }

    public async Task<long> GetFileSizeAsync(string storageName, string filePath)
    {
        var request = new GetObjectMetadataRequest
        {
            BucketName = storageName,
            Key = filePath
        };

        // Get the metadata for the object and retrieve the size
        GetObjectMetadataResponse response = await _amazonS3Client.GetObjectMetadataAsync(request);
        long fileSize = response.ContentLength;

        return fileSize;
    }


    public async Task<bool> DoesFileExistAsync(string storageName, string filePath)
    {
        if (string.IsNullOrWhiteSpace(filePath)) throw new ArgumentNullException("File path cannot be empty or null");
        if (filePath.EndsWith('/')) throw new ArgumentException("File path cannot end with a slash");

        return await ObjectExistsAsync(_amazonS3Client, storageName, filePath);
    }

    public async Task<bool> ObjectExistsAsync(IAmazonS3 s3Client, string bucketName, string prefix)
    {
        //see: https://stackoverflow.com/questions/3526585/determine-if-an-object-exists-in-a-s3-bucket-based-on-wildcard

        var request = new ListObjectsV2Request
        {
            BucketName = bucketName,
            Prefix = prefix,
            MaxKeys = 1
        };

        var response = await s3Client.ListObjectsV2Async(request);
        return response.S3Objects.Count > 0;

        // var allObjectKeys = await s3Client.GetAllObjectKeysAsync(bucketName, prefix, null);
        // return allObjectKeys.Count > 0;
    }

    public async Task CopyFileAsync(string storageName, string sourceFilePath, string destinationFilePath,
        bool overwrite)
    {
        //see: https://github.com/aws/aws-sdk-net/blob/master/sdk/src/Services/S3/Custom/_bcl/IO/S3FileInfo.cs

        if (!overwrite)
        {
            if (await DoesFileExistAsync(storageName, destinationFilePath))
            {
                throw new IOException("File already exists");
            }
        }

        var request = new CopyObjectRequest
        {
            SourceBucket = storageName,
            SourceKey = sourceFilePath,
            DestinationBucket = storageName,
            DestinationKey = destinationFilePath
        };
        var copyObjectResponse = await _amazonS3Client.CopyObjectAsync(request);
        copyObjectResponse.EnsureSuccessfulResponse();
    }

    public async Task CopyFileAsync(string sourceStorageName, string sourceFilePath, string destinationStorageName,
        string destinationFilePath, bool overwrite)
    {
        //see: https://github.com/aws/aws-sdk-net/blob/master/sdk/src/Services/S3/Custom/_bcl/IO/S3FileInfo.cs

        if (!overwrite)
        {
            if (await DoesFileExistAsync(sourceStorageName, destinationFilePath))
            {
                throw new IOException("File already exists");
            }
        }

        var getObjectRequest = new GetObjectRequest
        {
            BucketName = sourceStorageName,
            Key = sourceFilePath
        };

        var getObjectResponse = await _amazonS3Client.GetObjectAsync(getObjectRequest);
        getObjectResponse.EnsureSuccessfulResponse();

        using (var stream = getObjectResponse.ResponseStream)
        {
            var putObjectRequest = new PutObjectRequest
            {
                BucketName = destinationStorageName,
                Key = destinationFilePath,
                InputStream = stream
            };

            var putObjectResponse = await _amazonS3Client.PutObjectAsync(putObjectRequest);
            putObjectResponse.EnsureSuccessfulResponse();
        }
    }

    #region AmazonS3MultiPartUpload

    public class AmazonS3MultiPartUpload
    {
        // see: https://ercanerdogan.medium.com/uploading-large-files-to-amazon-s3-using-multipart-upload-in-net-56ea03692d05

        public static async Task UploadFileInChunksAsync(string bucketName, string keyName, Stream inputStream,
            IAmazonS3 s3Client)
        {
            // 1. Start a multipart upload
            var initiateResponse = await s3Client.InitiateMultipartUploadAsync(
                new InitiateMultipartUploadRequest
                {
                    BucketName = bucketName,
                    Key = keyName
                });

            string uploadId = initiateResponse.UploadId;
            Workspan.Current?.Log.Information($"Initiated upload with ID: {uploadId}");

            const int partSize = 5 * 1024 * 1024; // 5MB chunks
            byte[] buffer = new byte[partSize];
            var partETags = new List<PartETag>();

            try
            {
                int totalBytesRead = 0;
                int partNumber = 1;
                int bytesRead;

                // 2. Upload each chunk
                while ((bytesRead = await inputStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    var memoryStream = new MemoryStream(buffer, 0, bytesRead);

                    var partRequest = new UploadPartRequest
                    {
                        BucketName = bucketName,
                        Key = keyName,
                        UploadId = uploadId,
                        PartNumber = partNumber,
                        PartSize = bytesRead,
                        InputStream = memoryStream
                    };


                    var partResponse = await s3Client.UploadPartAsync(partRequest);
                    partETags.Add(new PartETag(partNumber, partResponse.ETag));

                    Workspan.Current?.Log.Information($"Uploaded part {partNumber}, ETag: {partResponse.ETag}");
                    partNumber++;

                    totalBytesRead += bytesRead;
                }

                // 3. Complete the multipart upload
                var completeRequest = new CompleteMultipartUploadRequest
                {
                    BucketName = bucketName,
                    Key = keyName,
                    UploadId = uploadId,
                    PartETags = partETags
                };


                var completeResponse = await s3Client.CompleteMultipartUploadAsync(completeRequest);
                Workspan.Current?.Log.Information(
                    $"Upload completed, Location: {completeResponse.Location}, Size: {totalBytesRead}");
            }
            catch (Exception e)
            {
                // Abort the multipart upload if there's an error
                Workspan.Current?.RecordException(e, $"Amazon S3 multipart upload failed for key: {keyName}");

                var abortRequest = new AbortMultipartUploadRequest
                {
                    BucketName = bucketName,
                    Key = keyName,
                    UploadId = uploadId
                };
                await s3Client.AbortMultipartUploadAsync(abortRequest);

                throw;
            }
        }

        #endregion
    }
}