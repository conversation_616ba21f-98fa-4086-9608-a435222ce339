using System;
using Microsoft.AspNetCore.Http;

namespace FlexCharge.Common.HTTP;

public static class HttpContextHelper
{
    public static string GetConsumerIP(HttpContext httpContext)
    {
        string consumerIp = null;

        // handle standardized 'Forwarded' header
        string forwarded = httpContext.Request.Headers["Forwarded"];
        if (!string.IsNullOrEmpty(forwarded))
        {
            foreach (string segment in forwarded.Split(',')[0].Split(';'))
            {
                string[] pair = segment.Trim().Split('=');
                if (pair.Length == 2 && pair[0].Equals("for", StringComparison.OrdinalIgnoreCase))
                {
                    string ip = pair[1].Trim('"');

                    // IPv6 addresses are always enclosed in square brackets
                    int left = ip.IndexOf('['), right = ip.IndexOf(']');
                    if (left == 0 && right > 0)
                    {
                        consumerIp = ip.Substring(1, right - 1);
                        break;
                    }

                    // strip port of IPv4 addresses
                    int colon = ip.IndexOf(':');
                    if (colon != -1)
                    {
                        consumerIp = ip.Substring(0, colon);
                        break;
                    }

                    // this will return IPv4, "unknown", and obfuscated addresses
                    consumerIp = ip;
                }
            }
        }

        if (consumerIp is null)
        {
            consumerIp = httpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString();
        }

        #region [Commented] Logging Http Request Headers

        // if (EnvironmentHelper.IsInStaging)
        // {
        //     using var workspan = Workspan.Start<TrackingController>();
        //     workspan.Log.Information($"CUSTIP: {consumerIp}");
        //     
        //     StringBuilder sb = new StringBuilder();
        //     sb.AppendLine("RHEADERS: ");
        //     foreach (var h in HttpContext.Request.Headers)
        //     {
        //         StringBuilder sb2 = new StringBuilder();
        //         foreach (var v in h.Value)
        //         {
        //             sb2.Append(v);
        //             sb2.Append(" | ");
        //         }
        //
        //         sb.AppendLine($"{h.Key} : {sb2.ToString()}");
        //
        //         workspan.Log.Information(sb.ToString());
        //     }
        //     
        // }

        #endregion

        return consumerIp;

        //see: // see: https://learn.microsoft.com/en-us/aspnet/core/host-and-deploy/proxy-load-balancer?view=aspnetcore-7.0#forwarded-headers-middleware-options
        //see: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/x-forwarded-headers.html

        // Check if the X-Forwarded-For header exists in the request
        // if (HttpContext.Request.Headers.TryGetValue("X-Forwarded-For", out var headerValues))
        // {
        //     // The header may contain a comma-separated list of IPs; the first IP is usually the client's real IP
        //     return headerValues.FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim();
        // }
        // else
        // {
        // //If the X-Forwarded-For header doesn't exist, fall back to the default remote IP
        //      return HttpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString();
        //}
    }
}