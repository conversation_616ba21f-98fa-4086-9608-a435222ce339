using System;
using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Common.Partitions;

internal static class PartitionScriptHelper
{
    internal static string AssignCountScript(string variableName, string partitionedTableName) =>
        $"{variableName} := (select count(*) from \"{partitionedTableName}\");";

    internal static string Assert(string variableName1, string variableName2, string message) =>
        $"ASSERT {variableName1} >= {variableName2}  , '{message}';";

    internal static string CreateDailyTableForPartitionScript(DateOnly from, string parentTableName,
        out string newTableName)
    {
        var result = CreateTableForPartitionScript(from, from.AddDays(1), parentTableName, out newTableName);
        return result;
    }

    internal static string SetLocalStatementTimeoutScript(int timeout) => $"SET LOCAL statement_timeout = {timeout};";


    internal static string CreateIndexScript(string tableName, string columnName, bool isUnique, string partitionColumn)
    {
        return $"CREATE INDEX ON {tableName} (\"{columnName}\");";
    }


    internal static string LockTableScript(string tableName)
    {
        return $"LOCK TABLE {tableName} IN ACCESS EXCLUSIVE MODE;";
    }

    internal static string DetachTableScript(string parentTableName, string tableName)
    {
        return $"ALTER TABLE \"{parentTableName}\" DETACH PARTITION {tableName} ;"; /// CONCURRENTLY-if version>=14
    }

    internal static string MergeTableScript(string toTableName, string fromTableName, string partitionedColumnName)
    {
        return @$"Insert into {toTableName} select * from {fromTableName} order by ""{partitionedColumnName}"";";
    }

    internal static string RaiseNotice(string message) => $"RAISE NOTICE '{message}';";


    internal static string MergeTableIntoMonthlyScript(string monthlyTableName,
        MonthYear month, DateOnly enddate, DateOnly? startdate, string defaultTableName, string partitionedColumnName)
    {
        var firstDate = new DateOnly(month.Year, month.Month, 1);
        var lastDate = firstDate.AddMonths(1).AddDays(-1);
        lastDate = lastDate < enddate ? lastDate : enddate;
        if (startdate.HasValue && startdate.Value > firstDate)
        {
            firstDate = startdate.Value.AddDays(1);
        }

        var format = "yyyy-MM-dd";
        var fromFormat = firstDate.ToString(format);
        var toFormat = lastDate.ToString(format);
        if (firstDate > enddate) return null;
        var result = $@"WITH deletions AS (
    DELETE FROM {defaultTableName} WHERE
	""{partitionedColumnName}""::DATE  between '{fromFormat}' and '{toFormat}' returning *
     )
     INSERT INTO {monthlyTableName} select * from deletions;";

        return result;
    }

    internal static string MergeDefaultTableIntoDailyScript(string dailyTableName, string defaultTableName,
        DateOnly date, string partitionedColumnName)
    {
        var format = "yyyy-MM-dd";
        var dateFormat = date.ToString(format);


        var result = $@"WITH deletions AS (
    DELETE FROM {defaultTableName} WHERE
	""{partitionedColumnName}""::DATE  = '{dateFormat}'  returning *
     )
     INSERT INTO {dailyTableName} select * from deletions;";

        return result;
    }


    internal static string DropTableScript(string tableName)
    {
        return $"DROP TABLE {tableName};";
    }

    internal static string TruncateTableScript(string tableName)
    {
        return $"Truncate TABLE {tableName};";
    }

    internal static string AttachDailyTableScript(string tableName, DateOnly from, string partitionTableName)
    {
        var firstDate = from;
        var lastDate = from.AddDays(1);

        var format = "yyyy-MM-dd";
        var fromFormat = firstDate.ToString(format);
        var toFormat = lastDate.ToString(format);
        return
            $"ALTER TABLE \"{partitionTableName}\" ATTACH PARTITION {tableName} FOR VALUES FROM ('{fromFormat}') TO ('{toFormat}' );";
    }

    internal static string AttachMonthlyTableScript(string tableName, DateOnly firstDate, DateOnly lastDate,
        string partitionTableName)
    {
        var format = "yyyy-MM-dd";
        var fromFormat = firstDate.ToString(format);
        var toFormat = lastDate.ToString(format);
        return
            $"ALTER TABLE \"{partitionTableName}\" ATTACH PARTITION {tableName} FOR VALUES FROM ('{fromFormat}') TO ('{toFormat}' );";
    }

    internal static string CopyIndicesScript(string fromTableName, string toTableName)
    {
        return $"f_copy_idx('{fromTableName}', '{toTableName}');";
    }


    internal static string AttachDefaultPartitionTableScript(string partitionedTableName, string defaultTableName)
    {
        if (string.IsNullOrWhiteSpace(defaultTableName)) return string.Empty;
        return
            $"ALTER TABLE \"{partitionedTableName}\" ATTACH PARTITION {defaultTableName} DEFAULT;";
    }

    internal static string DetachDefaultTableScript(string partitionedTableName, string defaultTableName)
    {
        if (string.IsNullOrWhiteSpace(defaultTableName)) return string.Empty;
        return
            $"ALTER TABLE \"{partitionedTableName}\" DETACH PARTITION {defaultTableName} ;"; /// CONCURRENTLY-if version>=14
    }

    internal static string CreateMonthlyTableForPartitionScript(List<TablePartitionInfo> partitionInfos,
        string parentTableName,
        out string newTableName)
    {
        var from = partitionInfos.Min(p => p.From);
        var to = partitionInfos.Max(p => p.To);
        var result = CreateTableForPartitionScript(from, to, parentTableName, out newTableName);
        return result;
    }

    internal static string CreateMonthlyTableForPartitionScript(MonthYear monthYear, string parentTableName,
        out string newTableName, int deltaYears = 0)
    {
        var nollM = monthYear.Month < 10 ? "0" : "";
        var fromSuffix = $"{monthYear.Year}_{nollM}{monthYear.Month}";
        var from = DateOnly.FromDateTime(new DateTime(monthYear.Year - deltaYears, monthYear.Month, 1));
        var to = from.AddDays(1);
        var format = "yyyy-MM-dd";
        var fromFormat = from.ToString(format).Replace("-", "_");
        var toFormat = to.ToString(format);
        newTableName = $"{parentTableName.ToLower()}_{fromSuffix}";
        return
            $@"CREATE TABLE IF NOT EXISTS {newTableName} PARTITION OF ""{parentTableName}"" FOR VALUES FROM ('{fromFormat}') TO ('{toFormat}');";
    }


    internal static string CreateTableForPartitionScript(DateOnly from, DateOnly to, string parentTableName,
        out string newTableName, int deltaYears = 0)
    {
        var isdaily = to.DayNumber - from.DayNumber == 1;
        var format = "yyyy-MM-dd";
        var nollM = from.Month < 10 ? "0" : "";
        var nollD = from.Day < 10 ? "0" : "";
        var fromSuffix = isdaily
            ? $"{from.Year}_{nollM}{from.Month}_{nollD}{from.Day}"
            : $"{from.Year}_{nollM}{from.Month}";
        var fromFormat = from.AddYears(-deltaYears).ToString(format).Replace("-", "_");
        var toFormat = to.AddYears(-deltaYears).ToString(format);

        // var fromSuffix = isdaily?fromFormat.Replace("-", "_"):$"{from.Year}_{nollM}{from.Month}";
        var toSuffix = toFormat.Replace("-", "_");
        var period = isdaily ? "daily" : "monthly";

        newTableName = $"{parentTableName.ToLower()}_{fromSuffix}";

        return
            $@"CREATE TABLE IF NOT EXISTS {newTableName} PARTITION OF ""{parentTableName}"" FOR VALUES FROM ('{fromFormat}') TO ('{toFormat}');";
    }

    internal static string GetPartitionsScripts(string partitionedTableName, string schema = "public")
    {
        return @$"select json_agg(e) from(with recursive inh as (
   select i.inhrelid, cl.relname as parent,nsp.nspname as  parent_schema
   from pg_catalog.pg_inherits i
     join pg_catalog.pg_class cl on i.inhparent = cl.oid
     join pg_catalog.pg_namespace nsp on cl.relnamespace = nsp.oid
   where nsp.nspname = '{schema}'         
     and cl.relname = '{partitionedTableName}'  
     and cl.relkind='p'
   union all
   select i.inhrelid, (i.inhparent::regclass)::text,inh.parent_schema
   from inh
   join pg_catalog.pg_inherits i on (inh.inhrelid = i.inhparent)
)
select c.relname as tableName,
        pg_get_expr(c.relpartbound, c.oid, true) as partitionValues
from inh
   join pg_catalog.pg_class c on inh.inhrelid = c.oid
   join pg_catalog.pg_namespace n on c.relnamespace = n.oid
   left join pg_partitioned_table p on p.partrelid = c.oid
order by n.nspname, c.relname) e";
    }

    internal static string GetMinDateScript(string tableName, string partitionedColumnName)
    {
        return @$"SELECT Min(""{partitionedColumnName}"")::Date FROM {tableName} ";
    }


    internal static string GetMaxDateScript(string tableName, string partitionedColumnName)
    {
        return @$"SELECT Max(""{partitionedColumnName}"")::Date FROM {tableName} ";
    }


    internal static string DoScript() => " DO  $$";
    internal static string StartTransactionScript() => "BEGIN;";
    internal static string CommitTransactionScript() => "Commit;";
    internal static string StartBlockScript() => "BEGIN";
    internal static string EndBlockScript() => "END $$;";
    internal static string DeclareVariableScript(string name, string type) => $"DECLARE {name} {type};";
}