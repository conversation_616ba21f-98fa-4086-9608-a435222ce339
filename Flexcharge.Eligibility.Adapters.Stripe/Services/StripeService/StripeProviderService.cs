using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.Telemetry;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using Stripe;
using Forwarding = global::Stripe.Forwarding;

namespace FlexCharge.Eligibility.Adapters.Stripe.Services.StripeService;

class StripeProviderService : IStripeProviderService
{
    private ISecretsManager _secretsManager;

    public StripeProviderService(ISecretsManager secretsManager)
    {
        _secretsManager = secretsManager;
    }

    public async Task MarkInvoiceAsync(string accountId, string invoiceId, Dictionary<string, string> metaData)
    {
        using var workspan = Workspan.Start<StripeProviderService>();

        try
        {
            var invoiceService = new InvoiceService();
            var requestOptions = new RequestOptions
            {
                StripeAccount = accountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };

            var updateOptions = new InvoiceUpdateOptions
            {
                Metadata = metaData
            };

            await invoiceService.UpdateAsync(invoiceId, updateOptions, requestOptions);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "An error occurred while adding metadata to Stripe invoice");
            throw;
        }
    }

    public async Task<Charge> GetChargeAsync(string accountId, string chargeId)
    {
        using var workspan = Workspan.Start<StripeProviderService>();

        try
        {
            var chargeService = new ChargeService();
            var requestOptions = new RequestOptions
            {
                StripeAccount = accountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };

            var updateOptions = new ChargeGetOptions() { };

            var charge = await chargeService.GetAsync(chargeId, updateOptions, requestOptions);

            return charge;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "An error occurred while adding metadata to Stripe invoice");
            throw;
        }
    }

    public async Task RequestForwardApi(string accountId, string apiKey, string paymentMethod,
        string forwardApiEndpointUrl)
    {
        throw new NotImplementedException("Stripe Forward API is not implemented yet.");

        // var options = new Forwarding.RequestCreateOptions
        // {
        //     
        //     PaymentMethod = paymentMethod,
        //     Url = forwardApiEndpointUrl,
        //     Request = new Forwarding.RequestRequestOptions
        //     {
        //         Headers = new List<Forwarding.RequestRequestHeaderOptions>
        //         {
        //             // Add bearer???
        //             
        //             // new Forwarding.RequestRequestHeaderOptions
        //             // {
        //             //     Name = "Authorization",
        //             //     Value =
        //             //         "Bearer eyJhbGciOiJIUzI1NiJ9.Zm9yd2FyZGluZy1hcGktZGVtbw.2qoK37CNBmMjMDRERSYUSE-YrjsTgGhHnxMeqOxjrAg",
        //             // },
        //         },
        //         Body =
        //             "{\"metadata\":{\"reference\":\"Your Token Reference\"},\"card\":{\"number\":\"\",\"exp_month\":\"\",\"exp_year\":\"\",\"cvc\":\"\",\"name\":\"\"}}",
        //     },
        //     Replacements = new List<string>
        //     {
        //         "card_number",
        //         "card_expiry",
        //         "card_cvc",
        //         "cardholder_name",
        //     },
        // };
        //
        // var service = new Forwarding.RequestService();
        // await service.CreateAsync(options, new RequestOptions
        // {
        //     StripeAccount = accountId,
        //     ApiKey = apiKey
        // });
    }
}