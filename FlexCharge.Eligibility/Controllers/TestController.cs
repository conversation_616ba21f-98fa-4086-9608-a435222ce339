#if DEBUG
using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Sms;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.EligibilityChecks.Implementations.RuleEngine;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;
using FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.DisputeOptimization;
using FlexCharge.Eligibility.EligibilityWorkflows.DataMaps.Implementations.EligibilityStrategies;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.OrdersRecyclingEngine;
using FlexCharge.Eligibility.Services.ConsumerNotificationsService;
using FlexCharge.Eligibility.Services.OffSessionRetryService;
using FlexCharge.Eligibility.Services.PCSMServices;
using FlexCharge.Eligibility.Services.RecyclingEngineService;
using FlexCharge.Eligibility.Services.StripeReportsService;
using FlexCharge.Eligibility.Services.StripeServices;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using FlexCharge.Eligibility.Workflows.Tracing;
using FlexCharge.Grpc.Payments;
using FlexCharge.WorkflowEngine;
using FlexCharge.WorkflowEngine.Common.Services.WorkflowService;
using Microsoft.Extensions.Options;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Stripe;
using Stripe.Reporting;
using ActionResult = Microsoft.AspNetCore.Mvc.ActionResult;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;
using TransactionType = FlexCharge.Eligibility.Entities.Enums.TransactionType;

namespace FlexCharge.Eligibility.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ILogger _logger;
        private readonly IPublishEndpoint _publisher;
        private readonly IBigPayloadService _bigPayloadService;
        private readonly IRequestClient<AchVerifiedDebitCommand> _achDebitVerifiedRequest;
        private readonly ISmsServices _smsServices;
        private readonly IAmazonDynamoDB _dynamoDb;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly ReadOnlyPostgreSQLDbContext _readOnlyDbContext;
        private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
        private readonly IServiceProvider _serviceProvider;
        private readonly IActivityService _activityService;
        private readonly IUrlShortenerService _urlShortenerService;
        private readonly IConsumerNotificationsService _consumerNotificationsService;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IWorkflowService _workflowService;
        private readonly WorkflowPostgreSQLDbContext _workflowDbContext;
        private readonly IRecyclingEngineService _recyclingEngineService;

        private readonly FlexCharge.Grpc.Payments.GrpcGreeter.GrpcGreeterClient _greeterClient;
        //private readonly IProviderFactory _providerFactory;


        private readonly AppOptions _globalData;
        // private readonly KinesisService _kinesisService;
        // private readonly IRequestClient<DummyCommand> _dummyRequest;

        public TestController(ILogger<HealthController> logger, IOptions<AppOptions> globalData,
            IPublishEndpoint publisher,
            IBigPayloadService bigPayloadService, IRequestClient<AchVerifiedDebitCommand> achDebitVerifiedRequest,
            ISmsServices smsServices,
            IAmazonDynamoDB dynamoDb,
            PostgreSQLDbContext dbContext,
            ReadOnlyPostgreSQLDbContext readOnlyDbContext,
            IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
            IServiceProvider serviceProvider,
            IActivityService activityService,
            IUrlShortenerService urlShortenerService,
            IConsumerNotificationsService consumerNotificationsService,
            IServiceScopeFactory serviceScopeFactory,
            IWorkflowService workflowService,
            WorkflowPostgreSQLDbContext workflowDbContext,
            IRecyclingEngineService recyclingEngineService,
            FlexCharge.Grpc.Payments.GrpcGreeter.GrpcGreeterClient greeterClient
            //IProviderFactory providerFactory

            // KinesisService kinesisService, 
            // IRequestClient<DummyCommand> dummyRequest
        )
        {
            _logger = logger;
            _publisher = publisher;
            _bigPayloadService = bigPayloadService;
            _achDebitVerifiedRequest = achDebitVerifiedRequest;
            _smsServices = smsServices;
            _dynamoDb = dynamoDb;
            _dbContext = dbContext;
            _readOnlyDbContext = readOnlyDbContext;
            _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
            _serviceProvider = serviceProvider;
            _activityService = activityService;
            _urlShortenerService = urlShortenerService;
            _consumerNotificationsService = consumerNotificationsService;
            _serviceScopeFactory = serviceScopeFactory;
            _workflowService = workflowService;
            _workflowDbContext = workflowDbContext;
            _recyclingEngineService = recyclingEngineService;
            _greeterClient = greeterClient;
            //_providerFactory = providerFactory;
            // _kinesisService = kinesisService;
            // _dummyRequest = dummyRequest;
            _globalData = globalData.Value;
        }

        private Dictionary<string, string> _values = new Dictionary<string, string>();


        class TestPayload
        {
            public string Test { get; set; }
        }


        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Get()
        {
            if (!EnvironmentHelper.IsInStagingOrDevelopment) throw new NotSupportedException();

            using var workspan = Workspan.Start<TestController>();

            await TestEmailSendingAsync();

            //var result = GetDbContextsAndTheirEntityProperties();

            //await Send0UnicodeCharacterInActivity();

            //await TestSuppressingExpectedUniqueConstraintErrorsInDatabase();

            return Ok();
        }

        private async Task TestEmailSendingAsync()
        {
            await _consumerNotificationsService.SendOfferCustomerNotificationAsync(
                Guid.Parse("76e105a7-4d60-457b-82f2-648d46aba287"),
                Guid.Parse("66597cee-0ea6-4283-8696-8b31dc1fda59"),
                true
            );
        }

        private async Task TestSuppressingExpectedUniqueConstraintErrorsInDatabase()
        {
            Workspan workspan = Workspan.Start<TestController>();

            try
            {
                using var _ = DatabaseLogSuppressor
                    .SuppressUniqueConstraintError<PostgreSQLDbContext>("Orders", "IX_Orders_ConfirmationId");

                workspan.Log.Error("AAA");
                workspan.Log.Error(new Exception("BBB"), "CCC");

                workspan.Log.Information("AAA");
                workspan.Log.Information(new Exception("BBB"), "CCC");

                workspan.Log.Warning("AAA");
                workspan.Log.Warning(new Exception("BBB"), "CCC");

                workspan.Log.Fatal("AAA");
                workspan.Log.Fatal(new Exception("BBB"), "CCC");


                var anOrder = await _dbContext.Orders.FirstOrDefaultAsync(o => o.ConfirmationId != null);
                anOrder.Id = Guid.NewGuid();
                _dbContext.Orders.Add(anOrder);

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                //Console.WriteLine(e);
                //throw;
            }
        }

        private Dictionary<string, Dictionary<string, Dictionary<string, string>>>
            GetDbContextsAndTheirEntityProperties()
        {
            var result = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>();

            // Get the current assembly
            var assembly = Assembly.GetExecutingAssembly();

            // Get all types from the assembly that are subclasses of DbContext
            var dbContextTypes = assembly.GetTypes().Where(t => t.IsSubclassOf(typeof(DbContext)));

            foreach (var dbContextType in dbContextTypes)
            {
                DbContext dbContext = null;

                // Try to create an instance of the DbContext using a constructor with DbContextOptions parameter
                var optionsBuilder = new DbContextOptionsBuilder();
                //optionsBuilder.UseInMemoryDatabase(dbContextType.Name); // Use in-memory database for example
                var options = optionsBuilder.Options;

                var constructorWithDbContextOptions = dbContextType.GetConstructor(new[] {options.GetType()});
                if (constructorWithDbContextOptions != null)
                {
                    dbContext = (DbContext) Activator.CreateInstance(dbContextType, options);
                }

                // If that fails, try to create an instance using a parameterless constructor
                if (dbContext == null)
                {
                    var parameterlessConstructor = dbContextType.GetConstructor(Type.EmptyTypes);
                    if (parameterlessConstructor != null)
                    {
                        dbContext = (DbContext) Activator.CreateInstance(dbContextType);
                    }
                }

                if (dbContext == null)
                {
                    continue; // If we still couldn't create an instance, skip this DbContext type
                }

                // Get the entity properties for the DbContext
                var entityProperties = GetEntityProperties(dbContext);

                // Add the result to the dictionary
                result.Add(dbContextType.Name, entityProperties);
            }

            return result;
        }

        private Dictionary<string, Dictionary<string, string>> GetEntityProperties(DbContext context)
        {
            var result = new Dictionary<string, Dictionary<string, string>>();

            var entityTypes = context.Model.GetEntityTypes();

            foreach (var entityType in entityTypes)
            {
                var tableName = entityType.GetTableName();
                var properties = entityType.GetProperties()
                    .Where(p => p.GetColumnType() == "json" || p.GetColumnType() == "jsonb")
                    .ToDictionary(p => p.Name, p => p.GetColumnType());

                result.Add(tableName, properties);
            }

            return result;
        }

        private async Task Send0UnicodeCharacterInActivity()
        {
            // var badData = new TestPayload() {Test = "\\\\\u0000\\\\u0000\\\\u0000"};
            //
            // await _activityService.CreateActivityAsync(TransmitActivities.Transmit, set => set
            //     .Data(badData)
            //     .Meta(meta => meta
            //         .SetValue("Test", JsonConvert.SerializeObject(badData)))
            // );
            //
            // var anOrder = await _dbContext.Orders.LastAsync();
            // anOrder.Id = Guid.NewGuid();
            //
            // anOrder.Meta = new Dictionary<string, string>()
            // {
            //     {"Test", JsonConvert.SerializeObject(badData)}
            // };
            //
            // _dbContext.Orders.Add(anOrder);
            // await _dbContext.SaveChangesAsync();


            // await _smsServices.SendSms(new SmsRequest()
            // {
            //     Message = "Test sms checkout.flex-charge.com/nike?osk='036e7009-bc04-41bf-8410-6f9195e7c891'",
            //     PhoneNumber = "+************"
            // });

            // var achVerifiedDebitCommandParameters = new
            // {
            //     Mid = Guid.Parse("036e7009-bc04-41bf-8410-6f9195e7c891"),
            //     OwnerId = Guid.NewGuid(),
            //     PublicToken = publicKey,
            //     AccountId = accountId,
            //     AccountName = accountName,
            //     AccountType = accountType,
            //     InstitutionName = institutionName,
            //     OrderId = Guid.NewGuid(),
            //     Amount = 100,
            //     Currency = "USD",
            //     TransactionId = Guid.NewGuid(),
            // };
            //
            // var achVerifiedDebitCommandResponse =
            //     await _achDebitVerifiedRequest.GetResponse<AchVerifiedDebitCommandResponse>(
            //         achVerifiedDebitCommandParameters);

            //TestPayload testPayload = new TestPayload() {Test = "AAA"};

            // var bigPayload = await _bigPayloadService.CreateBigPayloadAsync(testPayload);
            //
            // var loadedPayload = await bigPayload.GetValueAsync(_bigPayloadService);

            // // await _publisher.Publish<MerchantCreatedEvent>(new
            // // {
            // //     TEST = 3.ToString()
            // // });
            //
            // // await _publisher.Publish<MerchantCreatedEvent>(new
            // // {
            // //    
            // //     TEST = 3
            // //     
            // // });
            //
            // // await _kinesisService.PutRecordAsync(new { m  = "abc"}, "activity-stream", "eligibility-ms");
            // //
            // using var workspan = Workspan.Start<TestController>();
            //
            // try
            // {
            //
            // workspan.Log.Information("DUMMY REQUEST SENT");
            //
            // // CancellationTokenSource source = new CancellationTokenSource();
            // // source.CancelAfter(5000);
            //
            // Response<DummyCommandResponse> dummyResponse = null;
            //
            // // await Task.Run(async () =>
            // //     {
            //         dummyResponse = await _dummyRequest
            //             .GetResponse<DummyCommandResponse>(new
            //             {
            //                 DummyLoad = "AAA"
            //             });
            // //     }
            // //     , source.Token
            // // );
            //
            // workspan.Log.Information("DUMMY RETURNED SUCCESSFULLY");
            // }
            // catch (Exception ex)
            // {
            //     workspan.RecordException(ex);
            //     throw;
            // }
        }


        [HttpGet("test-url-shortener")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestUrlShortener()
        {
            string url = "https://www.flex-charge.com/checkout/AA6152?osk=036e7009-bc04-41bf-8410-6f9195e7c891";

            var shortUrl = await _urlShortenerService.ShortenUrlAsync(url, DateTime.UtcNow.AddMinutes(5));

            var destinationUrl = await _urlShortenerService.GetDestinationUrlAsync(shortUrl.Path);
            var shortenedCureUrl = _urlShortenerService.GetShortUrlLink(shortUrl);


            Console.WriteLine();
            Console.WriteLine($"{shortenedCureUrl} => {destinationUrl}");
            Console.WriteLine();

            return Ok(shortenedCureUrl);
        }


        [HttpGet("test-rule-engine-retry-strategy")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> Get(string orderCreatedDay, int orderExpiryIntervalInDays, bool immediateRetry,
            int? executionHour, int? executionMinute)
        {
            _logger.LogInformation(
                $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");

            await TestRuleEngine_RetryStrategy(orderCreatedDay, orderExpiryIntervalInDays, immediateRetry,
                new TimeOnly(executionHour ?? 16, executionMinute ?? 0));

            return Ok();
        }

        private async Task TestRuleEngine_RetryStrategy(string orderCreatedDay, int orderExpiryIntervalInDays,
            bool immediateRetryEnabled,
            TimeOnly executionTime)
        {
            List<(string Code, string NormalizedCode, int RetriesExecuted)> codesToTry = new();

            string provider = "flexcharge_dummy";
            //codesToTry.Add(new("generic_decline", "47", 0));
            //codesToTry.Add(new("insufficient_funds", "51", 0));
            //codesToTry.Add(new("card_not_supported", "57", 0));
            //codesToTry.Add(new("card_velocity_exceeded", "61", 0));
            //codesToTry.Add(new("currency_not_supported", "62", 0));
            //codesToTry.Add(new("incorrect_pin", "55", 0));
            //codesToTry.Add(new("suspected_fraud_pickup_card", "34", 0));
            //codesToTry.Add(new("1555", "-1", 0));
            //codesToTry.Add(new("0", "-1", 0));
            //codesToTry.Add(new("", "-1", 0));
            //codesToTry.Add(new(" ", "-1", 0));
            codesToTry.Add(new("fraudulent", "59", 0));

            bool isMIT = true;

            // int orderExpiryIntervalInDays = 21;
            //DateTime orderCreatedOn = DateTime.Parse("2024-02-29T18:22:41.471Z");
            DateTime orderCreatedOn = DateTime.Parse(orderCreatedDay);


            StringBuilder results = new();

            results.AppendLine();
            results.AppendLine("=====================");

            results.AppendLine($"Expiry interval (days): {orderExpiryIntervalInDays}");
            results.AppendLine($"OrderCreatedOn: {orderCreatedOn}");
            results.AppendLine($"Provider: {provider}");
            results.AppendLine();

            var todayDay = 0;
            List<bool> authorizationByThisCodeAttempted = Enumerable.Repeat(false, codesToTry.Count).ToList();
            for (todayDay = 0; todayDay <= orderExpiryIntervalInDays + 1; todayDay++)
            {
                if (todayDay == 0 && immediateRetryEnabled)
                {
                    results.AppendLine("EXECUTE. IMMEDIATE RETRY AUTHORIZATION");
                    results.AppendLine();
                }

                await TestRuleEngine_RetryStrategy(orderCreatedOn, orderExpiryIntervalInDays, todayDay, codesToTry,
                    provider, isMIT,
                    results, authorizationByThisCodeAttempted, immediateRetryEnabled, executionTime);
            }

            results.AppendLine("=====================");
            results.AppendLine();

            foreach (var code in codesToTry)
            {
                results.AppendLine(
                    $"{code.Code} => ATTEMPTS: {code.RetriesExecuted + (immediateRetryEnabled ? 1 : 0)}");
            }

            results.AppendLine();
            results.AppendLine("#####################");


            Console.WriteLine(results.ToString());
        }

        private async Task TestRuleEngine_RetryStrategy(DateTime orderCreatedOn, int orderExpiryIntervalInDays,
            int todayDay,
            List<(string Code, string NormalizedCode, int RetriesExecuted)> codesToTry,
            string provider, bool isMIT, StringBuilder results, List<bool> authorizationByThisCodeAttempted,
            bool immediateRetryEnabled, TimeOnly executionTime)
        {
            using var workspan = Workspan.Start<TestController>();

            DateTime orderExpiryDate = orderCreatedOn + TimeSpan.FromDays(orderExpiryIntervalInDays);

            //var today = orderCreatedOn;
            var today = orderCreatedOn + TimeSpan.FromDays(todayDay);
            //var today = orderExpiryDate;
            // var today = DateTime.Parse("2024-01-29T18:22:41.471Z");

            results.AppendLine("-------------------------------");
            results.AppendLine($"OrderExpiry: {orderExpiryDate}");
            results.AppendLine($"Today: {today.AddHours(executionTime.Hour).AddMinutes(executionTime.Minute)}");
            results.AppendLine();

            //Merchant merchant = new Merchant();
            Merchant merchant =
                _dbContext.Merchants.First(x => x.Mid == Guid.Parse("76e105a7-4d60-457b-82f2-648d46aba287"));

            for (var i = 0; i < codesToTry.Count; i++)
            {
                var code = codesToTry[i];

                results.Append($"{code.Code} =>");
                try
                {
                    var utcNow = today.Date.AddHours(executionTime.Hour).AddMinutes(executionTime.Minute);

                    var order = Order.CreateTestOrder(isCit: false, orderCreatedOn, orderExpiryDate);
                    order.EvaluationCount =
                        code.RetriesExecuted +
                        (immediateRetryEnabled
                            ? 1
                            : 0); // starts from 0 as it's incremented during evaluation, not scheduling

                    if (authorizationByThisCodeAttempted[i] || immediateRetryEnabled)
                    {
                        await SimulateAuthorization((code.Code, code.NormalizedCode), order, merchant, provider,
                            orderExpiryDate, isCit: false);
                        results.Append($" MaxAuthorizationAttempts: {order.MaxAuthorizationAttempts}");
                    }

                    int daysSinceOrderFirstPlaced =
                        (int) Math.Truncate((today.Date - orderCreatedOn.Date).TotalDays);
                    // int? mitExpiryIntervalInHours =
                    //     (int) Math.Truncate((orderExpiryDate.Date - orderCreatedOn.Date).TotalHours);

                    // Stage4Request stage4Request = new()
                    // {
                    //     IsMIT = isMIT,
                    //     FirstDeclineDateDaysOld = (decimal) daysSinceOrderFirstPlaced,
                    //     ResponseCodeSource = provider,
                    //     ResponseCode = lastPaymentResponse.ResponseCode,
                    // };

                    if (today < orderExpiryDate)
                    {
                        var recyclingWorkflow =
                            await RecyclingWorkflowFactory.CreateWorkflowAsync(_activityService, merchant, order);

                        var workflowContext = new RecyclingWorkflowContext(merchant, order, today);
                        workflowContext.CurrentTimeUtc = today;

                        var valueStorage = new ValueStorage();

                        var workflow =
                            await recyclingWorkflow.Workflow.BuildAsync(recyclingWorkflow.Description, workflowContext,
                                valueStorage,
                                _serviceScopeFactory, merchant.WorkflowsExternalParameters);


                        // #region Serializing Workflow
                        //
                        // try
                        // {
                        //     using var serviceScope = _serviceScopeFactory.CreateScope();
                        //
                        //     // Important!!! Use scoped workflowDbContext for SerializeWorkflow() call to avoid problems
                        //     var workflowDbContext = serviceScope.ServiceProvider
                        //         .GetRequiredService<WorkflowPostgreSQLDbContext>();
                        //     var workflowService = serviceScope.ServiceProvider.GetRequiredService<IWorkflowService>();
                        //
                        //     await workflowService.SerializeWorkflow(workflow, recyclingWorkflow.Description,
                        //         workflowDbContext,
                        //         workflowDbContext.Workflows, true);
                        // }
                        // catch (Exception e)
                        // {
                        //     workspan.RecordFatalException(e, "Cannot serialize workflow");
                        // }
                        //
                        // #endregion

                        await recyclingWorkflow.Workflow.Run(recyclingWorkflow.Description, _serviceScopeFactory);

                        var trace = ((ITraceable) workflowContext).Tracer.GetTraceAsString();

                        var workflowResult = workflowContext.ExecutionResult;

                        bool executeRetry = workflowResult.ChargeRetryIntervalInDays > 0 &&
                                            daysSinceOrderFirstPlaced % workflowResult.ChargeRetryIntervalInDays == 0;

                        results.Append($", StopRepayments: {workflowResult.StopRepayments}");
                        results.Append($", ChargeRetryIntervalInDays: => {workflowResult.ChargeRetryIntervalInDays}");

                        if (workflowResult.StopRepayments)
                        {
                            results.Append($", STOP. Reason: {string.Join(',', workflowResult.Errors)}");
                        }
                        else if (executeRetry)
                        {
                            var recycleProcessingWindow = workflowResult.RecycleWindow;

                            var retryTimeForOrder = OffSessionRetrySchedulerService.GetRetryTimeForOrder(order, utcNow,
                                recycleProcessingWindow);

                            if (retryTimeForOrder.OrderExpiresBeforeRetry)
                            {
                                results.Append($", EXPIRED. Order expires before next retry window");
                            }
                            else
                            {
                                results.Append($", EXECUTE. Schedule time: {retryTimeForOrder.ScheduleTime}");
                            }

                            codesToTry[i] = (code.Code, code.NormalizedCode, code.RetriesExecuted + 1);
                            authorizationByThisCodeAttempted[i] = true;
                        }
                        else
                        {
                            results.Append($", SKIP");
                        }

                        // if (stage4Response.Errors?.Any() == true)
                        // {
                        //     results.Append(
                        //         $" [{string.Join(", ", stage4Response.Errors.Where(e => e.Error != null).Select(e => e.Error))}]");
                        // }
                    }
                    else
                    {
                        results.Append($", EXPIRED");
                    }
                }
                catch (Exception e)
                {
                    results.Append($" Error executing workflow");
                }


                results.AppendLine();
            }
        }

        async Task<Stage3Response> SimulateAuthorization((string Code, string NormalizedCode) code,
            Order order, Merchant merchant, string provider, DateTime? orderExpiryDate, bool isCit)
        {
            var mappedResponse = InternalResponseMapper.GetMappedResponse(code.NormalizedCode);

            var lastPaymentResponse = new PaymentTransactionResponse(
                DateTime.UtcNow,
                TransactionType.FullAuthorization,
                responseCodeGateway: provider,
                responseCodeProcessor: null,
                code.Code,
                "M",
                "M",
                usedGatewayOrder: 0,
                internalResponseCode: mappedResponse.MappedResponseCode,
                internalResponseMessage: mappedResponse.MappedResponseMessage,
                internalResponseGroup: mappedResponse.MappedResponseGroup.ToString());

            var errorCodeToRetryStrategyMapper = new FlexFactorErrorCodeToRecycleStrategyMapper();
            var retryStrategy =
                await errorCodeToRetryStrategyMapper.GetRecycleStrategyAsync(lastPaymentResponse
                    .NormalizedResponseCode);

            order.MaxAuthorizationAttempts = retryStrategy.MaxRetires;

            //var stage3Response = E0006_RuleEngineStage3.RuleEngineStage3(order, merchant, null,
            var ruleEngineStage3Block = new E0025_RuleEngineStage3();
            var stage3Response = await ruleEngineStage3Block.RuleEngineStage3Async(order, merchant, null,
                new Stage3Request()
                {
                    ResponseCode = lastPaymentResponse.ResponseCode,
                    AvsResultCode = lastPaymentResponse.AVS,
                    CvvResultCode = lastPaymentResponse.CVV,
                    CavvResultCode =
                        string.IsNullOrWhiteSpace(order.SCAAuthenticationToken) ? "N" : "Y",
                    IsFullyAuthorized = false,
                    ResponseCodeSource = provider,
                    IsRecurringPayments = true,
                    OrderExpiryDateTime = orderExpiryDate,
                    FraudProviderResponses = new Dictionary<string, string>()
                    {
                        {"SEON.Result", "-1"},
                        {"KOUNT.Result", "-1"},
                        {"SEON.Success", "False"},
                        {"KOUNT.Success", "False"}
                    },
                    BureauProviderResponses = new Dictionary<string, int>(),
                    ZeroVerificationResponseCode = null,
                    IsCustomerInitiatedTransaction = isCit
                },
                lastPaymentResponse,
                _serviceProvider
            );


            order.AddPaymentTransactionResponse(lastPaymentResponse);

            return stage3Response;
        }

        [HttpGet("test-rule-engine-stage3")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestRuleEngineStage4(bool isMit, string? normalizedResponseCode = null)
        {
            using var workspan = Workspan.Start<TestController>();

            _logger.LogInformation(
                $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");

            var code = ("0", "0");
            string provider = "flexcharge_dummy";

            var orderCreatedOn = DateTime.UtcNow;
            DateTime? orderExpiryDate = orderCreatedOn + TimeSpan.FromDays(21);

            ProviderNormalizedResponseToActionMap providerNormalizedResponseToActionMap = new();
            await providerNormalizedResponseToActionMap.LoadAsync();

            if (string.IsNullOrEmpty(normalizedResponseCode))
            {
                foreach (var responseCode in providerNormalizedResponseToActionMap.Map.Keys)
                {
                    await TestResponseCode(responseCode);
                }

                return Ok();
            }
            else
            {
                return Ok(await TestResponseCode(normalizedResponseCode));
            }

            async Task<object> TestResponseCode(string responseCode)
            {
                if (providerNormalizedResponseToActionMap.Map.TryGetValue(responseCode, out var action))
                {
                    var order = Order.CreateTestOrder(isCit: orderExpiryDate == null, orderCreatedOn, orderExpiryDate);

                    Merchant merchant =
                        _dbContext.Merchants.First(x => x.Mid == Guid.Parse("76e105a7-4d60-457b-82f2-648d46aba287"));

                    var stage3Response = await SimulateAuthorization(("XXX", responseCode), order, merchant, provider,
                        orderExpiryDate, isMit == false);

                    var settings = new JsonSerializerSettings
                    {
                        Converters = {new StringEnumConverter()},
                        Formatting = Formatting.Indented
                    };

                    if (!isMit)
                    {
                        switch (action.CitAction)
                        {
                            case CitAction.Approve:
                                Ensure(responseCode, stage3Response,
                                    x => x.OrderState == OrderState.ELIGIBILITY_STAGE3_PASSED);
                                break;
                            case CitAction.DoNotTryAgain:
                                Ensure(responseCode, stage3Response, x => x.OrderState == OrderState.NOT_ELIGIBLE);
                                break;
                            case CitAction.Cascade:
                                Ensure(responseCode, stage3Response,
                                    x =>
                                        x.OrderState == OrderState.CONDITIONAL_INTERNAL &&
                                        x.CureSet.Any());
                                break;
                            default:
                                throw new ArgumentOutOfRangeException();
                        }
                    }
                    else
                    {
                        switch (action.MitAction)
                        {
                            case MitAction.Approve:
                                Ensure(responseCode, stage3Response,
                                    x => x.OrderState == OrderState.ELIGIBILITY_STAGE3_PASSED);
                                break;
                            case MitAction.DoNotTryAgain:
                                Ensure(responseCode, stage3Response,
                                    x => x.OrderState == OrderState.NOT_ELIGIBLE
                                         && x.StopRetries == true);
                                break;
                            case MitAction.Cascade:
                                Ensure(responseCode, stage3Response, x =>
                                    x.OrderState == OrderState.CONDITIONAL_INTERNAL &&
                                    x.CureSet.Any() &&
                                    x.StopRetries == false);
                                break;
                            // case MitAction.TryAgainLater:
                            //     Ensure(responseCode, stage3Response, x =>
                            //         x.OrderState == OrderState.NOT_ELIGIBLE &&
                            //         x.StopRetries == false);
                            //     break;
                            default:
                                throw new ArgumentOutOfRangeException();
                        }
                    }

                    return JsonConvert.SerializeObject(stage3Response, settings);

                    void Ensure(string normalizedCode, Stage3Response response, Func<Stage3Response, bool> condition)
                    {
                        if (!condition(response) &&
                            normalizedCode != "0") // Approved order can be declined by after auth conditions
                        {
                            workspan.Log.Fatal("Error in response: {Response} for {NormalizedCode}",
                                JsonConvert.SerializeObject(response, settings), normalizedCode);
                        }
                    }
                }
                else
                {
                    workspan.Log
                        .Fatal("No action found for normalized response code {ResponseCode}", responseCode);

                    return null;
                }
            }
        }

        [HttpPost("force-mit-off-session-retry")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> ForceMitOffSessionRetry(Guid orderId)
        {
            var workspan = Workspan.StartEndpoint<TestController>(this, null, _globalData);

            try
            {
                await _publisher.Publish(new RetryOffSessionOfferCommand(orderId)
                {
                    ForceRetry = true
                });
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
            }

            return Ok();
        }

        [HttpGet("perform-luhn-check")]
        public async Task PerformLuhnCheck(string cardNumber)
        {
            // code to perform Luhn check inline

            bool PassesLuhnTest(string cardNumber)
            {
                //Clean the card number- remove dashes and spaces
                cardNumber = cardNumber.Replace("-", "").Replace(" ", "");
                //Convert card number into digits array
                int[] digits = new int[cardNumber.Length];
                for (int len = 0; len < cardNumber.Length; len++)
                {
                    digits[len] = Int32.Parse(cardNumber.Substring(len, 1));
                }

                //Luhn Algorithm
                //Adapted from code availabe on Wikipedia at
                //http://en.wikipedia.org/wiki/Luhn_algorithm
                int sum = 0;
                bool alt = false;
                for (int i = digits.Length - 1; i >= 0; i--)
                {
                    int curDigit = digits[i];
                    if (alt)
                    {
                        curDigit *= 2;
                        if (curDigit > 9)
                        {
                            curDigit -= 9;
                        }
                    }

                    sum += curDigit;
                    alt = !alt;
                }

                //If Mod 10 equals 0, the number is good and this will return true
                return sum % 10 == 0;
            }

            Console.WriteLine(PassesLuhnTest(cardNumber));
        }

        [HttpPost("test-activities")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestActivities()
        {
            await _activityService.CreateActivityAsync(EligibilityActivities.BinCheck_Failed);

            //await _activityService.CreateActivityAsync(EligibilityActivities.OrderAmountIsTooLow_Decline);


            return Ok();
        }

        [HttpGet("test-dispute-optimization")]
        public async Task TestDisputeOptimization()
        {
            var issuerPartToIssuerMapDataMapMapDataMapList = new IssuerPartToIssuerMapValidatableDataMapList();
            await issuerPartToIssuerMapDataMapMapDataMapList.LoadAsync();

            return; //!!!

            // var order = await _dbContext.Orders.SingleAsync(x =>
            //     x.Id == new Guid("510eb6c4-9a54-4a45-bb81-9e81cda5f0f1"));
            // var merchant = await _dbContext.Merchants.SingleAsync(x => x.Mid == order.Mid);
            // var site = await _dbContext.Sites.FirstAsync(x => x.Mid == merchant.Mid);
            //
            // order.State = nameof(OrderState.ORDER_INITIATED);
            //
            // EligibilityCheckContext context = new(merchant, site,
            //     order, new EvaluateRequest()
            //     {
            //         PaymentMethod = new()
            //         {
            //             CardBinNumber = "411111"
            //         }
            //     },
            //     DataChangeFlags.All,
            //     isReEvaluation: false,
            //     isOffSessionRetry: false,
            //     null,
            //     UserExperienceFlowType.Frictionless,
            //     null,
            //     null);
            //
            // ValueStorage valueStorage = new();
            //
            //
            // E1001_BinCheck binCheck = new();
            // binCheck.Initialize(context, _serviceScopeFactory, valueStorage);
            // await binCheck.ExecuteAsync();
            //
            // E2013_BlockByDisputeOptimization blockByDisputeOptimization = new();
            // blockByDisputeOptimization.Initialize(context, _serviceScopeFactory, valueStorage);
            // await blockByDisputeOptimization.ExecuteAsync();
        }


        #region WORKFLOWS

        [HttpGet("test-workflow-builder")]
        public async Task TestWorkflowBuilder()
        {
            var order = await _dbContext.Orders.SingleAsync(o =>
                o.Id == Guid.Parse("a8f4ed34-c73c-404b-823c-6b21c130b689"));
            var merchant = await _dbContext.Merchants.SingleAsync(m => m.Mid == order.Mid);

            //order.IsCIT = true;

            int i = 1000;
            //for (i = 0; i < 1000; i += 10)
            {
                order.ExpiryDate = DateTime.Now.AddHours(i);
                order.MaxAuthorizationAttempts = 3;
                order.EvaluationCount = 0;


                var recyclingWorkflow =
                    await RecyclingWorkflowFactory.CreateWorkflowAsync(_activityService, merchant, order);
                //var recyclingWorkflow = await RecyclingWorkflowFactory.CreateTestWorkflowAsync();

                var workflowContext = new RecyclingWorkflowContext(merchant, order, DateTime.UtcNow);
                var valueStorage = new ValueStorage();

                var workflow =
                    await recyclingWorkflow.Workflow.BuildAsync(recyclingWorkflow.Description, workflowContext,
                        valueStorage, _serviceScopeFactory,
                        merchant.WorkflowsExternalParameters);

                // if (!EnvironmentHelper.IsInProduction)
                // {
                //     // TEST CODE to create and store workflow definition in DB
                //     var workflowDefinition = WorkflowDefinition.CreateWorkflowDefinition(workflow);
                //     Guid recycleEngineWorkflowId = new("6a27db7e-2103-43d7-baf9-8f5e99911e71");
                //
                //     var workflowDefinitionSerializationSettings = new JsonSerializerSettings
                //     {
                //         NullValueHandling = NullValueHandling.Ignore,
                //         Converters = new List<JsonConverter>()
                //         {
                //             // Convert enums values to strings
                //             new StringEnumConverter()
                //         }
                //     };
                //
                //     using var serviceScope = _serviceScopeFactory.CreateScope();
                //     var workflowDbContext =
                //         serviceScope.ServiceProvider.GetRequiredService<WorkflowPostgreSQLDbContext>();
                //     
                //     workflowDbContext.Workflows.SingleOrDefault(w => w.WorkflowId == recycleEngineWorkflowId && w.Version == workflow.Version);
                //
                //     workflowDbContext.Workflows.Update(new WorkflowEngine.Entities.Workflow()
                //     {
                //         Name = "Recycle Strategy 1",
                //         WorkflowId = recycleEngineWorkflowId,
                //         IsReadonly = true,
                //         Definition =
                //             JsonConvert.SerializeObject(workflowDefinition, workflowDefinitionSerializationSettings),
                //     });
                //
                //     await workflowDbContext.SaveChangesAsync();
                // }

                await recyclingWorkflow.Workflow.Run(recyclingWorkflow.Description, _serviceScopeFactory);

                long a = 0;

                ThreadPool.SetMaxThreads(30, 5);

                for (int j = 0; j < 10; j++)
                {
                    List<Func<Task>> taskLambdas = new();
                    for (int t = 0; t < 1; t++)
                    {
                        Func<Task> asyncLambda = async () =>
                        {
                            await Task.Delay(1000);
                            Interlocked.Increment(ref a);

                            using var serviceScope = _serviceScopeFactory.CreateScope();
                            var workflowService = serviceScope.ServiceProvider.GetRequiredService<IWorkflowService>();
                            var workflowDbContext =
                                serviceScope.ServiceProvider.GetRequiredService<WorkflowPostgreSQLDbContext>();

                            await workflowService.SerializeWorkflow(workflow, recyclingWorkflow.Description,
                                workflowDbContext,
                                workflowDbContext.Workflows, true);

                            using var serviceScope2 = _serviceScopeFactory.CreateScope();
                            var workflowService2 = serviceScope.ServiceProvider.GetRequiredService<IWorkflowService>();
                            var workflowDbContext2 =
                                serviceScope2.ServiceProvider.GetRequiredService<WorkflowPostgreSQLDbContext>();

                            await workflowService2.SerializeWorkflow(workflow, recyclingWorkflow.Description,
                                workflowDbContext2,
                                workflowDbContext2.Workflows, true);


                            Console.WriteLine($"{a}");
                            Interlocked.Decrement(ref a);
                        };

                        taskLambdas.Add(asyncLambda);
                    }

                    await Task.WhenAll(taskLambdas.Select(t => t()).ToArray());
                }


                Console.WriteLine($"{i}: {JsonConvert.SerializeObject(workflowContext.ExecutionResult)}");
            }
        }

        #endregion

        [HttpPost("test-readonly-order-properties")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestReadonlyOrderProperties()
        {
            var order = _dbContext.Orders.Single(o => o.Id == Guid.Parse("0e28ae5a-d4e6-48cd-b71a-2349da1ef572"));
            order.SetNotes("AAA");
            await _dbContext.SaveChangesAsync();

            return Ok();
        }

        // [HttpPost("test-provider-services")]
        // [ProducesResponseType(200)]
        // public async Task<ActionResult> TestProviderServices()
        // {
        //     var provider =
        //         _providerFactory.CreateProvider(FlexCharge.Eligibility.Adapters.Stripe.ProviderDescription.ProviderId);
        //
        //     var service = provider.GetService<IUpdatePaymentInstrumentService>();
        //
        //     return Ok();
        // }

        [HttpPost("test-provider-to-action-map")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestProviderToActionMap(string responseCode)
        {
            ProviderNormalizedResponseToActionMap providerNormalizedResponseToActionMap = new();

            await providerNormalizedResponseToActionMap.LoadAsync();

            if (providerNormalizedResponseToActionMap.Map.TryGetValue(responseCode, out var action))
            {
                var serializerSettings = new JsonSerializerSettings
                {
                    Converters = {new StringEnumConverter()},
                    Formatting = Formatting.Indented
                };

                return Ok(JsonConvert.SerializeObject(action, serializerSettings));
            }

            return NotFound();
        }

        [HttpPost("test-stripe-reports")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestStripeDownloadReports(Guid mid, string accountId, string? reportId)
        {
            var stripeReportsService = _serviceProvider.GetRequiredService<IStripeReportsService>();

            await stripeReportsService.InitiateReportDownloadingAsync(mid, accountId, reportId);

            return Ok();
        }

        [HttpPost("test-stripe-create-orders")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestStripeCreateOrders()
        {
            // var service = _serviceProvider.GetRequiredService<IStripeSubscriptionsService>();
            //
            // var failedInvoices = await service.GetFailedInvoicesForActiveSubscriptionsAsync(
            //     new Guid("0195d230-b421-71df-9d3c-c8a52fbb94ec"), "acct_1Qw3ibFtucC0nsFo");
            //
            // return Ok(failedInvoices);


            await _publisher.RunIdempotentCommandWithoutResponseAsync(new PaymentProviderSynchronizeOrdersCommand(
                new Guid("0195d230-b421-71df-9d3c-c8a52fbb94ec"), "Stripe", "acct_1Qw3ibFtucC0nsFo"));

            return Ok();
        }

        [HttpPost("test-stripe-get-legal-entity-information")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestStripeGetLegalEntityInformation(string accountId)
        {
            var stripeReportsService = _serviceProvider.GetRequiredService<IStripeMerchantInformationService>();

            await stripeReportsService.GetMerchantAccountInformationAsync(
                new Guid("76e105a7-4d60-457b-82f2-648d46aba287"), accountId);

            return Ok();
        }

        [HttpPost("test-grpc")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestGrpc()
        {
            await _greeterClient.SayHelloAsync(new HelloRequest()
            {
                Name = "AAA"
            });

            return Ok();
        }


        [HttpPost("test-stripe-report-parameters")]
        [ProducesResponseType(200)]
        public async Task<ActionResult> TestStripeReportParameters(string? accountId, string stripeApiKey,
            string reportId)
        {
            using var workspan = Workspan.Start<StripeReportsService>();

            var stripeRequestOptions = new RequestOptions()
            {
                StripeAccount = accountId,
                ApiKey = stripeApiKey
            };

            var reportService = new ReportTypeService();
            var reportTypes = await reportService.ListAsync(
                requestOptions: stripeRequestOptions);

            var reportRunService = new ReportRunService();

            // HashSet<string> reportsToDownload = new HashSet<string>()
            // {
            //     "balance.summary.1"
            // };

            // Group reports by their base name (removing version numbers)
            var latestReportVersions = reportTypes
                .Where(x => !x.Id.StartsWith("connected"))
                // Group by the base report name (everything before the last dot)
                .GroupBy(x =>
                {
                    int lastDotIndex = x.Id.LastIndexOf('.');
                    // Check if there's a digit after the last dot
                    if (lastDotIndex > 0 && lastDotIndex < x.Id.Length - 1 && char.IsDigit(x.Id[lastDotIndex + 1]))
                    {
                        return x.Id.Substring(0, lastDotIndex);
                    }

                    // If there's no version number, use the full ID
                    return x.Id;
                })
                // For each group, select the report with the highest version number
                .Select(group =>
                {
                    return group
                        .OrderByDescending(report =>
                        {
                            int lastDotIndex = report.Id.LastIndexOf('.');
                            if (lastDotIndex > 0 && lastDotIndex < report.Id.Length - 1)
                            {
                                string versionPart = report.Id.Substring(lastDotIndex + 1);
                                // Try to parse the version number
                                if (int.TryParse(versionPart, out int version))
                                {
                                    return version;
                                }
                            }

                            return 0; // Default version if no version number found
                        })
                        .First(); // Take the one with the highest version number
                })
                .ToList();


            foreach (var reportType in latestReportVersions)
            {
                if (reportType.Id != reportId)
                    continue; //!!!

                workspan.Log.Information($"Requesting report: {reportType.Id}");

                try
                {
                    StringBuilder reportTimeInterval = new StringBuilder();


                    var reportParameterOptions = new ReportRunParametersOptions()
                    {
                        ConnectedAccount = accountId,
                    };

                    // If it's UnixEpoch - the IntervalStart parameter is not available
                    if (reportType.DataAvailableStart > DateTime.UnixEpoch)
                    {
                        reportParameterOptions.IntervalStart = DateTime.UtcNow.AddMonths(-3);

                        if (reportType.DataAvailableStart > reportParameterOptions.IntervalStart)
                            reportParameterOptions.IntervalStart = reportType.DataAvailableStart;
                    }

                    // If it's UnixEpoch - the IntervalEnd parameter is not available
                    if (reportType.DataAvailableEnd > DateTime.UnixEpoch)
                    {
                        reportParameterOptions.IntervalEnd = DateTime.UtcNow;

                        if (reportType.DataAvailableEnd < reportParameterOptions.IntervalEnd)
                            reportParameterOptions.IntervalEnd = reportType.DataAvailableEnd;
                    }

                    if (reportType.Id.StartsWith("payins_insights.accdash."))
                    {
                        //reportParameterOptions.Payout = "issuing_transaction";
                    }


                    reportTimeInterval.Append(reportParameterOptions.IntervalStart?.ToString("yyyy-MM-dd"));
                    reportTimeInterval.Append(" - ");
                    reportTimeInterval.Append(reportParameterOptions.IntervalEnd?.ToString("yyyy-MM-dd"));

                    var reportRun = await reportRunService.CreateAsync(new ReportRunCreateOptions
                        {
                            ReportType = reportType.Id,
                            Parameters = reportParameterOptions
                        },
                        requestOptions: stripeRequestOptions);

                    workspan.Log.Information(
                        "Requested report: {ReportTypeId}, Status: {ReportRunStatus}, RunId: {ReportRunId}",
                        reportType.Id, reportRun.Status, reportRun.Id);
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e, "Failed to create Stripe report");
                }
            }

            return Ok();
        }
    }
}
#endif