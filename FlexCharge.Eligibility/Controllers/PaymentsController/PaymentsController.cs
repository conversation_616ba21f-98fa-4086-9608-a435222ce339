using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services;
using FlexCharge.Eligibility.Services.DataNormalization;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.SessionMatcherService;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Controllers;

[Route("")]
[ApiController]
[Authorize(AuthenticationSchemes = AuthenticationSchemas.BasicAuthentication)]
public partial class PaymentsController : BaseController
{
    private readonly ITransmitAndEvaluateService _transmitAndEvaluateService;
    private readonly IActivityService _activityService;
    private readonly IEligibilityService _eligibilityService;
    private readonly AppOptions _globalData;
    private readonly IMapper _mapper;

    public PaymentsController(
        IOptions<AppOptions> globalData,
        ITransmitAndEvaluateService transmitAndEvaluateService,
        IEligibilityService eligibilityService,
        IMapper mapper,
        IActivityService activityService
    )
    {
        _transmitAndEvaluateService = transmitAndEvaluateService;
        _eligibilityService = eligibilityService;
        _mapper = mapper;
        _activityService = activityService;
        _globalData = globalData.Value;
    }


    [HttpPost("sale")]
    [SensitiveDataPublicApiTelemetry(typeof(EvaluateRequest), JwtTenantIdClaim = MyClaimTypes.MERCHANT_ID)]
    [ProducesResponseType(typeof(SaleResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> SalePost(EvaluateRequest request, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.StartEndpoint<PaymentsController>(this, request, _globalData)
            .Request(request);

        var mid = GetMID();
        var pid = GetPID();
        Guid orderToCreateId = Guid.NewGuid();

        workspan
            .Baggage("Mid", mid)
            .Baggage("Pid", pid)
            .Baggage("OrderId", orderToCreateId);

        try
        {
            request = await _transmitAndEvaluateService.OverrideEvaluateRequestByRequestInExtraDataAsync(request,
                orderToCreateId, ModelState);

            var requestValidationResult = await _transmitAndEvaluateService
                .ValidateEvaluateRequestAsync(request, mid, pid, orderToCreateId);

            if (requestValidationResult.IsValid == false)
            {
                return ValidationProblem();
            }

            if (request.PaymentMethod?.Token == false)
            {
                await _eligibilityService.TokenizePaymentInstrumentAsync(request, orderToCreateId,
                    requestValidationResult.Merchant.Mid, cancellationToken);
            }

            var result = await _eligibilityService.RunEvaluateRequestAsync(request, pid,
                orderToCreateId, EvaluateProcessingType.Synchronous, requestType: EvaluateRequestType.SALE,
                cancellationToken: cancellationToken, isInternalEvaluation: false);

            var evaluateResponse = result.EvaluateResponse;
            evaluateResponse.OrderSessionKey = orderToCreateId;
            evaluateResponse.OrderId = evaluateResponse.OrderSessionKey;
            //evaluateResponse.AdditionalFields = request.AdditionalFields;

            var saleResponse = _mapper.Map<SaleResponse>(evaluateResponse);
            saleResponse.OrderId = orderToCreateId;

            if (saleResponse.Status != nameof(EligibilityStatusCodes.APPROVED) &&
                saleResponse.Status != nameof(EligibilityStatusCodes.CHALLENGE))
            {
                saleResponse.Status = nameof(EligibilityStatusCodes.DECLINED);
                // We only return response codes for declined requests
                saleResponse.ResponseCode = result.ResponseCode;
                saleResponse.ResponseMessage = result.ResponseMessage;
            }

            return Ok(saleResponse);
        }
        catch (Exception e)
        {
            workspan.RecordEndpointCriticalApiError(e);
            await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                set => set
                    .TenantId(request.Mid)
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request)))
            );

            return NotEligible<SaleResponse>(true);
        }
    }

    protected IActionResult NotEligible<T>(bool failed)
        where T : BaseResponse, new()
    {
        var sanitizedResponse = new T()
        {
            Status = nameof(EligibilityStatusCodes.DECLINED),
            Result = failed ? nameof(EligibilityResultCodes.Success) : nameof(EligibilityResultCodes.Failed)
        };

        // we don't want to return internal errors to client side and partner servers
        if (failed)
        {
            return ReturnResponse(sanitizedResponse);
        }

        return Ok(sanitizedResponse);
    }
}