// namespace FlexCharge.Eligibility.Entities.Enums;
//
// /// <summary>
// /// 
// /// </summary>
// /// <remarks>
// /// Lower number - higher access level
// /// E.g. SUPER_ADMIN should go before MERCHANT_ADMIN as he can view and edit MERCHANT_ADMIN's data
// /// </remarks>
// public enum RiskManagementListItemCreatedByRole
// {
//     SUPER_ADMIN = 10,
//     SYSTEM = 20,
//     MERCHANT_ADMIN = 30
// }