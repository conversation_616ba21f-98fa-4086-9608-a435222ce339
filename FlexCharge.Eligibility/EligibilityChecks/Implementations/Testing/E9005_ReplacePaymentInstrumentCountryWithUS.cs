using System.Threading.Tasks;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.Testing;

public class E9005_ReplacePaymentInstrumentCountryWithUS : EligibilityCheckBase
{
    public override bool IsProductionBlock => false;

    protected override async Task ExecuteBlockAsync()
    {
        if (OrderPayload.PaymentMethod.CardCountry != "US")
        {
            OrderPayload.PaymentMethod.CardCountry = "US";
        }
    }
}