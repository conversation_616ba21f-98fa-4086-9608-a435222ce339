using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Cloud.SecretsManager;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Commands;
using Flexcharge.Eligibility.StripeAdapter.Helpers;
using FlexCharge.Utils;
using MassTransit;
using Stripe;
using Stripe.Reporting;

namespace FlexCharge.Eligibility.Services.StripeReportsService;

public interface IStripeReportsService
{
    Task InitiateReportDownloadingAsync(Guid mid, string accountId, string? reportIdOrPrefix = null);
    Task<bool> TryToDownloadReportAsync(Guid mid, string accountId, string reportName, string reportRunId);
}

public class StripeReportsService : IStripeReportsService
{
    private readonly ISecretsManager _secretsManager;
    private readonly IPublishEndpoint _publisher;

    public StripeReportsService(ISecretsManager secretsManager, IPublishEndpoint publisher,
        IBigPayloadService bigPayloadService, IHttpClientFactory httpClientFactory)
    {
        _secretsManager = secretsManager;
        _publisher = publisher;
    }

    public async Task InitiateReportDownloadingAsync(Guid mid, string accountId, string? reportIdOrPrefix = null)
    {
        using var workspan = Workspan.Start<StripeReportsService>();

        try
        {
            var stripeRequestOptions = new RequestOptions()
            {
                StripeAccount = accountId,
                ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
            };

            var reportService = new ReportTypeService();
            var reportTypes = await reportService.ListAsync(
                requestOptions: stripeRequestOptions);

            var reportRunService = new ReportRunService();

            // HashSet<string> reportsToDownload = new HashSet<string>()
            // {
            //     "balance.summary.1"
            // };

            // Group reports by their base name (removing version numbers)
            var latestReportVersions = reportTypes
                .Where(x => !x.Id.StartsWith("connected"))
                // Group by the base report name (everything before the last dot)
                .GroupBy(x =>
                {
                    int lastDotIndex = x.Id.LastIndexOf('.');
                    // Check if there's a digit after the last dot
                    if (lastDotIndex > 0 && lastDotIndex < x.Id.Length - 1 && char.IsDigit(x.Id[lastDotIndex + 1]))
                    {
                        return x.Id.Substring(0, lastDotIndex);
                    }

                    // If there's no version number, use the full ID
                    return x.Id;
                })
                // For each group, select the report with the highest version number
                .Select(group =>
                {
                    return group
                        .OrderByDescending(report =>
                        {
                            int lastDotIndex = report.Id.LastIndexOf('.');
                            if (lastDotIndex > 0 && lastDotIndex < report.Id.Length - 1)
                            {
                                string versionPart = report.Id.Substring(lastDotIndex + 1);
                                // Try to parse the version number
                                if (int.TryParse(versionPart, out int version))
                                {
                                    return version;
                                }
                            }

                            return 0; // Default version if no version number found
                        })
                        .First(); // Take the one with the highest version number
                })
                .ToList();


            foreach (var reportType in latestReportVersions)
            {
                if (reportIdOrPrefix != null && !reportType.Id.StartsWith(reportIdOrPrefix) &&
                    reportType.Id != reportIdOrPrefix)
                {
                    workspan.Log.Information($"Skipping report: {reportType.Id}");
                    continue; //!!!
                }

                workspan.Log.Information($"Requesting report: {reportType.Id}");

                await RequestReportFromStripeAsync(mid, accountId, reportRunService, reportType, stripeRequestOptions);
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    private async Task RequestReportFromStripeAsync(Guid mid, string accountId, ReportRunService reportRunService,
        ReportType reportType, RequestOptions stripeRequestOptions)
    {
        using var workspan = Workspan.Start<StripeReportsService>()
            .Baggage("Mid", mid)
            .Baggage("AccountId", accountId)
            .Baggage("ReportId", reportType.Id)
            .Baggage("ReportName", reportType.Name);

        try
        {
            StringBuilder reportTimeInterval = new StringBuilder();


            var reportParameterOptions = new ReportRunParametersOptions()
            {
            };

            // If it's UnixEpoch - the IntervalStart parameter is not available
            if (reportType.DataAvailableStart > DateTime.UnixEpoch)
            {
                reportParameterOptions.IntervalStart = DateTime.UtcNow.AddMonths(-3);

                if (reportType.DataAvailableStart > reportParameterOptions.IntervalStart)
                    reportParameterOptions.IntervalStart = reportType.DataAvailableStart;
            }

            // If it's UnixEpoch - the IntervalEnd parameter is not available
            if (reportType.DataAvailableEnd > DateTime.UnixEpoch)
            {
                reportParameterOptions.IntervalEnd = DateTime.UtcNow;

                if (reportType.DataAvailableEnd < reportParameterOptions.IntervalEnd)
                    reportParameterOptions.IntervalEnd = reportType.DataAvailableEnd;
            }


            reportTimeInterval.Append(reportParameterOptions.IntervalStart?.ToString("yyyy-MM-dd"));
            reportTimeInterval.Append(" - ");
            reportTimeInterval.Append(reportParameterOptions.IntervalEnd?.ToString("yyyy-MM-dd"));

            var reportRun = await reportRunService.CreateAsync(new ReportRunCreateOptions
                {
                    ReportType = reportType.Id,
                    Parameters = reportParameterOptions
                },
                requestOptions: stripeRequestOptions);

            await _publisher.RunIdempotentCommandWithoutResponseAsync(
                new PaymentProviderDownloadProcessingReportCommand(
                    mid, "Stripe", accountId, $"{reportType.Name} {reportTimeInterval}", reportRun.Id)
                {
                    ReportRunTime = DateTime.UtcNow
                });

            workspan.Log.Information("Requested report: {ReportTypeId}, Status: {ReportRunStatus}",
                reportType.Id, reportRun.Status);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Failed to create Stripe report");
        }
    }

    public async Task<bool> TryToDownloadReportAsync(Guid mid, string accountId, string reportName,
        string reportRunId)
    {
        using var workspan = Workspan.Start<StripeReportsService>();

        var stripeRequestOptions = new RequestOptions()
        {
            StripeAccount = accountId,
            ApiKey = await StripeInitializationHelper.GetStripeApiKeyAsync(_secretsManager)
        };

        var reportRunService = new ReportRunService();
        var fileService = new FileService();

        try
        {
            var updatedReportRun =
                await reportRunService.GetAsync(reportRunId, requestOptions: stripeRequestOptions);

            if (updatedReportRun.Status == "succeeded")
            {
                var file = await fileService.GetAsync(updatedReportRun.Result.Id,
                    requestOptions: stripeRequestOptions);

                var fileUrl = file.Url;

                workspan
                    .Tag("ReportName", reportName)
                    .Tag("FileName", file.Filename)
                    .Tag("Url", fileUrl)
                    .Tag("Id", file.Id)
                    .Tag("Created", file.Created)
                    .Tag("ExpiresAt", file.ExpiresAt)
                    .Tag("Title", file.Title)
                    .Tag("Type", file.Type)
                    .Tag("Size", file.Size)
                    .Tag("Purpose", file.Purpose)
                    .Log.Information("Stripe report is ready");

                string fileName = FileNameHelpers.MakeFilenameFriendly(reportName) +
                                  $" {DateTime.UtcNow.ToString("yy-MM-dd")}" + "." + file.Type;

                workspan.Log.Information("Stripe report is ready, publishing download command");

                await _publisher.Publish(
                    new DownloadAndStoreMerchantDocumentCommand(mid, fileName, GetFileContentType(file.Type), fileUrl)
                    {
                        Bearer = stripeRequestOptions.ApiKey,
                        Headers = new Dictionary<string, string>()
                        {
                            {"Stripe-Account", stripeRequestOptions.StripeAccount},
                        }
                    });

                string GetFileContentType(string fileType)
                {
                    return fileType switch
                    {
                        "txt" => "text/plain",
                        "csv" => "text/csv",
                        "pdf" => "application/pdf",
                        _ => "application/octet-stream"
                    };
                }

                return true;
            }
            else
            {
                workspan.Log.Information("Stripe report is not ready yet");
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }

        return false;
    }
}