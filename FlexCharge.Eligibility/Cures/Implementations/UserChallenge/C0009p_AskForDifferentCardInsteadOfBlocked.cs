// using System;
// using System.Collections.Generic;
// using System.Threading.Tasks;
// using FlexCharge.Eligibility.DTO;
// using FlexCharge.Eligibility.Entities;
// using FlexCharge.Eligibility.Services;
// using FlexCharge.Common.Shared.UIBuilder;
// using Microsoft.Extensions.DependencyInjection;
//
// namespace FlexCharge.Eligibility.Cures.Implementations;
//
// public class C0009p_AskForDifferentCardInsteadOfBlocked : AskForNewPaymentInstrumentCureBase
// {
//     // Ask for a new credit card - verify check digit on the fly
//     // Error 78 - ask for a new card explaining that used one needs to be unblocked
//     // Returns new payment instrument token
//
//     private Guid _cardNumberQuestionId = new Guid("40F36284-E898-446F-8241-CD7330AE9352");
//     private Guid _expiryMonthQuestionId = new Guid("DD032EE2-105F-407D-943D-90C366EB6842");
//     private Guid _expiryYearQuestionId = new Guid("C778A150-1765-49DE-8AA3-BB015C6F81D7");
//
//     protected override void Create(Order order, EvaluateRequest evaluateRequest)
//     {
//         AddTitle(order, "The card you have provided seems blocked. Please provide another card to complete the transaction.");
//
//
//         UI.HorizontalSeparator();
//
//         UI.SubTitle()
//             .Text("Please provide below the card details of a different card:");
//
//         using (UI.StartRow())
//         {
//             UI.SmallInput(InputType.Text, Guid.NewGuid())
//                 .Text("First Name:")
//                 .Placeholder("First Name")
//                 .Validations(v => v
//                     .Required("First Name is required")
//                     .Regex("^(\\s)*[A-Za-z]+((\\s)?((\\'|\\-|\\.)?([A-Za-z])+))*(\\s)*$", "First Name is invalid"));
//
//             UI.SmallInput(InputType.Text, Guid.NewGuid())
//                 .Text("Last Name:")
//                 .Placeholder("Last Name")
//                 .Validations(v => v
//                     .Required("Last Name is required")
//                     .Regex("^(\\s)*[A-Za-z]+((\\s)?((\\'|\\-|\\.)?([A-Za-z])+))*(\\s)*$", "First Name is invalid"));
//         }
//
//
//         UI.FullRowInput(InputType.Text, _cardNumberQuestionId)
//             .Id("ui-widget-input-card-mask")
//             .Placeholder("Card Number")
//             .Validations(v => v
//                 .Required("Card number is required")
//                 .Regex(
//                     "^(?:(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11}))$",
//                     "Card number is incorrect",
//                     stripWhitespaceBeforeValidation: true // to support masks with whitespace between number groups
//                 ));
//
//         using (UI.StartRow())
//         {
//             UI.Select(_expiryMonthQuestionId, columnSpan: 4)
//                 .Text("Expiry Month")
//                 .Placeholder("Month")
//                 .PotentialAnswers(x => x.AddAnswers(ExpiryMonthPossibleAnswers))
//                 .Validations(v => v.Required("Expiry Month is required"))
//                 .DefaultValue(null);
//
//             // UI.SmallInput(InputType.Text, Guid.NewGuid())
//             //     .Text("Expiry Month")
//             //     .Placeholder("Expiry month")
//             //     .Validations(v => v
//             //         .Required("Expiry date is required")
//             //         .Regex("^(0[1-9]|1[0-2])$", "Expiry month is incorrect")
//             //     );
//
//             UI.Select(_expiryYearQuestionId, columnSpan: 4)
//                 .Text("Expiry Year")
//                 .Placeholder("Year")
//                 .PotentialAnswers(x => x.AddAnswers(ExpiryYearPossibleAnswers))
//                 .Validations(v => v.Required("Expiry Year is required"))
//                 .DefaultValue(null);
//
//             // UI.SmallInput(InputType.Text, Guid.NewGuid())
//             //     .Text("Expiry Year")
//             //     .Placeholder("Year")
//             //     .Validations(v => v
//             //         .Required("Expiry year is required")
//             //         .Regex("^([0-9]{4}|[0-9]{2})$", "Expiry month is incorrect")
//             //     );
//
//             UI.Input(InputType.Text, Guid.NewGuid(), columnSpan: 4)
//                 .Text("CVV").Placeholder("CVV")
//                 .Attribute("maxLength", "4")
//                 .Validations(v => v
//                     .Required("CVV is required")
//                     .Regex("^[0-9]{3,4}$", "CVV is incorrect")
//                 );
//         }
//    }
//
//
//     protected override async Task<UserChallengeCureResult> ExecuteCureAsync(Order order,
//         EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
//     {
//         if (TryGetAnswerAsString(challengeReEvaluateRequest, _cardNumberQuestionId, out var cardNumber))
//         {
//             using (var serviceScope = ServiceScopeFactory.CreateScope())
//             {
//                 var securityCheckService = serviceScope.ServiceProvider.GetRequiredService<ISecurityCheckService>();
//                 // throws exception if not white-listed card is used in testing environment
//                 try
//                 {
//                     securityCheckService.EnsureInProductionOrTestCardIsWhitelisted(cardNumber);
//                 }
//                 catch
//                 {
//                     return UserChallengeCureResult.NOT_CURED;
//                 }
//             }
//         }
//
//         if (TryGetAnswerAsGuid(challengeReEvaluateRequest, _expiryMonthQuestionId, out var monthGuid) &&
//             ExpiryMonthAnswerHelper.TryToGetAnswer(monthGuid, out var expiryMonthAnswer) &&
//             TryGetAnswerAsGuid(challengeReEvaluateRequest, _expiryYearQuestionId, out var yearGuid) &&
//             ExpiryYearAnswerHelper.TryToGetAnswer(yearGuid, out var expiryYearAnswer)
//            )
//         {
//             try
//             {
//                 DateTime expiryDate = new DateTime(expiryYearAnswer.Value, expiryMonthAnswer.Value, 1);
//
//                 evaluateRequest.PaymentMethod.ExpirationMonth = expiryMonthAnswer.Value;
//                 evaluateRequest.PaymentMethod.ExpirationYear = expiryYearAnswer.Value;
//                 
//                 SetChangedData(DataChangeFlags.PaymentMethod);
//
//                 return UserChallengeCureResult.RE_EVALUATE;
//             }
//             catch (Exception e)
//             {
//                 Log($"Customer entered incorrect expiry date date: {expiryYearAnswer.Value}/{expiryMonthAnswer.Value}");
//                 return UserChallengeCureResult.NOT_CURED;
//             }
//         }
//         else return UserChallengeCureResult.NOT_CURED;
//     }
//
//     private static readonly ValueAnswer<int>[] ExpiryMonthPossibleAnswers = new[]
//     {
//         new ValueAnswer<int>(new Guid("34410BDC-CD1E-4B6B-A4F2-DDE54AD429EA"), "1", 1),
//         new ValueAnswer<int>(new Guid("B2CBE019-D33A-4E05-BECE-8F7FA168F5DD"), "2", 2),
//         new ValueAnswer<int>(new Guid("0FB46860-9B0E-4B53-9E86-BEC91FBBBCB1"), "3", 3),
//         new ValueAnswer<int>(new Guid("197A2855-E292-486F-AB03-233FD1873160"), "4", 4),
//         new ValueAnswer<int>(new Guid("B4932F61-4C30-4BE9-BC6F-5D90FCB7774C"), "5", 5),
//         new ValueAnswer<int>(new Guid("B06C5C9E-7232-46E9-995E-A6A69DD8D1C2"), "6", 6),
//         new ValueAnswer<int>(new Guid("B0F9D548-7368-4B00-8F65-912FCAC835E8"), "7", 7),
//         new ValueAnswer<int>(new Guid("94783F16-8981-4F33-9855-6A83EF000492"), "8", 8),
//         new ValueAnswer<int>(new Guid("2CBD8C46-9D4B-4FEC-AB51-34FA2F69B9FB"), "9", 9),
//         new ValueAnswer<int>(new Guid("E8606A89-B00C-4229-BF9F-77AA6D6F6DCB"), "10", 10),
//         new ValueAnswer<int>(new Guid("9605C9F0-D76A-484C-B43A-4319FC7E81EB"), "11", 11),
//         new ValueAnswer<int>(new Guid("2C98B399-CE01-495C-B694-722D4EA1BB14"), "12", 12),
//     };
//
//     private static SelectorAnswersHelper<ValueAnswer<int>> ExpiryMonthAnswerHelper = new(ExpiryMonthPossibleAnswers);
//
//     private static ValueAnswer<int>[] ExpiryYearPossibleAnswers;
//
//     private static SelectorAnswersHelper<ValueAnswer<int>> ExpiryYearAnswerHelper;
//
//     static C0009p_AskForDifferentCardInsteadOfBlocked()
//     {
//         List<ValueAnswer<int>> possibleExpiryYears = new();
//         var now = DateTime.Now;
//         for (int year = now.Year; year <= now.Year + 15; year++)
//         {
//             possibleExpiryYears.Add(new ValueAnswer<int>(Guid.NewGuid(), year.ToString(), year));
//         }
//
//         ExpiryYearPossibleAnswers = possibleExpiryYears.ToArray();
//
//         ExpiryYearAnswerHelper = new(ExpiryYearPossibleAnswers);
//     }
// }