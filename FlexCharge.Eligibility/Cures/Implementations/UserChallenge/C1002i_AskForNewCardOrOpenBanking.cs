using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Cures.Implementations;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.EligibilityChecks.Implementations;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Eligibility.Services;
using FlexCharge.Common.Shared.UIBuilder;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C1002i_AskForNewCardOrOpenBanking : AskForNewPaymentInstrumentCureBase
{
    protected override bool ShowSelectAnotherFormOfPaymentLink => false;


    // Ask for a new credit card - verify check digit on the fly
    // Returns new payment instrument token

    private Guid _openBankingButtonId = new Guid("4B9DB74E-DF42-4543-AF8A-E81D74326476");


    private Guid _cardNumberQuestionId = new Guid("40F36284-E898-446F-8241-CD7330AE9352");
    private Guid _firstNameQuestionId = new Guid("722BEDEA-4964-4F1A-822A-8A8D05595D26");
    private Guid _lastNameQuestionId = new Guid("74B758B1-2A2A-40FA-A921-01B577DE8D39");
    private Guid _expiryMonthQuestionId = new Guid("DD032EE2-105F-407D-943D-90C366EB6842");
    private Guid _expiryYearQuestionId = new Guid("C778A150-1765-49DE-8AA3-BB015C6F81D7");


    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest,
            IsImplicitConsentFlow ? "" : "Unfortunately we could not solve the issue with your card.<br>");

        UI.HorizontalSeparator();

        // UI.SubTitle()
        //     .Text("Please, select another payment type:");

        using (UI.StartFullRowList())
        {
            UI.ListItem()
                .Text(
                    //"We can try to process your payment again. Please insert the details of the card you want to use to process the payment."
                    "Do you want to pay with a different card? Please insert the details below:"
                );
        }

        using (UI.StartFullRow())
        {
            UI.FullRowInput(InputType.Text, Guid.NewGuid())
                .Id("ui-widget-input-card-mask")
                .Placeholder("Card Number")
                .Attribute("name", "number")
                .Validations(v => v
                    .Required("Card number is required")
                    .Regex(
                        "^(?:(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11}))$",
                        "Card number is incorrect",
                        stripWhitespaceBeforeValidation: true // to support masks with whitespace between number groups
                    ));

            using (UI.StartRow())
            {
                UI.Select(_expiryMonthQuestionId, columnSpan: 4)
                    .Text("Expiry Month")
                    .Placeholder("Month")
                    .Attribute("name", "month")
                    .PotentialAnswers(x => x.AddAnswers(ExpiryMonthPossibleAnswers))
                    .Validations(v => v.Required("Expiry Month is required"))
                    .DefaultValue(null);

                UI.Select(_expiryYearQuestionId, columnSpan: 4)
                    .Text("Expiry Year")
                    .Placeholder("Year")
                    .Attribute("name", "year")
                    .PotentialAnswers(x => x.AddAnswers(ExpiryYearPossibleAnswers))
                    .Validations(v => v.Required("Expiry Year is required"))
                    .DefaultValue(null);

                UI.Input(InputType.Text, Guid.NewGuid(), columnSpan: 4)
                    .Text("CVV").Placeholder("CVV")
                    .Attribute("maxLength", "4")
                    .Attribute("name", "verificationValue")
                    .Validations(v => v
                        .Required("CVV is required")
                        .Regex("^[0-9]{3,4}$", "CVV is incorrect")
                    );
            }
        }

        UI.HorizontalSeparator();

        using (UI.StartFullRowList())
        {
            UI.ListItem()
                .Text(
                    //$"Or you can pay with your bank account. All you need to do is to confirm your bank account via Plaid. "+
                    //$"You will be charged ${OrderAmount(order)}, <b>with no additional charges or fees.</b> Just click the Pay with bank account button below and follow the instructions of Plaid."
                    $"Or do you want to pay with your bank account? All you need to do is to confirm the account via Plaid. " +
                    $"<b>No additional charges or fees.</b>"
                );

            //"Alternatively, how about paying with your bank account? All you need to do is to link your bank account. You will be charged just the amount due.");
        }

        if (TrySplitCardHolderName(evaluateRequest, out var firstName, out var lastName))
        {
            AddParameter("FirstName", firstName);
            AddParameter("LastName", lastName);
        }
        else throw new NotEligibleException();

        AddParameter("CureType", "NewCard");
        AddParameter("TokenField", _cardNumberQuestionId.ToString());
        AddParameter("FirstNameField", _firstNameQuestionId.ToString());
        AddParameter("LastNameField", _lastNameQuestionId.ToString());
    }

    protected override void CreateFormButtons(EvaluateRequest evaluateRequest)
    {
        UI.SubmitFormButton(columnSpan: 6, Align.Left)
            .Text("Pay with card");

        UI.OptionButton(_openBankingButtonId, columnSpan: 6, Align.Right)
            .Text("Pay with bank account");
    }


    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        if (IsButtonPressed(challengeReEvaluateRequest, _openBankingButtonId))
        {
            return RunAnotherCure(nameof(C1001_AskForOpenBanking_Plaid));
        }
        else
        {
            if (TryGetAnswerAsString(challengeReEvaluateRequest, _cardNumberQuestionId, out var cardNumber))
            {
                using (var serviceScope = ServiceScopeFactory.CreateScope())
                {
                    var securityCheckService = serviceScope.ServiceProvider.GetRequiredService<ISecurityCheckService>();
                    // throws exception if not white-listed card is used in testing environment
                    try
                    {
                        securityCheckService.EnsureInProductionOrTestCardIsWhitelisted(cardNumber);

                        return ProcessNewPaymentTokenAnswer(_cardNumberQuestionId, evaluateRequest,
                            challengeReEvaluateRequest);
                    }
                    catch
                    {
                        return UserChallengeResult.NOT_CURED;
                    }
                }
            }
            else return UserChallengeResult.NOT_CURED;
        }

        return UserChallengeResult.NOT_CURED;
    }

    private static readonly ValueAnswer<int>[] ExpiryMonthPossibleAnswers = new[]
    {
        new ValueAnswer<int>(new Guid("34410BDC-CD1E-4B6B-A4F2-DDE54AD429EA"), "1", 1),
        new ValueAnswer<int>(new Guid("B2CBE019-D33A-4E05-BECE-8F7FA168F5DD"), "2", 2),
        new ValueAnswer<int>(new Guid("0FB46860-9B0E-4B53-9E86-BEC91FBBBCB1"), "3", 3),
        new ValueAnswer<int>(new Guid("197A2855-E292-486F-AB03-233FD1873160"), "4", 4),
        new ValueAnswer<int>(new Guid("B4932F61-4C30-4BE9-BC6F-5D90FCB7774C"), "5", 5),
        new ValueAnswer<int>(new Guid("B06C5C9E-7232-46E9-995E-A6A69DD8D1C2"), "6", 6),
        new ValueAnswer<int>(new Guid("B0F9D548-7368-4B00-8F65-912FCAC835E8"), "7", 7),
        new ValueAnswer<int>(new Guid("94783F16-8981-4F33-9855-6A83EF000492"), "8", 8),
        new ValueAnswer<int>(new Guid("2CBD8C46-9D4B-4FEC-AB51-34FA2F69B9FB"), "9", 9),
        new ValueAnswer<int>(new Guid("E8606A89-B00C-4229-BF9F-77AA6D6F6DCB"), "10", 10),
        new ValueAnswer<int>(new Guid("9605C9F0-D76A-484C-B43A-4319FC7E81EB"), "11", 11),
        new ValueAnswer<int>(new Guid("2C98B399-CE01-495C-B694-722D4EA1BB14"), "12", 12),
    };

    private static SelectorAnswersHelper<ValueAnswer<int>> ExpiryMonthAnswerHelper = new(ExpiryMonthPossibleAnswers);

    private static ValueAnswer<int>[] ExpiryYearPossibleAnswers;

    private static SelectorAnswersHelper<ValueAnswer<int>> ExpiryYearAnswerHelper;

    static C1002i_AskForNewCardOrOpenBanking()
    {
        List<ValueAnswer<int>> possibleExpiryYears = new();
        var now = DateTime.Now;
        for (int year = now.Year; year <= now.Year + 15; year++)
        {
            possibleExpiryYears.Add(new ValueAnswer<int>(Guid.NewGuid(), year.ToString(), year));
        }

        ExpiryYearPossibleAnswers = possibleExpiryYears.ToArray();

        ExpiryYearAnswerHelper = new(ExpiryYearPossibleAnswers);
    }
}