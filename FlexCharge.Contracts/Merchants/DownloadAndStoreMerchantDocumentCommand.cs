using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Common;

namespace FlexCharge.Contracts;

public record DownloadAndStoreMerchantDocumentCommand(
    Guid Mid,
    string FileName,
    string ContentType,
    string DownloadUrl
) : IdempotentCommand, ICorrelatedMessage
{
    public string? Bearer { get; set; }
    public Dictionary<string, string>? Headers { get; set; }


    Guid? ICorrelatedMessage.MessageCorrelationId => null;
    Guid? ICorrelatedMessage.MessageTenantId => Mid;
}