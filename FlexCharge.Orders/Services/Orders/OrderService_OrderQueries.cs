using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper.Configuration;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Exports;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Types;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Orders.Services;

public partial class OrderService
{
    public async Task<OrdersQueryResponseDTO> GetByIdAsync(Guid mid, Guid id)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", id)
            .LogEnterAndExit();

        try
        {
            Order record = await _readOnlyDbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .SingleOrDefaultAsync(x => x.Id == id);

            if (record == null)
            {
                workspan.Log.Information(
                    "OrderService > GetByIdAsync > NOTFOUND > ExternalReference: {Id}", id);

                return null;
            }
            else if (record.MerchantId != mid)
            {
                workspan.Log.Fatal(
                    "OrderService > GetByIdAsync > Failed to get order (wrong mid): merchantId: {Mid}; OrderId: {OrderId}",
                    mid, id);

                return null;
            }

            var mapped = _mapper.Map<OrdersQueryResponseDTO>(record);

            //var contact = await _client.GetResponse<ContactDTO>(record.Id.ToString());

            //This is for refunds support. Required only in this endpoint as only Merchants can invoke refunds.
            //Temporary solution - We'll be replaced by an advanced refund support in the future
            if (record != null)
            {
                mapped.RefundVoidTransactionId = record
                    .ActivityItems?
                    .OrderByDescending(x => x.CreatedOn)?
                    .FirstOrDefault(x => x.Type.ToLower() == "authorization")?
                    .PaymentTransactionId;
            }

            mapped.ActivityItems = mapped.ActivityItems.OrderBy(x => x.PaymentDate);
            mapped.IsCancelable =
                record.StatusCategory == nameof(OrderStatusCategory.draft) ||
                record.StatusCategory == nameof(OrderStatusCategory.processing) ||
                record.StatusCategory == nameof(OrderStatusCategory.onhold);

            mapped.PaidOutOfBand = record.PaidOutOfBand ?? false;

            var site = await _readOnlyDbContext.Sites.SingleOrDefaultAsync(x => x.Id == record.SiteId);
            var merchant = await _readOnlyDbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == record.MerchantId);
            mapped.SiteName = site?.Name;
            mapped.SiteDescriptor = site?.Descriptor;
            mapped.MerchantName = merchant?.Dba;
            mapped.MerchantId = merchant?.Mid ?? Guid.Empty;

            return mapped;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: orderService => Unable to get order ");
            throw;
        }
    }

    public async Task<PublicOrderQueryResponseDTO> PublicGetByIdAsync(Guid mid, Guid id)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", id)
            .LogEnterAndExit();

        try
        {
            Order record = await _readOnlyDbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .SingleOrDefaultAsync(x => x.Id == id);

            if (record == null)
            {
                workspan.Log.Information(
                    "OrderService > PublicGetByIdAsync > NOTFOUND > ExternalReference: {Id}", id);

                return null;
            }
            else if (record.MerchantId != mid)
            {
                workspan.Log.Fatal(
                    "OrderService > PublicGetByIdAsync > Failed to get order (wrong mid): merchantId: {Mid}; OrderId: {OrderId}",
                    mid, id);

                return null;
            }

            var mapped = _mapper.Map<PublicOrderQueryResponseDTO>(record);

            mapped.State = GetPublicOrderState(record);

            mapped.ActivityItems = mapped.ActivityItems.OrderBy(x => x.PaymentDate);

            var site = await _readOnlyDbContext.Sites.SingleOrDefaultAsync(x => x.Id == record.SiteId);
            var merchant = await _readOnlyDbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == record.MerchantId);

            mapped.SiteId = site?.Id ?? Guid.Empty;
            mapped.MerchantName = merchant?.Dba;
            mapped.MerchantId = merchant?.Mid ?? Guid.Empty;

            return mapped;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: orderService => Unable to get order ");
            throw;
        }
    }

    private OrderState GetPublicOrderState(Order record)
    {
        switch (record.StatusCategory)
        {
            case nameof(OrderStatusCategory.capturerequired):
                return OrderState.CaptureRequired;
            case nameof(OrderStatusCategory.cancelled):
                return OrderState.Cancelled;
            case nameof(OrderStatusCategory.completed):
                return OrderState.Completed;
            case nameof(OrderStatusCategory.processing):
            case nameof(OrderStatusCategory.draft):
            case nameof(OrderStatusCategory.onhold):
            case nameof(OrderStatusCategory.problem):
            case nameof(OrderStatusCategory.returned):
            default:
                return OrderState.Processing;
        }
    }

    public async Task<OrdersQueryResponseDTO> AdminGetByIdAsync(Guid? mid, Guid id)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", id)
            .LogEnterAndExit();

        try
        {
            var record = _readOnlyDbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .Where(x => x.Id == id);

            if (mid.HasValue)
                record = record.Where(x => x.MerchantId == mid);

            var order = await record.SingleOrDefaultAsync();

            if (order == null)
            {
                return null;
            }

            var mapped = _mapper.Map<OrdersQueryResponseDTO>(order);

            var site = await _readOnlyDbContext.Sites.SingleOrDefaultAsync(x => x.Id == order.SiteId);
            var merchant = await _readOnlyDbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == order.MerchantId);
            mapped.SiteName = site?.Name;
            mapped.SiteDescriptor = site?.Descriptor;
            mapped.MerchantName = merchant?.Dba;

            mapped.ActivityItems = mapped.ActivityItems.OrderBy(x => x.PaymentDate);
            //var contact = await _client.GetResponse<ContactDTO>(record.Id.ToString())
            mapped.IsCancelable =
                order.StatusCategory == nameof(OrderStatusCategory.draft) ||
                order.StatusCategory == nameof(OrderStatusCategory.processing) ||
                order.StatusCategory == nameof(OrderStatusCategory.onhold);

            return mapped;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: orderService => Unable to get order ");
            throw;
        }
    }

    public async Task<OrdersQueryResponseDTO> PartnerGetByIdAsync(Guid pid, Guid id, bool isIntegrationPartner)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Pid", pid)
            .Baggage("OrderId", id)
            .LogEnterAndExit();

        try
        {
            var merchants = _readOnlyDbContext.Merchants.AsQueryable();

            merchants = isIntegrationPartner
                ? merchants.Where(x => x.IntegrationPartnerId == pid)
                : merchants.Where(x => x.Pid == pid);

            var allowedMerchants = await merchants.Select(x => x.Mid).ToListAsync();
            if (allowedMerchants == null || !allowedMerchants.Any())
            {
                workspan.Log.Information("No merchants found for this partner");
                throw new FlexChargeException("No access", "No merchants found for this partner");
            }

            var record = _readOnlyDbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .AsQueryable();

            if (allowedMerchants.Any())
                record = record.Where(x => allowedMerchants.Contains(x.MerchantId));

            record = record.Where(x => x.Id == id);

            var order = await record.SingleOrDefaultAsync();

            if (order == null)
            {
                return null;
            }

            var mapped = _mapper.Map<OrdersQueryResponseDTO>(order);

            mapped.ActivityItems = mapped.ActivityItems.OrderBy(x => x.PaymentDate);
            //var contact = await _client.GetResponse<ContactDTO>(record.Id.ToString())
            mapped.IsCancelable =
                order.StatusCategory == nameof(OrderStatusCategory.draft) ||
                order.StatusCategory == nameof(OrderStatusCategory.processing) ||
                order.StatusCategory == nameof(OrderStatusCategory.onhold);

            var site = await _readOnlyDbContext.Sites.SingleOrDefaultAsync(x => x.Id == order.SiteId);
            var merchant = await _readOnlyDbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == order.MerchantId);
            mapped.SiteName = site?.Name;
            mapped.SiteDescriptor = site?.Descriptor;
            mapped.MerchantName = merchant?.Dba;

            return mapped;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: orderService => Unable to get order ");
            throw;
        }
    }

    public async Task<OrdersQueryResponseDTO> GetByExternalReferenceAsync(Guid mid, string id)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("ExternalOrderId", id)
            .LogEnterAndExit();

        try
        {
            Order record = await _readOnlyDbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .SingleOrDefaultAsync(x => x.ReferenceNumber == id);

            //var contact = await _client.GetResponse<ContactDTO>(record.Id.ToString());

            if (record is null)
            {
                workspan.Log.Information(
                    "OrderService > GetByExternalReferenceAsync > NOTFOUND > ExternalReference: {Id}", id);
                return null;
            }
            else if (record.MerchantId != mid)
            {
                workspan.Log.Fatal(
                    "OrderService > GetByExternalReferenceAsync > Failed to get order (wrong mid): merchantId: {Mid}; ExternalReference: {OrderId}",
                    mid, id);

                return null;
            }


            var mapped = _mapper.Map<OrdersQueryResponseDTO>(record);

            mapped.ActivityItems = mapped.ActivityItems.OrderBy(x => x.PaymentDate);

            return mapped;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: orderService => Unable to get order ");
            throw;
        }
    }

    public async Task<OrdersQueryResponse> GetAsync(Guid mid,
        Guid? partnerId,
        string query,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string orderBy,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        List<string>? types,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        string? bin,
        string? last4)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        try
        {
            workspan.Log.Information(
                "ENTERED: {Service} => {Method} => Mid: {M} Query: {Q} | Status: {S} | From: {F} | To: {T} | OrderBy: {O} | Sort: {Sort} | PageSize: {P} | Page #: {PageNumber}",
                nameof(OrderService), nameof(GetAsync), mid, query, status, from, to, orderBy, sort, pageSize,
                pageNumber);

            OrdersQueryResponse response = new OrdersQueryResponse();
            var dbset = _dbContext.Orders.Where(x => x.MerchantId == mid)
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .AsQueryable();

            var entity = await FilterAndSortOrders(dbset, query, status, from, to,
                placedFrom, placedTo, timezone, sort, sortField, pageSize, pageNumber, types, orderId, externalOrderId,
                contactId, isMIT, siteId, email, firstName, lastName, partnerId, bin, last4);

            if (entity == null)
            {
                response.AddError($"No orders");
                return response;
            }

            response.Orders =
                _mapper.Map<IPagedList<Entities.Order>, PagedDTO<OrdersQueryResponse.OrderItemQueryDTO>>(entity);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: OrderService => Unable to query orders");
            throw;
        }
    }

    public async Task<OrdersQueryResponse> AdminGetAsync(Guid? mid,
        Guid? partnerId,
        string query,
        bool? ghostMode,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string orderBy,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        List<string>? types,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        string? bin,
        string? last4)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        try
        {
            workspan.Log.Information(
                "ENTERED: {Service} => {Method} => Mid: {M} Query: {Q} | GhostMode: {GM} | Status: {S} | From: {F} | To: {T} | OrderBy: {O} | Sort: {Sort} | PageSize: {P} | Page #: {PageNumber}",
                nameof(OrderService), nameof(AdminGetAsync), mid, query, ghostMode, status, from, to, orderBy, sort,
                pageSize, pageNumber);

            OrdersQueryResponse response = new OrdersQueryResponse();
            var dbset = _dbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .Where(x => !x.IsDeleted)
                .IgnoreQueryFilters()
                .AsQueryable();

            if (mid != null)
                dbset = dbset.Where(x => x.MerchantId == mid);

            if (ghostMode != null)
                dbset = dbset.Where(x => x.IsGhostMode == ghostMode);

            var entity = await FilterAndSortOrders(dbset, query, status, from, to,
                placedFrom, placedTo, timezone, sort, sortField, pageSize, pageNumber, types, orderId, externalOrderId,
                contactId, isMIT, siteId, email, firstName, lastName, partnerId, bin, last4);

            if (entity == null)
            {
                response.AddError($"No orders");
                return response;
            }

            response.Orders =
                _mapper.Map<IPagedList<Entities.Order>, PagedDTO<OrdersQueryResponse.OrderItemQueryDTO>>(entity);
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: OrderService => Unable to query orders");
            throw;
        }
    }

    public async Task<OrdersQueryResponse> PartnerGetAsync(Guid pid, // pid from JWT token
        bool isIntegrationPartner,
        Guid mid,
        Guid? partnerId, // PartnerId from query string, to filter by partner
        string query,
        bool? ghostMode,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string orderBy,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        List<string>? types,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        string? bin,
        string? last4)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Pid", pid)
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        _securityCheckService.EnsurePartnerHasAccessToMerchant(pid, mid);

        try
        {
            workspan.Log.Information(
                "ENTERED: {Service} => {Method} => Mid: {M} Query: {Q} | GhostMode: {GM} | Status: {S} | From: {F} | To: {T} | OrderBy: {O} | Sort: {Sort} | PageSize: {P} | Page #: {PageNumber}",
                nameof(OrderService), nameof(PartnerGetAsync), mid, query, ghostMode, status, from, to, orderBy, sort,
                pageSize, pageNumber);

            var merchants = _readOnlyDbContext.Merchants.AsQueryable();

            if (isIntegrationPartner)
            {
                merchants = mid == Guid.Empty
                    ? merchants.Where(x => x.IntegrationPartnerId == pid)
                    : merchants.Where(x => x.IntegrationPartnerId == pid && x.Mid == mid);
            }
            else
            {
                merchants = mid == Guid.Empty
                    ? merchants.Where(x => x.Pid == pid)
                    : merchants.Where(x => x.Pid == pid && x.Mid == mid);
            }

            var allowedMerchants = await merchants.Select(x => x.Mid).ToListAsync();

            OrdersQueryResponse response = new OrdersQueryResponse();

            if (allowedMerchants == null || !allowedMerchants.Any())
            {
                workspan.Log.Information("No merchants found for this partner");
                return response;
            }

            var dbset = _dbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .Where(x => !x.IsDeleted)
                .IgnoreQueryFilters()
                .AsQueryable();

            if (merchants.Any())
                dbset = dbset.Where(x => allowedMerchants.Contains(x.MerchantId));

            var entity = await FilterAndSortOrders(dbset, query, status, from, to,
                placedFrom, placedTo, timezone, sort, sortField, pageSize, pageNumber, types, orderId, externalOrderId,
                contactId, isMIT, siteId, email, firstName, lastName, partnerId, bin, last4);

            if (entity == null)
            {
                response.AddError($"No orders");
                return response;
            }

            response.Orders =
                _mapper.Map<IPagedList<Entities.Order>, PagedDTO<OrdersQueryResponse.OrderItemQueryDTO>>(entity);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: OrderService => Unable to query orders");
            throw;
        }
    }

    public async Task<String> ExportAsync(Guid mid,
        Guid? partnerId, // PartnerId from query string, to filter by partner
        string query,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string orderBy,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        List<string>? types,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        string? bin,
        string? last4)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        try
        {
            workspan.Log.Information(
                "ENTERED: {Service} => {Method} => Mid: {M} Query: {Q} | Status: {S} | From: {F} | To: {T} | OrderBy: {O} | Sort: {Sort} | PageSize: {P} | Page #: {PageNumber}",
                nameof(OrderService), nameof(GetAsync), mid, query, status, from, to, orderBy, sort, pageSize,
                pageNumber);

            OrdersQueryResponse response = new OrdersQueryResponse();
            var dbset = _dbContext.Orders.Where(x => x.MerchantId == mid)
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .AsQueryable();

            var entity = await FilterAndSortOrders(dbset, query, status, from, to,
                placedFrom, placedTo, timezone, sort, sortField, pageSize, pageNumber, types, orderId, externalOrderId,
                contactId, isMIT, siteId, email, firstName, lastName, partnerId, bin, last4);
            var orders =
                _mapper.Map<IPagedList<Entities.Order>, PagedDTO<OrdersQueryResponse.OrderItemQueryDTO>>(entity);

            // String csv;
            // if (pageNumber != null && pageSize != null)
            // {
            //     orders = pageNumber == 1
            //         ? orders.Skip(0).Take((int)pageSize).ToList()
            //         : orders.Skip(((int)pageNumber - 1) * (int)pageSize).Take((int)pageSize).ToList();
            // }
            var csv = CSVExport.GenerateCSVFromRows(orders.rows.ToList());

            return csv;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: OrderService => Unable to query orders");
            throw;
        }
    }

    public async Task<String> AdminExportAsync(Guid? mid,
        Guid? partnerId, // PartnerId from query string, to filter by partner
        string query,
        bool? ghostMode,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string orderBy,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        List<string>? types,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        string? bin,
        string? last4)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        try
        {
            workspan.Log.Information(
                "ENTERED: {Service} => {Method} => Mid: {M} Query: {Q} | GhostMode: {GM} | Status: {S} | From: {F} | To: {T} | OrderBy: {O} | Sort: {Sort} | PageSize: {P} | Page #: {PageNumber}",
                nameof(OrderService), nameof(AdminGetAsync), mid, query, ghostMode, status, from, to, orderBy, sort,
                pageSize, pageNumber);

            OrdersQueryResponse response = new OrdersQueryResponse();
            var dbset = _dbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .Where(x => !x.IsDeleted)
                .IgnoreQueryFilters()
                .AsQueryable();

            if (mid != null)
                dbset = dbset.Where(x => x.MerchantId == mid);

            if (ghostMode != null)
                dbset = dbset.Where(x => x.IsGhostMode == ghostMode);

            var entity = await FilterAndSortOrders(dbset, query, status, from, to,
                placedFrom, placedTo, timezone, sort, sortField, pageSize, pageNumber, types, orderId, externalOrderId,
                contactId, isMIT, siteId, email, firstName, lastName, partnerId, bin, last4);
            var orders =
                _mapper.Map<IPagedList<Entities.Order>, PagedDTO<OrdersQueryResponse.OrderExportItemQueryDTO>>(entity);

            // String csv;
            // if (pageNumber != null && pageSize != null)
            // {
            //     orders = pageNumber == 1
            //         ? orders.Skip(0).Take((int)pageSize).ToList()
            //         : orders.Skip(((int)pageNumber - 1) * (int)pageSize).Take((int)pageSize).ToList();
            // }
            var csv = CSVExport.GenerateCSVFromRows(orders.rows.ToList());

            return csv;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: OrderService => Unable to query orders");
            throw;
        }
    }

    public async Task<String> PartnerExportAsync(Guid pid,
        bool isIntegrationPartner,
        Guid mid,
        Guid? partnerId, // PartnerId from query string, to filter by partner
        string query,
        bool? ghostMode,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string orderBy,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        List<string>? types,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        string? bin,
        string? last4)
    {
        using var workspan = Workspan.Start<OrderService>()
            .Baggage("Pid", pid)
            .Baggage("Mid", mid)
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        _securityCheckService.EnsurePartnerHasAccessToMerchant(pid, mid);

        try
        {
            workspan.Log.Information(
                "ENTERED: {Service} => {Method} => Mid: {M} Query: {Q} | GhostMode: {GM} | Status: {S} | From: {F} | To: {T} | OrderBy: {O} | Sort: {Sort} | PageSize: {P} | Page #: {PageNumber}",
                nameof(OrderService), nameof(PartnerGetAsync), mid, query, ghostMode, status, from, to, orderBy, sort,
                pageSize, pageNumber);

            var merchants = _dbContext.Merchants.AsQueryable();

            if (isIntegrationPartner)
            {
                merchants = mid == Guid.Empty
                    ? merchants.Where(x => x.IntegrationPartnerId == pid)
                    : merchants.Where(x => x.IntegrationPartnerId == pid && x.Mid == mid);
            }
            else
            {
                merchants = mid == Guid.Empty
                    ? merchants.Where(x => x.Pid == pid)
                    : merchants.Where(x => x.Pid == pid && x.Mid == mid);
            }

            var allowedMerchants = await merchants.Select(x => x.Mid).ToListAsync();

            OrdersQueryResponse response = new OrdersQueryResponse();

            if (allowedMerchants == null || !allowedMerchants.Any())
            {
                workspan.Log.Information("No merchants found for this partner");
                // throw new FlexChargeException("No access", "No merchants found for this partner"); 
                return String.Empty;
            }

            var dbset = _readOnlyDbContext.Orders
                .Include(x => x.BillingAddress)
                .Include(x => x.ShippingAddress)
                .Include(x => x.OrderItems)
                .Include(x => x.ActivityItems)
                .Where(x => !x.IsDeleted)
                .IgnoreQueryFilters()
                .AsQueryable();

            if (merchants.Any())
                dbset = dbset.Where(x => allowedMerchants.Contains(x.MerchantId));

            var entity = await FilterAndSortOrders(dbset, query, status, from, to,
                placedFrom, placedTo, timezone, sort, sortField, pageSize, pageNumber, types, orderId, externalOrderId,
                contactId, isMIT, siteId, email, firstName, lastName, partnerId, bin, last4);
            var orders =
                _mapper.Map<IPagedList<Entities.Order>, PagedDTO<OrdersQueryResponse.OrderItemQueryDTO>>(entity);

            // String csv;
            // if (pageNumber != null && pageSize != null)
            // {
            //     orders = pageNumber == 1
            //         ? orders.Skip(0).Take((int)pageSize).ToList()
            //         : orders.Skip(((int)pageNumber - 1) * (int)pageSize).Take((int)pageSize).ToList();
            // }
            var csv = CSVExport.GenerateCSVFromRows(orders.rows.ToList());

            return csv;
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "EXCEPTION: OrderService => Unable to query orders");
            throw;
        }
    }

    protected IQueryable<Order> CountByDate(IQueryable<Order> dbset, DateTime? from, DateTime? to, string? timezone,
        string field)
    {
        try
        {
            var fromUTC = from.Value.ToUniversalTime();
            var toUTC = to.Value.ToUniversalTime();
            if (timezone != null)
            {
                var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                if (!isTimezoneValid)
                    throw new Exception("Invalid timezone");

                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneName);

                fromUTC = TimeZoneInfo.ConvertTimeToUtc(from.Value, tz);
                toUTC = TimeZoneInfo.ConvertTimeToUtc(to.Value, tz);
            }

            switch (field)
            {
                case "createdOn":
                    dbset = dbset.Where(x =>
                        x.CreatedOn >= fromUTC &&
                        x.CreatedOn <= toUTC);
                    break;
                case "orderPlacedDate":
                default:
                    dbset = dbset.Where(x =>
                        x.OrderPlacedDate >= fromUTC &&
                        x.OrderPlacedDate <= toUTC);
                    break;
            }

            return dbset;
        }
        catch (Exception e)
        {
            throw;
        }
    }

    protected async Task<IPagedList<Order>> FilterAndSortOrders(IQueryable<Order> dbset,
        string query,
        List<OrderStatusCategory> status,
        DateTime? from,
        DateTime? to,
        DateTime? placedFrom,
        DateTime? placedTo,
        string? timezone,
        string sort,
        string sortField,
        int pageSize,
        int pageNumber,
        List<string>? types,
        Guid? orderId,
        string? externalOrderId,
        Guid? contactId,
        bool? isMIT,
        Guid? siteId,
        string? email,
        string? firstName,
        string? lastName,
        Guid? partnerId,
        string? bin,
        string? last4)
    {
        var statusList = status?.Select(x => x.ToString().ToLower()).ToList();

        try
        {
            if (orderId != null && orderId != Guid.Empty)
            {
                dbset = dbset.Where(x => x.Id == orderId);
            }

            if (externalOrderId != null)
            {
                dbset = dbset.Where(x => x.ReferenceNumber == externalOrderId);
            }

            if (!string.IsNullOrEmpty(email))
            {
                dbset = dbset.Where(x => x.Email == email.ToUpper());
            }

            if (!string.IsNullOrEmpty(firstName))
            {
                dbset = dbset.Where(x => x.FirstName == firstName.ToUpper());
            }

            if (!string.IsNullOrEmpty(lastName))
            {
                dbset = dbset.Where(x => x.LastName == lastName.ToUpper());
            }

            if (contactId != null && contactId != Guid.Empty)
            {
                dbset = dbset.Where(x => x.PayerId == contactId);
            }

            if (!string.IsNullOrEmpty(bin))
            {
                dbset = dbset.Where(x => x.Bin.Contains(bin));
            }

            if (!string.IsNullOrEmpty(last4))
            {
                dbset = dbset.Where(x => x.Last4 == last4);
            }

            var isStatusesEmpty = statusList == null || !statusList.Any();
            var isTypesEmpty = types == null || !types.Any();

            dbset = dbset.Where(x =>
                (isStatusesEmpty && isTypesEmpty) ||
                (!isTypesEmpty && x.ActivityItems.Any(z => types.Contains(z.Type))) ||
                (!isStatusesEmpty && statusList.Contains(x.StatusCategory))
            );

            if (from.HasValue && to.HasValue && from.Value > DateTime.MinValue && to.Value > DateTime.MinValue)
            {
                dbset = CountByDate(dbset, from, to, timezone, "createdOn");
            }

            if (placedFrom.HasValue && placedTo.HasValue && placedFrom.Value > DateTime.MinValue &&
                placedTo.Value > DateTime.MinValue)
            {
                dbset = CountByDate(dbset, placedFrom, placedTo, timezone, "orderPlacedDate");
            }

            if (isMIT.HasValue)
            {
                dbset = dbset.Where(x => x.IsMIT == isMIT.Value);
            }

            if (siteId.HasValue)
            {
                dbset = dbset.Where(x => x.SiteId == siteId.Value);
            }

            if (!string.IsNullOrEmpty(query))
                dbset = dbset.Where(x =>
                    x.MerchantName.ToLower().Contains(query.ToLower()) ||
                    x.StoreName.ToLower().Contains(query.ToLower()) ||
                    x.Id.ToString().ToLower().Contains(query.ToLower()) ||
                    x.ReferenceNumber.ToLower().Contains(query.ToLower()) ||
                    x.Phone.ToLower().Contains(query.ToLower()) ||
                    x.MerchantDescriptor.ToLower().Contains(query.ToLower()) ||
                    (x.FirstName.ToLower() + " " + x.LastName.ToLower()).Contains(query.ToLower()) ||
                    x.Email.ToLower().Contains(query.ToLower()));

            if (partnerId.HasValue && partnerId != Guid.Empty)
            {
                var merchants = _dbContext.Merchants
                    .Where(x => x.Pid == partnerId)
                    .Select(x => x.Mid)
                    .ToList();

                dbset = dbset.Where(x => merchants.Contains(x.MerchantId));
            }

            switch (sortField)
            {
                case "OrderPlacedDate":
                    dbset = sort == "asc"
                        ? dbset.OrderBy(x => x.OrderPlacedDate)
                        : dbset.OrderByDescending(x => x.OrderPlacedDate);
                    break;
                case "CreatedOn":
                default:
                    dbset = sort == "asc"
                        ? dbset.OrderBy(x => x.CreatedOn)
                        : dbset.OrderByDescending(x => x.CreatedOn);
                    break;
            }

            var skipIndex = pageNumber == 1 ? 0 : ((int) pageNumber - 1) * (int) pageSize;

            var countEstimate = _dbContext.CountEstimator(dbset);

            var entity = await dbset
                .Select(x => new Order()
                {
                    Id = x.Id,
                    MerchantId = x.MerchantId,
                    MerchantName = x.MerchantName,
                    StoreName = x.StoreName,
                    ReferenceNumber = x.ReferenceNumber,
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                    Email = x.Email,
                    CreatedOn = ConvertToLocalDateTime(x.CreatedOn, timezone),
                    StatusCategory = x.StatusCategory,
                    OrderPlacedDate = ConvertToLocalDateTime(x.OrderPlacedDate, timezone),
                    RefundsAmount = x.RefundsAmount,
                    IsGhostMode = x.IsGhostMode,
                    BillingAddress = x.BillingAddress,
                    ShippingAddress = x.ShippingAddress,
                    OrderItems = x.OrderItems,
                    ActivityItems = x.ActivityItems,
                    ExpiryDate = x.ExpiryDate != null ? ConvertToLocalDateTime(x.ExpiryDate.Value, timezone) : null,
                    OrderPayoutStatus = x.OrderPayoutStatus,
                    Currency = x.Currency,
                    Amount = x.Amount,
                    StoreId = x.StoreId,
                    MerchantDescriptor = x.MerchantDescriptor,
                    PaymentInstrumentToken = x.PaymentInstrumentToken,
                    IsMIT = x.IsMIT,
                    SiteId = x.SiteId,
                    Bin = x.Bin,
                    Last4 = x.Last4,
                })
                .ToPagedListAsync(pageNumber, pageSize, null, CancellationToken.None, countEstimate);


            return entity;
        }
        catch (Exception e)
        {
            throw;
        }
    }

    static DateTime ConvertToLocalDateTime(DateTime date, string? timezone)
    {
        try
        {
            if (timezone != null)
            {
                var isTimezoneValid = TimeZoneInfo.TryConvertIanaIdToWindowsId(timezone, out var timezoneName);

                if (!isTimezoneValid)
                    throw new Exception("Invalid timezone");

                var convertedDate = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(date, timezoneName);
                return convertedDate;
            }

            return date;
        }
        catch (Exception e)
        {
            throw;
        }
    }
}