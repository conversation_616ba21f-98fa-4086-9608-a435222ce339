using System;

namespace FlexCharge.Orders.Services.Reporting.OrdersSnapshotReport;

public class OrdersSnapshotReportModel
{
    public Guid OrderId { get; set; }
    public string ExternalId { get; set; }
    public DateTime EventTimestamp { get; set; }

    public string Currency { get; set; }

    // public string CurrencySymbol { get; set; }
    // public int CurrencyCode { get; set; }
    public decimal Amount { get; set; }
    public string OrderState { get; set; }

    public string Bin { get; set; }
    public string Last4 { get; set; }
}