using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace FlexCharge.Orders.Services.Reporting.OrdersSnapshotReport;

public class OrdersSnapshotReport : ReportBase<OrdersSnapshotReportParams, OrdersSnapshotReportOptions,
    OrdersSnapshotReportModel>
{
    public override Guid ReportId => new Guid("a9967a72-c4e4-49f4-9f3b-0a89c966fd07");
    public override string ReportName => "orders-snapshot";

    public OrdersSnapshotReport(IOptions<OrdersSnapshotReportOptions> options, PostgreSQLDbContext dbContext) : base(
        options, dbContext)
    {
    }

    protected override bool TryValidate(OrdersSnapshotReportParams parameter, out ReportValidator dict)
    {
        throw new NotImplementedException();
    }

    public override async Task<List<OrdersSnapshotReportModel>> GenerateRowsAsync(DateTime from, DateTime to,
        OrdersSnapshotReportParams param, CancellationToken ctoken)
    {
        var result = new List<OrdersSnapshotReportModel>();

        using var workspan = Workspan.Start<OrdersSnapshotReport>()
            .Baggage("ReportId", ReportId.ToString())
            .Baggage("ReportName", ReportName)
            .Baggage("From", from.ToString("o"))
            .Baggage("To", to.ToString("o"))
            .Baggage("Mid", param.Mid.ToString())
            .LogEnterAndExit();

        workspan.Log.Information("Generating report for {ParamMid} from {From} to {Unknown}", param.Mid, from, to);

        await foreach (var row in GenerateRowsYieldAsync(from, to, param, ctoken))
        {
            result.Add(row);
        }

        return result;
    }

    public override async IAsyncEnumerable<OrdersSnapshotReportModel> GenerateRowsYieldAsync(
        DateTime from,
        DateTime to,
        OrdersSnapshotReportParams param,
        [EnumeratorCancellation] CancellationToken ctoken)
    {
        using var workspan = Workspan.Start<OrdersSnapshotReport>()
            .LogEnterAndExit();

        var dbContext = _dbContext as PostgreSQLDbContext;

        var orders = dbContext.Orders
            .Include(o => o.ActivityItems)
            .Where(o =>
                o.IsTestOrder == false &&
                o.IsGhostMode == false &&
                o.StatusCategory == nameof(OrderStatusCategory.completed) &&
                o.CreatedOn <= to &&
                o.CreatedOn >= from &&
                o.MerchantId == param.Mid);

        await foreach (var order in orders.AsAsyncEnumerable().WithCancellation(ctoken))
        {
            yield return new OrdersSnapshotReportModel
            {
                OrderId = order.Id,
                ExternalId = order.ReferenceNumber,
                EventTimestamp = order.CreatedOn,
                OrderState = nameof(OrderStatusCategory.completed),
                Bin = order.Bin,
                Last4 = order.Last4,
                Amount = Utils.Formatters.IntToDecimal(order.Amount),
                Currency = order.Currency,
                //CurrencySymbol = order.CurrencySymbol,
                //CurrencyCode = order.CurrencyCode,
            };
        }
    }
}