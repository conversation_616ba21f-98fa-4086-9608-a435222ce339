using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Consumers;

public class EligibilityOfferThresholdLimitEventConsumer : ConsumerBase<EligibilityOfferThresholdLimitEvent>
{
    private IOrderService _orderService;
    private IMapper _mapper;
    private readonly IDistributedLockService _distributedLockService;

    public EligibilityOfferThresholdLimitEventConsumer(
        IOrderService orderService, IMapper mapper, 
        IServiceScopeFactory serviceScopeFactory,
        IDistributedLockService distributedLockService) : 
        base(serviceScopeFactory)
    {
        _orderService = orderService;
        _mapper = mapper;
        _distributedLockService = distributedLockService;
    }

    protected override async Task ConsumeMessage(EligibilityOfferThresholdLimitEvent message,
        CancellationToken cancellationToken)
    {
        Workspan
            .Baggage("Mid", message.Mid)
            .Baggage("OrderId", message.OrderId);

        try
        {
            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateOrderKey(message.OrderId),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));


            await _orderService.SetOrderStatusAsync(message.Mid, message.OrderId, OrderStatusCategory.problem, "",
                message.Message);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                $"EXCEPTION: OrdersService > OrderEligibilityThresholdLimitEventConsumer > payload: {JsonConvert.SerializeObject(message)}");
        }
    }
}