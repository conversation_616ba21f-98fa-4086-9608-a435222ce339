namespace FlexCharge.Orders.DTO;
using CsvHelper.Configuration;
using FlexCharge.Orders.Entities;

public class FundsReserveExportDTO : ClassMap<FundsReserve>
{
    public FundsReserveExportDTO()
    {
        Map(m => m.CreatedOn).Name("Created On");
        Map(m => m.Amount).Name("Amount").TypeConverterOption.Format("0.00");
        Map(m => m.OrderId).Name("Order Id");
        Map(m => m.Status).Name("Status");
        Map(m => m.Reason).Name("Reason");
        Map(m => m.Type).Name("Type");
        Map(m => m.Id).Name("ID");
        Map(m => m.FinancialAccountId).Name("Financial Account Id");
        Map(m => m.Mid).Name("Mid");
        Map(m => m.BatchId).Name("BatchId");
        Map(m => m.Note).Name("Note");
        
    }
}