using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands.Merchants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.Merchants.Consumers;

public class GetMerchantCommandConsumer : IdempotentCommandConsumer<GetMerchantCommand, GetMerchantCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;

    public GetMerchantCommandConsumer(
        PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }

    protected override async Task<GetMerchantCommandResponse> ConsumeCommand(
        GetMerchantCommand command,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<GetMerchantCommandConsumer>()
            .Baggage("MerchantId", command.MerchantId)
            .LogEnterAndExit();

        try
        {
            var merchant = await _dbContext.Merchants
                .FirstOrDefaultAsync(x => x.Id == command.MerchantId && !x.IsDeleted, cancellationToken);

            if (merchant == null)
            {
                return new GetMerchantCommandResponse
                {
                    Error = "Merchant not found"
                };
            }

            return new GetMerchantCommandResponse
            {
                MerchantId = merchant.Id,
                PartnerId = merchant.PartnerId,
                IntegrationPartnerId = merchant.IntegrationPartnerId,
                IsActive = merchant.Status == Enums.ActiveInActive.ACTIVE
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return new GetMerchantCommandResponse
            {
                Error = e.Message
            };
        }
    }
}