// <auto-generated />
using System;
using System.Collections.Generic;
using FlexCharge.Merchants;
using FlexCharge.Merchants.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20250513103746_add-NotEligibleEverOrderProcessingWorkflowId-column-to-Merchants-table")]
    partial class addNotEligibleEverOrderProcessingWorkflowIdcolumntoMerchantstable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "active_in_active", new[] { "active", "inactive" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "application_status", new[] { "draft", "cancelled", "submitted", "in_review", "approved", "declined" });
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Account", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("IntegrationPartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Accounts");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BuildingUnit")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Line1")
                        .HasColumnType("text");

                    b.Property<string>("Line2")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Street")
                        .HasColumnType("text");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Application", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AccountId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AccountManagerId")
                        .HasColumnType("uuid");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AgreeToTerms")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AnnualCreditVolume")
                        .HasColumnType("integer");

                    b.Property<int>("AnnualDebitVolume")
                        .HasColumnType("integer");

                    b.Property<int>("AnnualSalesVolume")
                        .HasColumnType("integer");

                    b.Property<Guid>("Assignee")
                        .HasColumnType("uuid");

                    b.Property<string>("AvgDeclineRate")
                        .HasColumnType("text");

                    b.Property<string>("AvgDeliveryTime")
                        .HasColumnType("text");

                    b.Property<string>("AvgDisputeRate")
                        .HasColumnType("text");

                    b.Property<string>("AvgMonthlySales")
                        .HasColumnType("text");

                    b.Property<int>("AvgPurchaseAmount")
                        .HasColumnType("integer");

                    b.Property<string>("AvgTransactionAmount")
                        .HasColumnType("text");

                    b.Property<DateTime?>("BankAccountVerified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("BusinessEstablishedDate")
                        .HasColumnType("text");

                    b.Property<string>("ChargebackPercentage")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<bool>("ContractAlreadySigned")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CrmId")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<string>("DdaType")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeveloperContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("EcommercePlatform")
                        .HasColumnType("text");

                    b.Property<string>("EcommercePlatformUsagePeriod")
                        .HasColumnType("text");

                    b.Property<string>("ExternalId")
                        .HasColumnType("text");

                    b.Property<string>("Healthcare")
                        .HasColumnType("text");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<string>("InfoDisclosed")
                        .HasColumnType("text");

                    b.Property<Guid?>("IntegrationPartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IntegrationPartnerParticipateSale")
                        .HasColumnType("boolean");

                    b.Property<string>("IntegrationTier")
                        .HasColumnType("text");

                    b.Property<int>("IntegrationType")
                        .HasColumnType("integer");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSiteValidated")
                        .HasColumnType("boolean");

                    b.Property<string>("LegalEntityCountry")
                        .HasColumnType("text");

                    b.Property<string>("LegalEntityName")
                        .HasColumnType("text");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("MaxSubDuration")
                        .HasColumnType("text");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OperationsStatus")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Pcidss")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("PrimaryContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductsSold")
                        .HasColumnType("text");

                    b.Property<string>("Regulated")
                        .HasColumnType("text");

                    b.Property<string>("ReturnsPercent")
                        .HasColumnType("text");

                    b.Property<string>("RiskCategory")
                        .HasColumnType("text");

                    b.Property<string>("RiskStatus")
                        .HasColumnType("text");

                    b.Property<string>("RoutingNumber")
                        .HasColumnType("text");

                    b.Property<string>("RoutingType")
                        .HasColumnType("text");

                    b.Property<Guid?>("SalesAgencyId")
                        .HasColumnType("uuid");

                    b.Property<string>("SitesCount")
                        .HasColumnType("text");

                    b.Property<bool>("SkipRiskAssessment")
                        .HasColumnType("boolean");

                    b.Property<string>("SpecialTerms")
                        .HasColumnType("text");

                    b.Property<ApplicationStatus>("Status")
                        .HasColumnType("application_status");

                    b.Property<string>("TaxId")
                        .HasColumnType("text");

                    b.Property<string>("TimeInBusiness")
                        .HasColumnType("text");

                    b.Property<List<string>>("TrafficAcquisitionChannels")
                        .HasColumnType("jsonb");

                    b.Property<string>("TrialPeriod")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("AddressId");

                    b.HasIndex("DeveloperContactId");

                    b.HasIndex("IntegrationPartnerId");

                    b.HasIndex("PartnerId");

                    b.HasIndex("PrimaryContactId");

                    b.HasIndex("SalesAgencyId");

                    b.ToTable("Applications");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.ApplicationActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Department")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("UpdateMessage")
                        .HasColumnType("text");

                    b.Property<string>("User")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("MerchantId");

                    b.ToTable("ApplicationActivity");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.ApplicationFee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<int>("ChargeType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<int?>("MinimumFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.ToTable("ApplicationFee");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AffectedColumns")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Entity")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Microservice")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NewValues")
                        .HasColumnType("text");

                    b.Property<string>("OldValues")
                        .HasColumnType("text");

                    b.Property<string>("Operation")
                        .HasColumnType("text");

                    b.Property<string>("PrimaryKey")
                        .HasColumnType("text");

                    b.Property<string>("TableName")
                        .HasColumnType("text");

                    b.Property<string>("UserEmail")
                        .HasColumnType("text");

                    b.Property<string>("UserFirstName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserIp")
                        .HasColumnType("text");

                    b.Property<string>("UserLastName")
                        .HasColumnType("text");

                    b.Property<string>("UserRole")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.CommissionsConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("From")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<int?>("MinimumFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SalesAgentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("To")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PartnerId");

                    b.HasIndex("SalesAgentId");

                    b.ToTable("CommissionsConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("ConfirmedUser")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<Guid>("ExternalId")
                        .HasColumnType("uuid");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<bool>("Primary")
                        .HasColumnType("boolean");

                    b.Property<string>("Role")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryPhone")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("MerchantId");

                    b.ToTable("Document");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.FeeConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<int>("ChargeType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<int?>("MinimumFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PartnerId");

                    b.ToTable("FeeConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.FundsReserveConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DisputeRateMax")
                        .HasColumnType("integer");

                    b.Property<int>("DisputeRateMin")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("Period")
                        .HasColumnType("integer");

                    b.Property<int>("ReserveRate")
                        .HasColumnType("integer");

                    b.Property<int>("ReserveRateType")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("FundsReserveConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MccCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("MccCodes");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AccountId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AccountManagerId")
                        .HasColumnType("uuid");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<bool>("AccountUpdaterEnabled")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AgreeToTerms")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("AllowBinCheckOnTokenization")
                        .HasColumnType("boolean");

                    b.Property<int>("AnnualCreditVolume")
                        .HasColumnType("integer");

                    b.Property<int>("AnnualDebitVolume")
                        .HasColumnType("integer");

                    b.Property<int>("AnnualSalesVolume")
                        .HasColumnType("integer");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("Assignee")
                        .HasColumnType("uuid");

                    b.Property<string>("AvgDeclineRate")
                        .HasColumnType("text");

                    b.Property<string>("AvgDeliveryTime")
                        .HasColumnType("text");

                    b.Property<string>("AvgDisputeRate")
                        .HasColumnType("text");

                    b.Property<string>("AvgMonthlySales")
                        .HasColumnType("text");

                    b.Property<int>("AvgPurchaseAmount")
                        .HasColumnType("integer");

                    b.Property<string>("AvgTransactionAmount")
                        .HasColumnType("text");

                    b.Property<DateTime?>("BankAccountVerified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<bool>("BillingInformationOptional")
                        .HasColumnType("boolean");

                    b.Property<string>("BusinessEstablishedDate")
                        .HasColumnType("text");

                    b.Property<bool>("CITClickToRefundEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("CITConsumerNotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("CITEvaluateAsync")
                        .HasColumnType("boolean");

                    b.Property<bool>("CaptureRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("ChargebackPercentage")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int?>("ConsumerOrderNotificationChannel")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CrmId")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Dba")
                        .HasColumnType("text");

                    b.Property<string>("DdaType")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeveloperContactId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("DynamicAuthorizationDiscountThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("EcommercePlatform")
                        .HasColumnType("text");

                    b.Property<string>("EcommercePlatformUsagePeriod")
                        .HasColumnType("text");

                    b.Property<Guid?>("EligibilityStrategyWorkflowId")
                        .HasColumnType("uuid");

                    b.Property<bool>("EnableGlobalNetworkTokenization")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("FinancialAccountId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("GhostModeThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<bool>("Global3DSEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Healthcare")
                        .HasColumnType("text");

                    b.Property<string>("Icon")
                        .HasColumnType("text");

                    b.Property<bool>("IgnoreSiteIdFromClient")
                        .HasColumnType("boolean");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<string>("InfoDisclosed")
                        .HasColumnType("text");

                    b.Property<bool>("InformationalOnly3DS")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("IntegrationPartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IntegrationPartnerParticipateSale")
                        .HasColumnType("boolean");

                    b.Property<string>("IntegrationTier")
                        .HasColumnType("text");

                    b.Property<string>("IntegrationType")
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsAvsRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBureauProductionActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCrawlingEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCvvRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEnforceMFAEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIframeMessagesCollectEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIntegrationGuideEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMitEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSenseJsOptional")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSiteValidated")
                        .HasColumnType("boolean");

                    b.Property<string>("Language")
                        .HasColumnType("text");

                    b.Property<int>("LatestDisputeRate")
                        .HasColumnType("integer");

                    b.Property<string>("LegalEntityCountry")
                        .HasColumnType("text");

                    b.Property<string>("LegalEntityName")
                        .HasColumnType("text");

                    b.Property<bool>("Locked")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<int?>("MITAgreedExpiryHours")
                        .HasColumnType("integer");

                    b.Property<bool>("MITClickToRefundEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITConsumerCuresEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITConsumerNotificationsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITEvaluateAsync")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITGetSiteByDynamicDescriptorEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MITImmediateRetryEnabled")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxOrderAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(30000);

                    b.Property<string>("MaxSubDuration")
                        .HasColumnType("text");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<int>("MinOrderAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(900);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NotEligibleEverOrderProcessingWorkflowId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("NotEligibleOrderProcessingWorkflowId")
                        .HasColumnType("uuid");

                    b.Property<int?>("OfferRequestsMaxPerDay")
                        .HasColumnType("integer");

                    b.Property<int?>("OfferRequestsRateLimitCount")
                        .HasColumnType("integer");

                    b.Property<int?>("OfferRequestsRateLimitIntervalMS")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OfferRequestsThrottlePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Offer_NSF_RequestsThrottle_Percentage")
                        .HasColumnType("numeric");

                    b.Property<string>("OperationsStatus")
                        .HasColumnType("text");

                    b.Property<int?>("Orders_MaxMonthlyAmount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("PayerEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("PayoutsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("Pcidss")
                        .HasColumnType("boolean");

                    b.Property<string>("PrimaryColor")
                        .HasColumnType("text");

                    b.Property<Guid?>("PrimaryContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("PrivateSshKey")
                        .HasColumnType("text");

                    b.Property<string>("ProductsSold")
                        .HasColumnType("text");

                    b.Property<string>("PublicSshKeyId")
                        .HasColumnType("text");

                    b.Property<Guid?>("RecyclingStrategyWorkflowId")
                        .HasColumnType("uuid");

                    b.Property<bool>("RedactIpEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Regulated")
                        .HasColumnType("text");

                    b.Property<string>("ReturnsPercent")
                        .HasColumnType("text");

                    b.Property<string>("RiskCategory")
                        .HasColumnType("text");

                    b.Property<int?>("RiskLevel")
                        .HasColumnType("integer");

                    b.Property<int?>("RiskLevel_Visa")
                        .HasColumnType("integer");

                    b.Property<string>("RiskStatus")
                        .HasColumnType("text");

                    b.Property<int?>("RiskTier")
                        .HasColumnType("integer");

                    b.Property<string>("RoutingNumber")
                        .HasColumnType("text");

                    b.Property<string>("RoutingType")
                        .HasColumnType("text");

                    b.Property<Guid?>("SalesAgencyId")
                        .HasColumnType("uuid");

                    b.Property<bool>("SchemeTransactionIdEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("SecondaryColor")
                        .HasColumnType("text");

                    b.Property<string>("SitesCount")
                        .HasColumnType("text");

                    b.Property<string>("SpecialTerms")
                        .HasColumnType("text");

                    b.Property<ActiveInActive>("Status")
                        .HasColumnType("active_in_active");

                    b.Property<string>("SupportedCountries")
                        .HasColumnType("text");

                    b.Property<string>("TaxId")
                        .HasColumnType("text");

                    b.Property<string>("TimeInBusiness")
                        .HasColumnType("text");

                    b.Property<int?>("Timezone")
                        .HasColumnType("integer");

                    b.Property<string>("TimezoneName")
                        .HasColumnType("text");

                    b.Property<List<string>>("TrafficAcquisitionChannels")
                        .HasColumnType("jsonb");

                    b.Property<string>("TrialPeriod")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<bool?>("UIWidgetOptional")
                        .HasColumnType("boolean");

                    b.Property<bool>("UseDefaultSiteForUnknownMerchantUrlsEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.Property<bool>("VirtualTerminalEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("AddressId");

                    b.HasIndex("DeveloperContactId");

                    b.HasIndex("IntegrationPartnerId");

                    b.HasIndex("PartnerId");

                    b.HasIndex("PrimaryContactId");

                    b.HasIndex("SalesAgencyId");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MerchantFee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<int>("ChargeType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("MinimumFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("RelatedMerchantId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RelatedMerchantId");

                    b.ToTable("MerchantFees");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MerchantFundsReserveConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("FundsReserveConfigurationId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("FundsReserveConfigurationId");

                    b.HasIndex("MerchantId");

                    b.ToTable("MerchantFundsReserveConfiguration");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("Expiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Notified")
                        .HasColumnType("boolean");

                    b.Property<string>("Params")
                        .HasColumnType("text");

                    b.Property<bool>("Read")
                        .HasColumnType("boolean");

                    b.Property<bool>("Seen")
                        .HasColumnType("boolean");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<int>("Source")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("TriggerCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Owner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("IdentificationNumber")
                        .HasColumnType("text");

                    b.Property<string>("IndividualFormOfID")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PercentOwnership")
                        .HasColumnType("integer");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("PrincipalType")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("MerchantId");

                    b.ToTable("Owners");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Partner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("BccEmail")
                        .HasColumnType("text");

                    b.Property<string>("ConsentLabel")
                        .HasColumnType("text");

                    b.Property<Guid?>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DashboardId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FinancialAccountId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("MerchantTermsAndConditionsUrl")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NotificationSenderEmail")
                        .HasColumnType("text");

                    b.Property<string>("NotificationSenderPhone")
                        .HasColumnType("text");

                    b.Property<string>("OperationsEmail")
                        .HasColumnType("text");

                    b.Property<bool>("PartnerFeesAutoCollectEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("PrivacyPolicyUrl")
                        .HasColumnType("text");

                    b.Property<string>("ReplyToEmail")
                        .HasColumnType("text");

                    b.Property<string>("RiskEmail")
                        .HasColumnType("text");

                    b.Property<string>("SalesEmail")
                        .HasColumnType("text");

                    b.Property<string>("SiteUrl")
                        .HasColumnType("text");

                    b.Property<ActiveInActive?>("Status")
                        .HasColumnType("active_in_active");

                    b.Property<string>("SupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("SupportLink")
                        .HasColumnType("text");

                    b.Property<string>("SupportName")
                        .HasColumnType("text");

                    b.Property<string>("SupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("TermsAndConditions")
                        .HasColumnType("text");

                    b.Property<string>("TermsAndConditionsUrl")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("ContactId");

                    b.ToTable("Partners");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.PartnerFee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<int?>("ApplyOrder")
                        .HasColumnType("integer");

                    b.Property<string>("ChargeType")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Group")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("MinimumFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PartnerId");

                    b.ToTable("PartnerFees");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Report", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastRunDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MerchantId")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<DateTime>("NextRunDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("RelatedMerchantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("SubscribedDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("RelatedMerchantId");

                    b.ToTable("Report");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SalesAgency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SalesAgencies");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SalesAgent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AgencyId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("ContactId");

                    b.ToTable("SalesAgents");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerSupportEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportLink")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerSupportPhone")
                        .HasColumnType("text");

                    b.Property<string>("Descriptor")
                        .HasColumnType("text");

                    b.Property<string>("DescriptorCity")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeveloperContactId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<Guid?>("SupportContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("Tags")
                        .HasColumnType("jsonb");

                    b.HasKey("Id");

                    b.HasIndex("DeveloperContactId");

                    b.HasIndex("MerchantId");

                    b.HasIndex("SupportContactId");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SiteCandidate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool?>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.ToTable("SiteCandidates");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SiteWhitelistedUrl", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Link")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("SiteWhitelistedUrls");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Application", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Account", null)
                        .WithMany("Applications")
                        .HasForeignKey("AccountId");

                    b.HasOne("FlexCharge.Merchants.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "DeveloperContact")
                        .WithMany()
                        .HasForeignKey("DeveloperContactId");

                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "IntegrationPartner")
                        .WithMany()
                        .HasForeignKey("IntegrationPartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "Partner")
                        .WithMany()
                        .HasForeignKey("PartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "PrimaryContact")
                        .WithMany()
                        .HasForeignKey("PrimaryContactId");

                    b.HasOne("FlexCharge.Merchants.Entities.SalesAgency", "SalesAgency")
                        .WithMany()
                        .HasForeignKey("SalesAgencyId");

                    b.Navigation("Address");

                    b.Navigation("DeveloperContact");

                    b.Navigation("IntegrationPartner");

                    b.Navigation("Partner");

                    b.Navigation("PrimaryContact");

                    b.Navigation("SalesAgency");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.ApplicationActivity", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Application", null)
                        .WithMany("Activities")
                        .HasForeignKey("ApplicationId");

                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", null)
                        .WithMany("Activities")
                        .HasForeignKey("MerchantId");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.ApplicationFee", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Application", "Application")
                        .WithMany("Fees")
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Application");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.CommissionsConfiguration", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Partner", null)
                        .WithMany("CommissionsConfigurations")
                        .HasForeignKey("PartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.SalesAgent", null)
                        .WithMany("CommissionsConfigurations")
                        .HasForeignKey("SalesAgentId");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Document", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Application", "Application")
                        .WithMany("Documents")
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", null)
                        .WithMany("Documents")
                        .HasForeignKey("MerchantId");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.FeeConfiguration", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Partner", null)
                        .WithMany("FeeConfigurations")
                        .HasForeignKey("PartnerId");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Merchant", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Account", "Account")
                        .WithMany("Merchants")
                        .HasForeignKey("AccountId");

                    b.HasOne("FlexCharge.Merchants.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "DeveloperContact")
                        .WithMany()
                        .HasForeignKey("DeveloperContactId");

                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "IntegrationPartner")
                        .WithMany()
                        .HasForeignKey("IntegrationPartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "Partner")
                        .WithMany()
                        .HasForeignKey("PartnerId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "PrimaryContact")
                        .WithMany()
                        .HasForeignKey("PrimaryContactId");

                    b.HasOne("FlexCharge.Merchants.Entities.SalesAgency", "SalesAgency")
                        .WithMany()
                        .HasForeignKey("SalesAgencyId");

                    b.Navigation("Account");

                    b.Navigation("Address");

                    b.Navigation("DeveloperContact");

                    b.Navigation("IntegrationPartner");

                    b.Navigation("Partner");

                    b.Navigation("PrimaryContact");

                    b.Navigation("SalesAgency");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MerchantFee", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "RelatedMerchant")
                        .WithMany("Fees")
                        .HasForeignKey("RelatedMerchantId");

                    b.Navigation("RelatedMerchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.MerchantFundsReserveConfiguration", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.FundsReserveConfiguration", "FundsReserveConfiguration")
                        .WithMany()
                        .HasForeignKey("FundsReserveConfigurationId");

                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "Merchant")
                        .WithMany("FundsReserveConfigurations")
                        .HasForeignKey("MerchantId");

                    b.Navigation("FundsReserveConfiguration");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Notification", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Owner", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("FlexCharge.Merchants.Entities.Application", null)
                        .WithMany("Owners")
                        .HasForeignKey("ApplicationId");

                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", null)
                        .WithMany("Owners")
                        .HasForeignKey("MerchantId");

                    b.Navigation("Address");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Partner", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "Contact")
                        .WithMany()
                        .HasForeignKey("ContactId");

                    b.Navigation("Address");

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.PartnerFee", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Partner", "RelatedPartner")
                        .WithMany("PartnerFees")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RelatedPartner");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Report", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "RelatedMerchant")
                        .WithMany("Reports")
                        .HasForeignKey("RelatedMerchantId");

                    b.Navigation("RelatedMerchant");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SalesAgent", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.SalesAgency", "Agency")
                        .WithMany("SalesAgents")
                        .HasForeignKey("AgencyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "Contact")
                        .WithMany()
                        .HasForeignKey("ContactId");

                    b.Navigation("Agency");

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Site", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "DeveloperContact")
                        .WithMany()
                        .HasForeignKey("DeveloperContactId");

                    b.HasOne("FlexCharge.Merchants.Entities.Merchant", "Merchant")
                        .WithMany("Sites")
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FlexCharge.Merchants.Entities.Contact", "SupportContact")
                        .WithMany()
                        .HasForeignKey("SupportContactId");

                    b.Navigation("DeveloperContact");

                    b.Navigation("Merchant");

                    b.Navigation("SupportContact");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SiteCandidate", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Application", null)
                        .WithMany("Sites")
                        .HasForeignKey("ApplicationId");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SiteWhitelistedUrl", b =>
                {
                    b.HasOne("FlexCharge.Merchants.Entities.Site", "Site")
                        .WithMany("WhitelistedUrls")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Account", b =>
                {
                    b.Navigation("Applications");

                    b.Navigation("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Application", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Documents");

                    b.Navigation("Fees");

                    b.Navigation("Owners");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Merchant", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Documents");

                    b.Navigation("Fees");

                    b.Navigation("FundsReserveConfigurations");

                    b.Navigation("Owners");

                    b.Navigation("Reports");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Partner", b =>
                {
                    b.Navigation("CommissionsConfigurations");

                    b.Navigation("FeeConfigurations");

                    b.Navigation("PartnerFees");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SalesAgency", b =>
                {
                    b.Navigation("SalesAgents");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SalesAgent", b =>
                {
                    b.Navigation("CommissionsConfigurations");
                });

            modelBuilder.Entity("FlexCharge.Merchants.Entities.Site", b =>
                {
                    b.Navigation("WhitelistedUrls");
                });
#pragma warning restore 612, 618
        }
    }
}
