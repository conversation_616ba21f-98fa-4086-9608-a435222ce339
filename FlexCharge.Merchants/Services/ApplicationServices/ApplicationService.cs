using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.ServiceModel.Channels;
using System.Threading.Tasks;
using Amazon.SimpleNotificationService.Model;
using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.Response;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Orders;
using FlexCharge.Common.Shared.Partners;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Telemetry;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using FlexCharge.Contracts;
using FlexCharge.Merchants.DTO.Public;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Merchant = FlexCharge.Merchants.Entities.Merchant;


namespace FlexCharge.Merchants.Services.ApplicationServices;

public class ApplicationService : IApplicationService
{
    private readonly IHttpContextAccessor _contextAccessor;
    private PostgreSQLDbContext _dbContext { get; set; }
    private readonly IMapper _mapper;
    private readonly IEmailSender _emailSender;
    private readonly IPublishEndpoint _publisher;
    private readonly IOptions<LogoImageValidationOptions> _logoImageValidationOptions;
    private readonly ICloudStorage _cloudStorage;
    private readonly IConfiguration _configuration;
    private readonly IOptions<DocumentUploadOptions> _documentUploadOptions;
    private readonly IUrlShortenerService _urlShortenerService;
    private readonly IHttpContextAccessor _httpContextAccessor;


    public ApplicationService(PostgreSQLDbContext dbContext,
        IMapper mapper,
        IPublishEndpoint publisher, IHttpContextAccessor contextAccessor,
        IOptions<LogoImageValidationOptions> logoImageValidationOptions,
        ICloudStorage cloudStorage, IEmailSender emailSender, IConfiguration configuration,
        IOptions<DocumentUploadOptions> documentUploadOptions, IUrlShortenerService urlShortenerService,
        IHttpContextAccessor httpContextAccessor)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _publisher = publisher;
        _contextAccessor = contextAccessor;
        _logoImageValidationOptions = logoImageValidationOptions;
        _cloudStorage = cloudStorage;
        _emailSender = emailSender;
        _configuration = configuration;
        _documentUploadOptions = documentUploadOptions;
        _urlShortenerService = urlShortenerService;
        _httpContextAccessor = httpContextAccessor;
    }


    public async Task<RiskResponseDTO> RiskUpdateStatusAsync(RiskAssessmentRequestDTO payload, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Request(payload)
            .Baggage("ApplicationId", payload.ApplicationId)
            .Baggage("partnerId", partnerId)
            .LogEnterAndExit();

        try
        {
            var response = new RiskResponseDTO();

            var application = await GetApplication(payload.ApplicationId, partnerId);
            var meta = JsonConvert.SerializeObject(application);

            var oldStatus = application.RiskStatus;

            //update state
            application.RiskStatus = payload.Type.ToString();
            application.RiskCategory = payload.RiskCategory.ToString();

            _dbContext.Applications.Update(application);
            await _dbContext.SaveChangesAsync();

            await ApplyApplicationActivity(application.Id, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Risk Assessment",
                Activity = nameof(payload.Type),
                Note = payload.Comment,
                Meta = meta,
                UpdateMessage = $"Updated risk status: {oldStatus} => {payload.Type}"
            });

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error while updating risk status");

            await ApplyApplicationActivity(payload.ApplicationId, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Risk Assessment",
                Activity = "Risk Status Update Failed",
                UpdateMessage = "Failed to update risk status",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task<OperationAssessmentResponseDTO> OperationUpdateStatusAsync(OperationAssessmentRequestDTO payload,
        Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Request(payload)
            .Baggage("ApplicationId", payload.ApplicationId)
            .Baggage("partnerId", partnerId)
            .LogEnterAndExit();

        try
        {
            var response = new OperationAssessmentResponseDTO();
            var application = await GetApplication(payload.ApplicationId, partnerId);
            var meta = JsonConvert.SerializeObject(application);

            var oldStatus = application.OperationsStatus;

            application.OperationsStatus = payload.OperationsStatus.ToString();

            _dbContext.Applications.Update(application);
            await _dbContext.SaveChangesAsync();

            await ApplyApplicationActivity(application.Id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Operations Assessment",
                Activity = nameof(payload.OperationsStatus),
                Note = payload.Comment,
                Meta = meta,
                UpdateMessage = $"Updated operations status: {oldStatus} => {payload.OperationsStatus}",
            });

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error while updating operation status");

            await ApplyApplicationActivity(payload.ApplicationId, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Operations Assessment",
                Activity = "Operations Status Update Failed",
                UpdateMessage = "Failed to update operations status",
                Note = e.Message,
            });

            throw;
        }
    }

    private async Task<Application> GetApplication(Guid id, Guid? partnerId = null)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", id)
            .Baggage("partnerId", partnerId)
            .LogEnterAndExit();

        try
        {
            var query = _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Where(x => x.Id == id)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x => x.Id == id && x.PartnerId == partnerId);
            }

            var application = await query.SingleOrDefaultAsync();

            if (application == null)
                throw new FlexChargeException($"Application not found for id:{id}");

            return application;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Error while getting application");

            throw;
        }
    }

    public async Task<ApplicationNewResponse> QuickSignupAsync(SignUpRequestDTO payload)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(payload).LogEnterAndExit();

        try
        {
            return EnvironmentHelper.IsInSandboxOrStagingOrDevelopment
                ? await QuickSandboxCreate(payload)
                : await QuickProductionCreate(payload);

            //Sandbox function
            async Task<ApplicationNewResponse> QuickSandboxCreate(SignUpRequestDTO payload)
            {
                // partnerId is required to create a new application
                // we use FlexFactor as the default partner for self sign up
                var partner = await _dbContext.Partners.SingleOrDefaultAsync(x => x.Name == "FlexFactor");

                if (partner == null)
                {
                    throw new FlexValidationException("Partner not found");
                }

                return await CreateAndConvertAsync(new ApplicationNewRequest()
                {
                    CustomerSupportInformation = new ApplicationCustomerSupportInformationDTO()
                    {
                        CustomerSupportName = payload.FirstName,
                        CustomerSupportEmail = payload.Email,
                        CustomerSupportPhone = payload.Phone, //Phone
                    },
                    Send = true,
                    StandardFeeSelected = true,
                    Urls = new List<string>() {payload.Urls},
                    PrimaryContact = new PrimaryContact
                    {
                        FirstName = payload.FirstName,
                        LastName = payload.LastName,
                        Email = payload.Email,
                        Phone = payload.Phone,
                    },
                    LegalEntityName = payload.LegalEntityName,
                    PartnerId = partner.Id,
                }, isStripeAppApplicant: false);
            }

            //Production function
            async Task<ApplicationNewResponse> QuickProductionCreate(SignUpRequestDTO payload)
            {
                throw new FlexValidationException("Self Sign up is not supported in production");
                return await CreateAndConvertAsync(new ApplicationNewRequest()
                {
                    Send = false,
                    StandardFeeSelected = true,
                    PrimaryContact = new PrimaryContact
                    {
                        FirstName = payload.FirstName,
                        LastName = payload.LastName,
                        Email = payload.Email,
                        Phone = payload.Phone,
                    },
                    LegalEntityName = payload.LegalEntityName,
                }, isStripeAppApplicant: false);
            }
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Information($"Validation Error: {ve.Message}");
            workspan.RecordException(ve);
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Error while creating a new application");

            throw;
        }
    }

    public async Task<ApplicationNewResponse> StripeQuickSignupAsync(StripeSignUpRequestDTO payload)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Request(payload)
            .LogEnterAndExit();

        try
        {
            //Get tenant/partnerID
            // need to assign a partner so apiClient can be created with a proper partner 
            var partnerId = await _dbContext.Partners
                .Where(x => x.Name == "FlexFactor" && x.Type == nameof(PartnerType.Whitelabel))
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            var applicationResponse = await CreateAndConvertAsync(new ApplicationNewRequest()
            {
                CustomerSupportInformation = new ApplicationCustomerSupportInformationDTO()
                {
                },
                PartnerId = partnerId,
                Send = true,
                StandardFeeSelected = true,
                Urls = new List<string>() {payload.Urls},
                PrimaryContact = new PrimaryContact
                {
                    FirstName = payload.FirstName,
                    LastName = payload.LastName,
                    Email = payload.Email,
                    Phone = payload.Phone,
                },
                LegalEntityName = payload.LegalEntityName,
                Dba = payload.LegalEntityName,
                Password = payload.Password,
                IpAddress = payload.IpAddress,
                UserAgent = payload.UserAgent,
            }, isStripeAppApplicant: true);

            if (applicationResponse.Success)
            {
                await SendStripeMerchantCreatedNotificationAsync(applicationResponse.ApplicationId);
            }

            try
            {
                await ApplyApplicationActivity(applicationResponse.ApplicationId, new ApplicationActivity
                {
                    Department = "Application",
                    Category = "Applicant invitation",
                    Activity = "Invite Sent",
                    Note = $"Invite sent to {payload.Email}",
                });
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Error while creating activity - Application Send Invite");
            }

            return applicationResponse;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Information($"Validation Error: {ve.Message}");
            workspan.RecordException(ve);
            throw;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    // Only used for quick signup (sandbox) and Stripe
    public async Task<ApplicationNewResponse> CreateAndConvertAsync(ApplicationNewRequest payload,
        bool isStripeAppApplicant)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(payload).LogEnterAndExit();

        var response = new ApplicationNewResponse();

        try
        {
            var emailAlreadyExist = await _dbContext.Applications.Include(x => x.PrimaryContact)
                .AnyAsync(x =>
                    x.PrimaryContact.Email == payload.PrimaryContact.Email &&
                    x.Status != ApplicationStatus.CANCELLED);

            if (emailAlreadyExist)
            {
                throw new FlexValidationException("PrimaryContact.Email",
                    "An email already exist");
            }


            if (payload.AccountId != null && payload.AccountId != Guid.Empty)
            {
                var account = await _dbContext.Accounts.FindAsync(payload.AccountId);
                if (account == null)
                {
                    throw new FlexValidationException("AccountId",
                        "Account cannot be found, either select a valid account or keep it empty and a new account will be created");
                }
            }
            else
            {
                var account = _dbContext.Accounts.Add(new Account()
                {
                    Name = payload.LegalEntityName,
                });

                if (payload.PartnerId != null)
                {
                    account.Entity.PartnerId = payload.PartnerId;
                }

                if (payload.IntegrationPartnerId != null)
                {
                    account.Entity.IntegrationPartnerId = payload.IntegrationPartnerId;
                }

                payload.AccountId = account.Entity.Id;
            }

            var mapped = _mapper.Map<Application>(payload);

            if (payload.StandardFeeSelected)
            {
                mapped.Fees.Clear();

                // var fees = _context.FeeConfigurations.Where(x => x.IsStandard);

                var partner = await _dbContext.Partners
                    .Include(x => x.FeeConfigurations)
                    .SingleOrDefaultAsync(x => x.Id == payload.PartnerId);

                if (partner != null)
                {
                    var fees = partner.FeeConfigurations.Where(x => x.IsStandard);

                    foreach (var fee in fees)
                    {
                        mapped.Fees.Add(new ApplicationFee
                        {
                            Name = fee.Name,
                            Type = fee.Type,
                            ChargeType = fee.ChargeType,
                            Amount = fee.Amount,
                            IsStandard = true
                        });
                    }
                }
            }


            if (payload.Owners != null)
            {
                var sumOwnership = payload.Owners.Sum(x => x.PercentOwnership);

                if (sumOwnership > 100)
                {
                    response.AddError(
                        $"The percentage cannot exceed 100",
                        "PercentOwnership",
                        "general");
                    return response;
                }

                var newOwners = new List<Owner>();
                foreach (var owner in payload.Owners)
                {
                    newOwners.Add(new Owner()
                    {
                        Title = owner.Title,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Email = owner.Email,
                        Phone = owner.Phone,
                        DateOfBirth = owner.DateOfBirth,
                        PrincipalType = owner.PrincipalType,
                        IndividualFormOfID = owner.IndividualFormOfID,
                        IdentificationNumber = owner.IdentificationNumber,
                        PercentOwnership = owner.PercentOwnership,
                        Address = new Address()
                        {
                            Line1 = owner.Address?.Line1,
                            Line2 = owner.Address?.Line2,
                            City = owner.Address?.City,
                            State = owner.Address?.State,
                            ZipCode = owner.Address?.ZipCode,
                            Country = owner.Address?.Country
                        }
                    });
                }

                mapped.Owners = newOwners;
            }

            if (payload.Urls != null)
            {
                var sites = new List<SiteCandidate>();

                foreach (var url in payload.Urls)
                {
                    sites.Add(new SiteCandidate()
                    {
                        Url = url
                    });
                }

                mapped.Sites = sites;
            }

            // Needed to convert applicant to merchant 
            mapped.SkipRiskAssessment = true;

            var newApplication = await _dbContext.Applications.AddAsync(mapped);

            if (!string.IsNullOrWhiteSpace(payload.LogoFile))
            {
                var application = (Application) newApplication.Entity;
                var merchantId = application.Id;
                mapped.LogoUrl = await StoreLogoImageAsync(merchantId, response, payload.LogoFile);

                if (!response.Success) return response;
            }

            await _dbContext.SaveChangesAsync();

            if (newApplication.Entity.Id == Guid.Empty)
                response.AddError("Unable to create application");

            response.ApplicationId = newApplication.Entity.Id;

            await _publisher.Publish(new ApplicationCreatedEvent
            {
                Id = newApplication.Entity.Id,
                Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                Email = payload.PrimaryContact.Email,
                Mid = response.ApplicationId,
                Pid = payload.PartnerId,
                IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                FirstName = payload.PrimaryContact.FirstName,
                LastName = payload.PrimaryContact.LastName,
                Aid = payload.AccountId ?? Guid.Empty,
                LegalEntityName = payload.LegalEntityName,
            });

            if (payload.Send)
            {
                await _publisher.Publish(new ApplicationInviteRequestedEvent
                {
                    Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                    Phone = payload.PrimaryContact.Phone,
                    Email = payload.PrimaryContact.Email,
                    Mid = response.ApplicationId,
                    Pid = payload.PartnerId,
                    IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                    FirstName = payload.PrimaryContact.FirstName,
                    LastName = payload.PrimaryContact.LastName,
                    Group = SuperAdminGroups.MERCHANT_ADMIN,
                    Aid = payload.AccountId ?? Guid.Empty,
                    MerchantState = "active", //set as active because its a stripe signup
                    Password = payload.Password,
                });

                try
                {
                    await ApplyApplicationActivity(mapped.Id, new ApplicationActivity
                    {
                        Department = "Application",
                        Category = "Application Send Invite",
                        Activity = "Invite Sent",
                        Note = $"Invite sent to {payload.PrimaryContact.Email}",
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Error while creating activity - Application Send Invite");
                }
            }

            workspan.Log.Information(
                $"IN: ApplicationService => Publishing ApplicationCreatedEvent for {newApplication.Entity.Id},To Email: {payload.PrimaryContact.FirstName}");


            mapped.Status = ApplicationStatus.SUBMITTED;
            mapped.AgreeToTerms = DateTime.Now.ToUniversalTime();

            _dbContext.Applications.Update(mapped);
            await _dbContext.SaveChangesAsync();

            var convertOptions = new ApplicationConvertOptions
            {
                IsStripeAppMerchant = isStripeAppApplicant,
                IsActive = true
            };
            await ConvertAsync(newApplication.Entity.Id, payload.PartnerId, convertOptions);

            return response;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Error(ve, "FlexChargeValidationException");
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to create a new application ");
            throw;
        }
    }

    public async Task<ApplicationNewResponse> AdminCreateAsync(AdminApplicationCreateRequest payload)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Request(payload)
            .LogEnterAndExit();

        var newApplication = new Application();
        var response = new ApplicationNewResponse();

        try
        {
            var emailAlreadyExist = await _dbContext.Applications.Include(x => x.PrimaryContact)
                .AnyAsync(x =>
                    x.PrimaryContact.Email == payload.PrimaryContact.Email &&
                    x.Status != ApplicationStatus.CANCELLED);

            if (emailAlreadyExist)
            {
                throw new FlexValidationException("PrimaryContact.Email",
                    "An email already exist");
            }

            if (payload.ContractAlreadySigned.HasValue)
            {
                newApplication.ContractAlreadySigned = payload.ContractAlreadySigned.Value;
            }

            if (payload.IntegrationPartnerParticipateSale.HasValue)
            {
                newApplication.IntegrationPartnerParticipateSale = payload.IntegrationPartnerParticipateSale.Value;
            }

            #region PrimaryContact

            newApplication.PrimaryContact = new Contact
            {
                FirstName = payload.PrimaryContact.FirstName,
                LastName = payload.PrimaryContact.LastName,
                Email = payload.PrimaryContact.Email,
                Phone = payload.PrimaryContact.Phone,
            };

            #endregion

            #region DeveloperContact

            if (
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperEmail) &&
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperLastName) &&
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperFirstName) &&
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperPhone)
            )
            {
                newApplication.DeveloperContact = new Contact
                {
                    FirstName = payload.DeveloperContact.DeveloperFirstName,
                    LastName = payload.DeveloperContact.DeveloperLastName,
                    Email = payload.DeveloperContact.DeveloperEmail,
                    Phone = payload.DeveloperContact.DeveloperPhone,
                };
            }

            #endregion

            #region CustomerSupportInfo

            newApplication.CustomerSupportName =
                payload.CustomerSupportInformation?.CustomerSupportName ?? string.Empty;
            newApplication.CustomerSupportEmail =
                payload.CustomerSupportInformation?.CustomerSupportEmail ?? string.Empty;
            newApplication.CustomerSupportLink =
                payload.CustomerSupportInformation?.CustomerSupportLink ?? string.Empty;
            newApplication.CustomerSupportPhone =
                payload.CustomerSupportInformation?.CustomerSupportPhone ?? string.Empty;

            #endregion

            #region ProductInfo

            newApplication.TrafficAcquisitionChannels = new List<string>();

            if (payload.ProductInfo != null)
            {
                newApplication.Healthcare = payload.ProductInfo.Healthcare;
                newApplication.Regulated = payload.ProductInfo.Regulated;
            }

            #endregion

            #region TransactionInfo

            newApplication.ChargebackPercentage = payload.TransactionInformation?.ChargebackPercentage;
            newApplication.AvgDeclineRate = payload.TransactionInformation?.AvgDeclineRate;
            newApplication.AvgDisputeRate = payload.TransactionInformation?.AvgDisputeRate;
            newApplication.AvgMonthlySales = payload.TransactionInformation?.AvgSales;
            newApplication.AvgTransactionAmount = payload.TransactionInformation?.AvgTransactionAmount;
            newApplication.ReturnsPercent = payload.TransactionInformation?.ReturnsPercent;

            #endregion

            #region CompanyInfo

            newApplication.BusinessEstablishedDate = payload.BusinessEstablishedDate;
            newApplication.Dba = payload.Dba;
            newApplication.Description = payload.Description;
            newApplication.Descriptor = payload.Descriptor;
            newApplication.EcommercePlatform = payload.EcommercePlatform;
            newApplication.IsSiteValidated = payload.IsSiteValidated;
            newApplication.LegalEntityCountry = payload.LegalEntityCountry;
            newApplication.LegalEntityName = payload.LegalEntityName;
            newApplication.CompanyName = payload.LegalEntityName;
            newApplication.Mcc = payload.Mcc;
            newApplication.Pcidss = payload.Pcidss;
            newApplication.TaxId = payload.TaxId;
            newApplication.Type = payload.Type;
            newApplication.SpecialTerms = payload.SpecialTerms;
            newApplication.AnnualSalesVolume = payload.AnnualSalesVolume;

            #endregion

            #region AccountInfo

            newApplication.PartnerId = payload.PartnerId;
            newApplication.IntegrationPartnerId = payload.IntegrationPartnerId;

            newApplication.IntegrationTier = payload.IntegrationTier;
            newApplication.AccountManagerId = payload.AccountManagerId;
            newApplication.AccountId = payload.AccountId;
            newApplication.CrmId = payload.CrmId;

            if (payload.SalesAgencyId != Guid.Empty)
            {
                newApplication.SalesAgencyId = payload.SalesAgencyId;
            }

            if (payload.AccountId != null)
            {
                var account = await _dbContext.Accounts.FindAsync(payload.AccountId);
                if (account == null)
                {
                    throw new FlexValidationException("Account does not exist");
                }
            }
            else
            {
                var account = _dbContext.Accounts.Add(new Account()
                {
                    Name = payload.LegalEntityName,
                });
                if (payload.PartnerId != null)
                {
                    account.Entity.PartnerId = payload.PartnerId;
                }

                if (payload.IntegrationPartnerId != null)
                {
                    account.Entity.IntegrationPartnerId = payload.IntegrationPartnerId;
                }

                newApplication.AccountId = account.Entity.Id;
            }

            #endregion

            #region BusinessModelInfo

            if (payload.BusinessModel != null)
            {
                newApplication.SitesCount = payload.BusinessModel?.UrlsCount;

                if (payload.BusinessModel?.Urls != null)
                {
                    var sites = new List<SiteCandidate>();

                    foreach (var url in payload.BusinessModel.Urls)
                    {
                        sites.Add(new SiteCandidate()
                        {
                            Url = url
                        });
                    }

                    newApplication.Sites = sites;
                }
            }

            #endregion

            #region OwnerInfo

            if (payload.Owners != null)
            {
                var sumOwnership = payload.Owners.Sum(x => x.PercentOwnership);

                if (sumOwnership > 100)
                {
                    throw new FlexValidationException("Owners cannot be more than 100%");
                }

                var newOwners = new List<Owner>();
                foreach (var owner in payload.Owners)
                {
                    newOwners.Add(new Owner()
                    {
                        Title = owner.Title,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Email = owner.Email,
                        Phone = owner.Phone,
                        DateOfBirth = owner.DateOfBirth,
                        PrincipalType = owner.PrincipalType,
                        IndividualFormOfID = owner.IndividualFormOfID,
                        IdentificationNumber = owner.IdentificationNumber,
                        PercentOwnership = owner.PercentOwnership,
                        Address = new Address()
                        {
                            Line1 = owner.Address?.Line1,
                            Line2 = owner.Address?.Line2,
                            City = owner.Address?.City,
                            State = owner.Address?.State,
                            ZipCode = owner.Address?.ZipCode,
                            Country = owner.Address?.Country
                        }
                    });
                }

                newApplication.Owners = newOwners;
            }

            #endregion

            #region BankInfo

            if (payload.BankAccountInformation != null)
            {
                newApplication.BankName = payload.BankAccountInformation.BankName;
                newApplication.DdaType = payload.BankAccountInformation.DdaType;
                newApplication.RoutingNumber = payload.BankAccountInformation.RoutingNumber;
                newApplication.AccountNumber = payload.BankAccountInformation.AccountNumber;
                newApplication.RoutingType = payload.BankAccountInformation.RoutingType;
            }

            #endregion

            #region AdressInfo

            newApplication.Address = new Address
            {
                Country = payload.Address?.Country,
                Line1 = payload.Address?.Line1,
                Line2 = payload.Address?.Line2,
                City = payload.Address?.City,
                ZipCode = payload.Address?.ZipCode,
                State = payload.Address?.State,
            };

            #endregion

            #region FeesInfo

            if (payload.StandardFeeSelected)
            {
                var fees = await _dbContext.FeeConfigurations
                    .Where(x => x.PartnerId == payload.PartnerId && x.IsStandard)
                    .ToListAsync();

                foreach (var fee in fees)
                {
                    newApplication.Fees.Add(new ApplicationFee
                    {
                        Name = fee.Name,
                        Type = fee.Type,
                        ChargeType = fee.ChargeType,
                        Amount = fee.Amount,
                        IsStandard = true
                    });
                }
            }
            else
            {
                newApplication.Fees = new List<ApplicationFee>();

                foreach (var fee in payload.Fees.ToList())
                {
                    newApplication.Fees.Add(new ApplicationFee
                    {
                        Name = fee.Name,
                        Type = fee.Type,
                        ChargeType = fee.ChargeType,
                        Amount = fee.Amount,
                        MinimumFeeAmount = fee.MinimumFeeAmount,
                        IsStandard = fee.IsStandard,
                    });
                }
            }

            #endregion

            await _dbContext.Applications.AddAsync(newApplication);

            #region LogoUrl

            if (!string.IsNullOrWhiteSpace(payload.LogoFile))
            {
                var merchantId = newApplication.Id;
                newApplication.LogoUrl = await StoreLogoImageAsync(merchantId, response, payload.LogoFile);

                if (!response.Success) return response;
            }

            #endregion

            await _dbContext.SaveChangesAsync();


            response.ApplicationId = newApplication.Id;

            await _publisher.Publish(new ApplicationCreatedEvent
            {
                Id = newApplication.Id,
                Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                Email = payload.PrimaryContact.Email,
                Mid = response.ApplicationId,
                Pid = payload.PartnerId,
                IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                FirstName = payload.PrimaryContact.FirstName,
                LastName = payload.PrimaryContact.LastName,
                Aid = payload.AccountId ?? Guid.Empty,
                LegalEntityName = payload.LegalEntityName
            });

            if (payload.Send)
            {
                await _publisher.Publish(new ApplicationInviteRequestedEvent
                {
                    Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                    Phone = payload.PrimaryContact.Phone,
                    Email = payload.PrimaryContact.Email,
                    Mid = response.ApplicationId,
                    Pid = payload.PartnerId,
                    IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                    FirstName = payload.PrimaryContact.FirstName,
                    LastName = payload.PrimaryContact.LastName,
                    Group = SuperAdminGroups.MERCHANT_ADMIN,
                    Aid = payload.AccountId ?? Guid.Empty,
                    MerchantState = "boarding",
                });

                try
                {
                    await ApplyApplicationActivity(newApplication.Id, new ApplicationActivity
                    {
                        Department = "Application",
                        Category = "Applicant invitation",
                        Activity = "Invite Sent",
                        UpdateMessage = $"Invite sent to {payload.PrimaryContact.Email}",
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Error while sending email");
                }
            }


            workspan.Log.Information(
                $"ApplicationService => Publishing ApplicationCreatedEvent for {newApplication.Id},To Email: {payload.PrimaryContact.FirstName}");

            return response;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Error(ve, "FlexChargeValidationException");
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to create a new application ");
            throw;
        }
    }

    public async Task StoreDocumentAsync(Guid applicationId, IFormFile file,
        bool sendNotification = true)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", applicationId)
            .Baggage("Mid", applicationId)
            .Baggage("FileName", file.FileName)
            .LogEnterAndExit();

        try
        {
            var application = await _dbContext.Applications
                .Include(x => x.Documents)
                .Include(x => x.Partner)
                .ThenInclude(x => x.Address).Include(application => application.PrimaryContact)
                .SingleOrDefaultAsync(x => x.Id == applicationId);

            if (application == null)
            {
                throw new FlexChargeException($"Application not found for id: {applicationId}");
            }

            await StoreDocumentAsync(application, file, sendNotification);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public async Task StoreDocumentAsync(Application application, IFormFile file,
        bool sendNotification = true)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", application.Id)
            .Baggage("Mid", application.Id)
            .Baggage("FileName", file.FileName)
            .LogEnterAndExit();

        using var reader = new StreamReader(file.OpenReadStream());
        var stream = reader.BaseStream;
        try
        {
            await FileValidator.ValidateAsync(stream, file.FileName, new FileValidationOptions
            {
                MaxFileSize = 10 * 1024 * 1024,
                MinFileSize = 1,
                Types = new List<string>
                    {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".csv", ".Jpeg", ".jpg", ".jpeg", ".png", ".PNG"}
            });

            await StoreDocumentAsync(application, file.FileName, file.ContentType, stream, sendNotification);
        }
        catch (FileValidationException e)
        {
            await ApplyApplicationActivity(application.Id, new ApplicationActivity
            {
                Category = "Application",
                Activity = "Document Upload Failed",
                UpdateMessage = e.Message,
            });
            throw new FlexValidationException("Document", e.Message);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public async Task StoreDocumentAsync(Guid applicationId, string fileName, string contentType, Stream stream,
        bool sendNotification = true)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", applicationId)
            .Baggage("Mid", applicationId)
            .Baggage("FileName", fileName)
            .Baggage("ContentType", contentType)
            .LogEnterAndExit();

        try
        {
            var application = await _dbContext.Applications
                .Include(x => x.Documents)
                .Include(x => x.Partner)
                .ThenInclude(x => x.Address).Include(application => application.PrimaryContact)
                .SingleOrDefaultAsync(x => x.Id == applicationId);

            if (application == null)
            {
                throw new FlexChargeException($"Application not found for id: {applicationId}");
            }

            await StoreDocumentAsync(application, fileName, contentType, stream, sendNotification);
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public async Task StoreDocumentAsync(Application application, string fileName, string contentType, Stream stream,
        bool sendNotification = true)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", application.Id)
            .Baggage("Mid", application.Id)
            .Baggage("FileName", fileName)
            .Baggage("ContentType", contentType)
            .LogEnterAndExit();

        try
        {
            //arn:aws:s3:::flex-staging-static-assets
            var root = Environment.GetEnvironmentVariable("AWS_S3_STATIC_FILES_BUCKET");
            var folder = "documents";

            var documentRandomId = Guid.NewGuid();

            var destination = $"{application.Id}/{folder}";

            await _cloudStorage.CreateFolderIfMissingAsync(root, destination);
            await _cloudStorage.UploadFileAsync(stream,
                root,
                $"{documentRandomId}_{fileName}",
                folder: destination,
                allowPublicAccess: false);

            if (sendNotification)
            {
                try
                {
                    if (application.Status == ApplicationStatus.SUBMITTED)
                    {
                        await DocumentUploadNotificationAsync(application, fileName);
                    }
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Failed to send document upload notification");
                }
            }

            try
            {
                await ApplyApplicationActivity(application.Id, new ApplicationActivity
                {
                    Category = "Application",
                    Activity = "Document Uploaded",
                    UpdateMessage = $"Document {documentRandomId}_{fileName} uploaded",
                });
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Failed create activity while saving document");
            }

            var documentPath = $"{root}/{destination}/{documentRandomId}_{fileName}";
            var documentFileName = $"{documentRandomId}_{fileName}";
            var documentId = documentRandomId.ToString();

            workspan
                .Tag("DocumentId", documentId)
                .Tag("FileName", documentFileName)
                .Tag("FullPath", documentPath)
                .Log.Information($"Document stored successfully");


            var document = new Document
            {
                Name = documentFileName,
                Path = documentPath,
                Type = contentType,
                ApplicationId = application.Id,
            };

            _dbContext.Documents.Add(document);
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            throw;
        }
    }

    public async Task<string> GetDocumentPublicUrlAsync(Guid documentId)
    {
        try
        {
            var document = await _dbContext.Documents.FindAsync(documentId);

            if (document == null)
            {
                throw new FlexValidationException("Document", "Document not found");
            }

            var root = Environment.GetEnvironmentVariable("AWS_S3_STATIC_FILES_BUCKET");
            var folder = "documents";

            var destination = $"{document.ApplicationId}/{folder}";

            var realUrl = await _cloudStorage.CreateExpiringPublicFileUrl(DateTime.UtcNow.AddHours(1),
                root, document.Name,
                destination);

            return realUrl;
        }
        catch (Exception e)
        {
            throw;
        }
    }

    public async Task<ApplicationNewResponse> AdminUpdateAsync(AdminApplicationUpdateRequest payload, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Request(payload)
            .LogEnterAndExit();

        var entity = await _dbContext.Applications
            .Include(x => x.Partner)
            .Include(x => x.IntegrationPartner)
            .Include(x => x.Address)
            .Include(x => x.PrimaryContact)
            .Include(x => x.DeveloperContact)
            .Include(x => x.Fees)
            .Include(x => x.SalesAgency)
            .Include(x => x.Owners).ThenInclude(owner => owner.Address)
            .Include(x => x.Sites)
            .Include(x => x.DeveloperContact)
            .Include(x => x.Documents) // Include Documents navigation property
            .SingleOrDefaultAsync(x => x.Id == payload.Id && x.Status != ApplicationStatus.APPROVED);

        if (entity == null || partnerId != null && entity.PartnerId != partnerId)
        {
            throw new FlexValidationException("Application not found");
        }

        var response = new ApplicationNewResponse();

        try
        {
            var emailAlreadyExist = await _dbContext.Applications.Include(x => x.PrimaryContact)
                .AnyAsync(x =>
                    x.PrimaryContact.Email == payload.PrimaryContact.Email &&
                    x.Status != ApplicationStatus.CANCELLED);

            if (!emailAlreadyExist)
            {
                throw new FlexValidationException
                    ("An Email Address can not Be modified");
            }

            if (payload.ContractAlreadySigned.HasValue)
            {
                entity.ContractAlreadySigned = payload.ContractAlreadySigned.Value;
            }

            if (payload.IntegrationPartnerParticipateSale.HasValue)
            {
                entity.IntegrationPartnerParticipateSale = payload.IntegrationPartnerParticipateSale.Value;
            }

            #region PrimaryContact

            entity.PrimaryContact.FirstName = payload.PrimaryContact.FirstName;
            entity.PrimaryContact.LastName = payload.PrimaryContact.LastName;
            // entity.PrimaryContact.Email = payload.PrimaryContact.Email;
            entity.PrimaryContact.Phone = payload.PrimaryContact.Phone;

            #endregion

            #region DeveloperContact

            if (
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperEmail) &&
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperLastName) &&
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperFirstName) &&
                !string.IsNullOrWhiteSpace(payload.DeveloperContact?.DeveloperPhone)
            )
            {
                entity.DeveloperContact = new Contact
                {
                    FirstName = payload.DeveloperContact.DeveloperFirstName,
                    LastName = payload.DeveloperContact.DeveloperLastName,
                    Email = payload.DeveloperContact.DeveloperEmail,
                    Phone = payload.DeveloperContact.DeveloperPhone,
                };
            }

            #endregion

            #region CustomerSupportInfo

            entity.CustomerSupportName = payload.CustomerSupportInformation?.CustomerSupportName ?? string.Empty;
            entity.CustomerSupportEmail = payload.CustomerSupportInformation?.CustomerSupportEmail ?? string.Empty;
            entity.CustomerSupportLink = payload.CustomerSupportInformation?.CustomerSupportLink ?? string.Empty;
            entity.CustomerSupportPhone = payload.CustomerSupportInformation?.CustomerSupportPhone ?? string.Empty;

            #endregion

            #region ProductInfo

            if (entity.TrafficAcquisitionChannels != null)
                entity.TrafficAcquisitionChannels.Clear();
            else
                entity.TrafficAcquisitionChannels = new List<string>();

            if (payload.ProductInfo != null)
            {
                entity.Healthcare = payload.ProductInfo.Healthcare;
                entity.Regulated = payload.ProductInfo.Regulated;
            }

            #endregion

            #region TransactionInfo

            entity.ChargebackPercentage = payload.TransactionInformation?.ChargebackPercentage ?? string.Empty;
            entity.AvgDeclineRate = payload.TransactionInformation?.AvgDeclineRate ?? string.Empty;
            entity.AvgDisputeRate = payload.TransactionInformation?.AvgDisputeRate ?? string.Empty;
            entity.AvgMonthlySales = payload.TransactionInformation?.AvgSales ?? string.Empty;
            entity.AvgTransactionAmount = payload.TransactionInformation?.AvgTransactionAmount ?? string.Empty;
            entity.ReturnsPercent = payload.TransactionInformation?.ReturnsPercent ?? string.Empty;

            #endregion

            #region CompanyInfo

            entity.LegalEntityName = payload.LegalEntityName;
            entity.CompanyName = payload.LegalEntityName;
            entity.LegalEntityCountry = payload.LegalEntityCountry;
            entity.Dba = payload.Dba;
            entity.TaxId = payload.TaxId;
            entity.Mcc = payload.Mcc;
            entity.Descriptor = payload.Descriptor;
            entity.Type = payload.Type;
            entity.BusinessEstablishedDate = payload.BusinessEstablishedDate;
            entity.EcommercePlatform = payload.EcommercePlatform;
            entity.Pcidss = payload.Pcidss;
            entity.Description = payload.Description;
            entity.SpecialTerms = payload.SpecialTerms;
            entity.IsSiteValidated = payload.IsSiteValidated;
            entity.AnnualSalesVolume = payload.AnnualSalesVolume;

            #endregion

            #region AccountInfo

            entity.PartnerId = payload.PartnerId;
            entity.IntegrationPartnerId = payload.IntegrationPartnerId;

            entity.IntegrationTier = payload.IntegrationTier;
            entity.AccountManagerId = payload.AccountManagerId;
            entity.CrmId = payload.CrmId;

            if (payload.SalesAgencyId != Guid.Empty)
            {
                entity.SalesAgencyId = payload.SalesAgencyId;
            }


            if (payload.AccountId != null)
            {
                var account = await _dbContext.Accounts.FindAsync(payload.AccountId);
                if (account == null)
                {
                    throw new FlexValidationException("Account does not exist");
                }
            }
            else
            {
                var account = _dbContext.Accounts.Add(new Account()
                {
                    Name = payload.LegalEntityName,
                });
                if (payload.PartnerId != null)
                {
                    account.Entity.PartnerId = payload.PartnerId;
                }

                if (payload.IntegrationPartnerId != null)
                {
                    account.Entity.IntegrationPartnerId = payload.IntegrationPartnerId;
                }

                entity.AccountId = account.Entity.Id;
            }

            #endregion

            #region BankInfo

            if (payload.BankAccountInformation != null)
            {
                entity.BankName = payload.BankAccountInformation.BankName;
                entity.DdaType = payload.BankAccountInformation.DdaType;
                entity.RoutingNumber = payload.BankAccountInformation.RoutingNumber;
                entity.AccountNumber = payload.BankAccountInformation.AccountNumber;
                entity.RoutingType = payload.BankAccountInformation.RoutingType;
            }

            #endregion

            #region AdressInfo

            if (entity.Address == null)
                entity.Address = new Address();
            entity.Address.Country = payload.Address?.Country;
            entity.Address.Line1 = payload.Address?.Line1;
            entity.Address.Line2 = payload.Address?.Line2;
            entity.Address.City = payload.Address?.City;
            entity.Address.ZipCode = payload.Address?.ZipCode;
            entity.Address.State = payload.Address?.State;

            #endregion

            #region BusinessModelInfo

            if (payload.BusinessModel != null)
            {
                entity.SitesCount = payload.BusinessModel?.UrlsCount;

                entity.Sites.Clear();
                if (payload.BusinessModel?.Urls != null)
                {
                    foreach (var url in payload.BusinessModel.Urls)
                    {
                        entity.Sites.Add(new SiteCandidate()
                        {
                            Url = url
                        });
                    }
                }
            }

            #endregion

            #region OwnerInfo

            if (payload.Owners != null)
            {
                var sumOwnership = payload.Owners.Sum(x => x.PercentOwnership);

                if (sumOwnership > 100)
                {
                    throw new FlexValidationException("Owners cannot be more than 100%");
                }

                var newOwners = new List<Owner>();

                foreach (var owner in payload.Owners)
                {
                    var existingOwner = entity.Owners.FirstOrDefault(x => x.Id == owner.Id);
                    if (owner.Id == null || existingOwner == null)
                    {
                        newOwners.Add(new Owner()
                        {
                            Title = owner.Title,
                            FirstName = owner.FirstName,
                            LastName = owner.LastName,
                            Email = owner.Email,
                            Phone = owner.Phone,
                            DateOfBirth = owner.DateOfBirth,
                            PrincipalType = owner.PrincipalType,
                            IndividualFormOfID = owner.IndividualFormOfID,
                            IdentificationNumber = owner.IdentificationNumber,
                            PercentOwnership = owner.PercentOwnership,
                            Address = new Address()
                            {
                                Line1 = owner.Address?.Line1,
                                Line2 = owner.Address?.Line2,
                                City = owner.Address?.City,
                                State = owner.Address?.State,
                                ZipCode = owner.Address?.ZipCode,
                                Country = owner.Address?.Country
                            }
                        });
                    }
                    else
                    {
                        existingOwner.Title = owner.Title;
                        existingOwner.FirstName = owner.FirstName;
                        existingOwner.LastName = owner.LastName;
                        existingOwner.Email = owner.Email;
                        existingOwner.Phone = owner.Phone;
                        existingOwner.DateOfBirth = owner.DateOfBirth;
                        existingOwner.PrincipalType = owner.PrincipalType;
                        existingOwner.IndividualFormOfID = owner.IndividualFormOfID;
                        existingOwner.IdentificationNumber = owner.IdentificationNumber;
                        existingOwner.PercentOwnership = owner.PercentOwnership;
                        existingOwner.Address.Line1 = owner.Address?.Line1;
                        existingOwner.Address.Line2 = owner.Address?.Line2;
                        existingOwner.Address.City = owner.Address?.City;
                        existingOwner.Address.State = owner.Address?.State;
                        existingOwner.Address.ZipCode = owner.Address?.ZipCode;
                        existingOwner.Address.Country = owner.Address?.Country;

                        newOwners.Add(existingOwner);
                    }
                }

                entity.Owners = newOwners;
            }

            #endregion

            #region FeesInfo

            if (payload.Fees != null)
            {
                foreach (var fee in payload.Fees.ToList())
                {
                    var existingFee = entity.Fees.FirstOrDefault(x => x.Id == fee.Id);

                    if (existingFee == null)
                    {
                        entity.Fees.Add(new ApplicationFee
                        {
                            Name = fee.Name,
                            Type = fee.Type,
                            ChargeType = fee.ChargeType,
                            Amount = fee.Amount,
                            MinimumFeeAmount = fee.MinimumFeeAmount,
                            IsStandard = fee.IsStandard,
                        });
                    }
                    else
                    {
                        existingFee.Name = fee.Name;
                        existingFee.Type = fee.Type;
                        existingFee.ChargeType = fee.ChargeType;
                        existingFee.Amount = fee.Amount;
                        existingFee.MinimumFeeAmount = fee.MinimumFeeAmount;
                        existingFee.IsStandard = fee.IsStandard;
                    }
                }
            }

            #endregion

            if (!string.IsNullOrWhiteSpace(payload.LogoFile))
            {
                //overrides existing logo image in the cloud storage
                entity.LogoUrl = await StoreLogoImageAsync(payload.Id, response, payload.LogoFile);

                if (!response.Success) return response;
            }
            else if (payload.LogoFile == string.Empty)
            {
                entity.LogoUrl = null;
            }

            _dbContext.Applications.Update(entity);

            await _dbContext.SaveChangesAsync();

            response.ApplicationId = entity.Id;

            try
            {
                await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                {
                    Category = "Application",
                    Activity = "Application Updated",
                    Meta = JsonConvert.SerializeObject(new
                    {
                        applicationId = entity.Id,
                        applicationStatus = entity.Status.ToString(),
                        applicationLegalEntityName = entity.LegalEntityName
                    }),
                    UpdateMessage = $"Application {entity.LegalEntityName} Updated",
                });
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "Failed create activity while updating application");
            }

            if (payload.Send)
            {
                await _publisher.Publish(new ApplicationInviteRequestedEvent
                {
                    Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                    Phone = payload.PrimaryContact.Phone,
                    Email = payload.PrimaryContact.Email,
                    Mid = response.ApplicationId,
                    Pid = payload.PartnerId,
                    IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                    FirstName = payload.PrimaryContact.FirstName,
                    LastName = payload.PrimaryContact.LastName,
                    Group = SuperAdminGroups.MERCHANT_ADMIN,
                    Aid = entity.AccountId ?? Guid.Empty,
                    MerchantState = "boarding"
                });

                try
                {
                    await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                    {
                        Department = "Application",
                        Category = "Applicant invitation",
                        Activity = "Invite Sent",
                        UpdateMessage = $"Invite sent to {payload.PrimaryContact.Email}",
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Failed create activity after sending invite");
                }
            }

            workspan.Log.Information(
                $"IN: ApplicationService => Publishing ApplicationCreatedEvent for {entity.Id},To Email: {payload.PrimaryContact.FirstName}");

            return response;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Error(ve, "FlexChargeValidationException");

            await ApplyApplicationActivity(entity.Id, new ApplicationActivity
            {
                Department = "Application",
                Category = "Application",
                Activity = "Application Update Failed",
                UpdateMessage = $"Failed to update application {entity.LegalEntityName}",
                Note = ve.Message,
            });

            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to create a new application ");

            await ApplyApplicationActivity(entity.Id, new ApplicationActivity
            {
                Department = "Application",
                Category = "Application",
                Activity = "Application Update Failed",
                UpdateMessage = $"Failed to update application {entity.LegalEntityName}",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task<FullApplicationResponse> AdminGetApplicationByIdAsync(Guid id, Guid? integrationPartnerId)
    {
        var entity = await _dbContext.Applications
            .Include(x => x.Partner)
            .Include(x => x.IntegrationPartner)
            .Include(x => x.Address)
            .Include(x => x.PrimaryContact)
            .Include(x => x.DeveloperContact)
            .Include(x => x.Fees)
            .Include(x => x.SalesAgency)
            .Include(x => x.Owners).ThenInclude(owner => owner.Address)
            .Include(x => x.Sites)
            .Include(application => application.Documents)
            .SingleOrDefaultAsync(x => x.Id == id && x.Status != ApplicationStatus.APPROVED);

        if (entity == null)
            throw new FlexNotFoundException($"Application with Id {id} does not exist");

        if (integrationPartnerId != null && entity.IntegrationPartnerId != integrationPartnerId)
        {
            throw new FlexValidationException("Application not found");
        }

        var applicantDto = new AdminApplicationGetResponse();
        var response = new FullApplicationResponse();

        applicantDto.ContractAlreadySigned = entity.ContractAlreadySigned;
        applicantDto.IntegrationPartnerParticipateSale = entity.IntegrationPartnerParticipateSale;

        #region PriamaryContact

        applicantDto.PrimaryContact = new PrimaryContact()
        {
            FirstName = entity.PrimaryContact.FirstName,
            LastName = entity.PrimaryContact.LastName,
            Email = entity.PrimaryContact.Email,
            Phone = entity.PrimaryContact.Phone,
            UserCreated = entity.PrimaryContact.UserId != Guid.Empty,
            ConfirmedUser = entity.PrimaryContact.ConfirmedUser,
        };

        #endregion

        applicantDto.IsSiteValidated = entity.IsSiteValidated;

        applicantDto.Status = entity.Status;
        applicantDto.StatusName = entity.Status.ToString();

        #region AddressInfo

        applicantDto.Address = new AddressDTO()
        {
            Country = entity.Address?.Country,
            Line1 = entity.Address?.Line1,
            Line2 = entity.Address?.Line2,
            City = entity.Address?.City,
            ZipCode = entity.Address?.ZipCode,
            State = entity.Address?.State,
        };

        #endregion

        #region DeveloperContacrInfo

        applicantDto.DeveloperContact = new DeveloperContact()
        {
            DeveloperFirstName = entity.DeveloperContact?.FirstName,
            DeveloperLastName = entity.DeveloperContact?.LastName,
            DeveloperEmail = entity.DeveloperContact?.Email,
            DeveloperPhone = entity.DeveloperContact?.Phone
        };

        #endregion

        #region CustomerSupportInfo

        applicantDto.CustomerSupportInformation = new ApplicationCustomerSupportInformationDTO
        {
            CustomerSupportName = entity.CustomerSupportName,
            CustomerSupportEmail = entity.CustomerSupportEmail,
            CustomerSupportPhone = entity.CustomerSupportPhone,
            CustomerSupportLink = entity.CustomerSupportLink
        };

        #endregion

        #region BankAccountInfo

        applicantDto.BankAccountInformation = new BankAccountInformationDTO
        {
            BankName = entity.BankName,
            DdaType = entity.DdaType,
            AccountNumber = entity.AccountNumber,
            RoutingNumber = entity.RoutingNumber,
            BankAccountVerified = entity.BankAccountVerified,
            RoutingType = entity.RoutingType
        };

        #endregion

        #region BusinessModelInfo

        applicantDto.BusinessModel = new BusinessModelDTO
        {
            UrlsCount = entity.SitesCount,
            Urls = entity.Sites?.Select(x => x.Url).ToList()
        };

        #endregion

        #region FeesInfo

        applicantDto.Fees = new List<ApplicationFeeDTO>();
        var existfees = await _dbContext.FeeConfigurations
            .Where(x => x.Id == id)
            .ToListAsync();

        foreach (var fee in existfees)
        {
            applicantDto.Fees.Add(new ApplicationFeeDTO()
            {
                Id = fee.Id,
                Name = fee.Name,
                Type = fee.Type,
                ChargeType = fee.ChargeType,
                Amount = fee.Amount,
                IsStandard = fee.IsStandard
            });
        }

        #endregion

        #region ProductInfo

        applicantDto.ProductInfo = new ProductInfoDTO()
        {
            Healthcare = entity.Healthcare,
            Regulated = entity.Regulated,
        };

        #endregion

        #region Fees Info

        applicantDto.StandardFeeSelected = entity.Fees.All(x => x.IsStandard);
        applicantDto.Fees = entity.Fees.Select(x => new ApplicationFeeDTO
        {
            Id = x.Id,
            Name = x.Name,
            Type = x.Type,
            ChargeType = x.ChargeType,
            Amount = x.Amount,
            MinimumFeeAmount = x.MinimumFeeAmount,
            IsStandard = x.IsStandard
        }).ToList();

        #endregion

        applicantDto.UploadedDocuments = entity.Documents?.Select(x => new DocumentDTO
        {
            Id = x.Id,
            Name = x.Name?.Length > 37 ? x.Name.Substring(37) : x.Name,
            Type = x.Type,
            Path = x.Path,
            Description = x.Description,
            CreatedOn = x.CreatedOn,
        }).ToList();

        #region Company Info

        applicantDto.LegalEntityName = entity.LegalEntityName;
        applicantDto.LegalEntityCountry = entity.LegalEntityCountry;
        applicantDto.Dba = entity.Dba;
        applicantDto.TaxId = entity.TaxId;
        applicantDto.Mcc = entity.Mcc;
        applicantDto.Descriptor = entity.Descriptor;
        applicantDto.Type = entity.Type;
        applicantDto.BusinessEstablishedDate = entity.BusinessEstablishedDate;
        applicantDto.EcommercePlatform = entity.EcommercePlatform;
        applicantDto.Pcidss = entity.Pcidss;
        applicantDto.Description = entity.Description;
        applicantDto.SpecialTerms = entity.SpecialTerms;
        applicantDto.IsSiteValidated = entity.IsSiteValidated;
        applicantDto.RiskCategory = entity.RiskCategory;
        applicantDto.RiskStatus = entity.RiskStatus;
        applicantDto.OperationsStatus = entity.OperationsStatus;
        applicantDto.AnnualSalesVolume = entity.AnnualSalesVolume;

        #endregion

        #region TransactionInfo

        applicantDto.TransactionInformation = new TransactionInformationDTO
        {
            ChargebackPercentage = entity.ChargebackPercentage,
            AvgDeclineRate = entity.AvgDeclineRate,
            AvgDisputeRate = entity.AvgDisputeRate,
            AvgSales = entity.AvgMonthlySales,
            AvgTransactionAmount = entity.AvgTransactionAmount,
            ReturnsPercent = entity.ReturnsPercent
        };

        #endregion

        #region AccountInfo

        applicantDto.IntegrationPartnerId = entity.IntegrationPartnerId;
        applicantDto.SalesAgencyId = entity.SalesAgencyId;
        applicantDto.IntegrationTier = entity.IntegrationTier;
        applicantDto.AccountManagerId = entity.AccountManagerId;
        applicantDto.AccountId = entity.AccountId;
        applicantDto.BusinessEstablishedDate = entity.BusinessEstablishedDate;
        applicantDto.LegalEntityName = entity.LegalEntityName;
        applicantDto.RiskCategory = entity.RiskCategory;
        applicantDto.CrmId = entity.CrmId;

        if (entity.PartnerId.HasValue)
        {
            applicantDto.PartnerId = entity.PartnerId.Value;
        }

        #endregion

        applicantDto.LogoUrl = entity.LogoUrl;

        #region OwnerInfo

        if (entity.Owners != null && entity.Owners.Count > 0)
        {
            var newOwners = new List<OwnerDTO>();
            foreach (var owner in entity.Owners)
            {
                newOwners.Add(new OwnerDTO
                {
                    Id = owner.Id,
                    Title = owner.Title,
                    FirstName = owner.FirstName,
                    LastName = owner.LastName,
                    Email = owner.Email,
                    Phone = owner.Phone,
                    DateOfBirth = owner.DateOfBirth,
                    PrincipalType = owner.PrincipalType,
                    IndividualFormOfID = owner.IndividualFormOfID,
                    IdentificationNumber = owner.IdentificationNumber,
                    PercentOwnership = owner.PercentOwnership,
                    Address = new AddressDTO()
                    {
                        Line1 = owner.Address?.Line1,
                        Line2 = owner.Address?.Line2,
                        City = owner.Address?.City,
                        State = owner.Address?.State,
                        ZipCode = owner.Address?.ZipCode,
                        Country = owner.Address?.Country
                    }
                });
            }

            applicantDto.Owners = newOwners;
        }

        #endregion

        response.Record = applicantDto;

        return response;
    }

    public async Task<ApplicationNewResponse> QuickUpsertAsync(ApplicationQuickCreateRequest payload, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(payload).LogEnterAndExit();

        return payload.Id == null
            ? await QuickCreateAsync(payload)
            : await QuickUpdateAsync(payload, partnerId);

        async Task<ApplicationNewResponse> QuickCreateAsync(ApplicationQuickCreateRequest payload)
        {
            ApplicationNewResponse response = new ApplicationNewResponse();
            Application application = new Application();

            var partner = _dbContext.Partners.SingleOrDefault(x => x.Id == payload.PartnerId);

            if (partner == null)
                throw new FlexValidationException("PartnerId", "Partner does not exist");

            application.PartnerId = partner.Id;

            var emailAlreadyExist = await _dbContext.Applications.Include(x => x.PrimaryContact)
                .AnyAsync(x =>
                    x.PrimaryContact.Email == payload.PrimaryContact.Email &&
                    x.Status != ApplicationStatus.CANCELLED);

            if (emailAlreadyExist)
            {
                throw new FlexValidationException("PrimaryContact.Email",
                    "An email already exist");
            }

            if (payload.ContractAlreadySigned.HasValue)
            {
                application.ContractAlreadySigned = payload.ContractAlreadySigned.Value;
            }

            if (payload.IntegrationPartnerParticipateSale.HasValue)
            {
                application.IntegrationPartnerParticipateSale = payload.IntegrationPartnerParticipateSale.Value;
            }

            application.PrimaryContact = new Contact
            {
                Primary = true,
                FirstName = payload.PrimaryContact?.FirstName,
                LastName = payload.PrimaryContact?.LastName,
                Email = payload.PrimaryContact?.Email,
                Phone = payload.PrimaryContact?.Phone,
            };

            application.AccountManagerId = payload.AccountManagerId;
            application.Dba = payload.Dba;
            application.LegalEntityName = payload.LegalEntityName;
            application.IntegrationTier = payload.IntegrationTier;
            application.SpecialTerms = payload.SpecialTerms;
            application.AnnualSalesVolume = payload.AnnualSalesVolume;
            application.CrmId = payload.CrmId;


            if (payload.AccountId != null)
            {
                var account = await _dbContext.Accounts.FindAsync(payload.AccountId);
                if (account == null)
                {
                    throw new FlexValidationException("Account does not exist");
                }
            }
            else
            {
                var account = _dbContext.Accounts.Add(new Account()
                {
                    Name = payload.LegalEntityName,
                });

                //Account need to be sign to Partner
                if (payload.PartnerId != Guid.Empty)
                {
                    account.Entity.PartnerId = payload.PartnerId;
                }

                application.AccountId = account.Entity.Id;
            }


            application.Fees = new List<ApplicationFee>();
            if (payload.StandardFeeSelected)
            {
                var fees = await _dbContext.FeeConfigurations
                    .Where(x => x.PartnerId == payload.PartnerId && x.IsStandard)
                    .ToListAsync();

                if (fees.Any() == false)
                {
                    throw new FlexValidationException("Standard Fee configurations does not exist for This Partner");
                }


                if (fees.Count > 0)
                {
                    foreach (var fee in fees)
                    {
                        application.Fees.Add(new ApplicationFee
                        {
                            Name = fee.Name,
                            Type = fee.Type,
                            ChargeType = fee.ChargeType,
                            Amount = fee.Amount,
                            IsStandard = true
                        });
                    }
                }
            }
            else
            {
                foreach (var fee in payload.Fees.ToList())
                {
                    application.Fees.Add(new ApplicationFee
                    {
                        Name = fee.Name,
                        Type = fee.Type,
                        ChargeType = fee.ChargeType,
                        Amount = fee.Amount,
                        MinimumFeeAmount = fee.MinimumFeeAmount,
                        IsStandard = fee.IsStandard,
                    });
                }
            }

            application.IntegrationPartnerId = payload.IntegrationPartnerId;
            application.IntegrationTier = payload.IntegrationTier;

            var salesAgency = await _dbContext.SalesAgencies.SingleOrDefaultAsync(x => x.Id == payload.SalesAgencyId);
            if (salesAgency != null)
                application.SalesAgencyId = payload.SalesAgencyId;

            await _dbContext.Applications.AddAsync(application);
            await _dbContext.SaveChangesAsync();

            response.ApplicationId = application.Id;

            await _publisher.Publish(new ApplicationCreatedEvent
            {
                Id = application.Id,
                Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                Email = payload.PrimaryContact.Email,
                Mid = response.ApplicationId,
                Pid = payload.PartnerId,
                IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                FirstName = payload.PrimaryContact.FirstName,
                LastName = payload.PrimaryContact.LastName,
                Aid = payload.AccountId ?? Guid.Empty,
                LegalEntityName = payload.LegalEntityName,
            });

            if (payload.SaveAndSend)
            {
                await _publisher.Publish(new ApplicationInviteRequestedEvent
                {
                    Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                    Phone = payload.PrimaryContact.Phone,
                    Email = payload.PrimaryContact.Email,
                    Mid = response.ApplicationId,
                    Pid = payload.PartnerId,
                    IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                    FirstName = payload.PrimaryContact.FirstName,
                    LastName = payload.PrimaryContact.LastName,
                    Group = SuperAdminGroups.MERCHANT_ADMIN,
                    Aid = application.AccountId ?? Guid.Empty,
                    MerchantState = "boarding"
                });

                try
                {
                    await ApplyApplicationActivity(application.Id, new ApplicationActivity
                    {
                        Department = "Application",
                        Category = "Applicant invitation",
                        Activity = "Invite Sent",
                        UpdateMessage = $"Invite sent to {payload.PrimaryContact.Email}",
                        Meta = JsonConvert.SerializeObject(application),
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Error while creating Application Activity");
                }
            }

            return response;
        }

        async Task<ApplicationNewResponse> QuickUpdateAsync(ApplicationQuickCreateRequest payload, Guid? partnerId)
        {
            try
            {
                ApplicationNewResponse response = new ApplicationNewResponse();
                var application = await _dbContext.Applications.Include(x => x.PrimaryContact).Include(x => x.Fees)
                    .SingleOrDefaultAsync(x => x.Id == payload.Id);

                if (application == null || partnerId != null && application.PartnerId != partnerId)
                    throw new FlexValidationException("ApplicationId", "application does not exist");

                var partner = _dbContext.Partners.SingleOrDefault(x => x.Id == payload.PartnerId);
                if (partner == null)
                    throw new FlexValidationException("PartnerId", "Partner does not exist");

                application.PartnerId = partner.Id;

                var emailAlreadyExist = await _dbContext.Applications.Include(x => x.PrimaryContact)
                    .AnyAsync(x =>
                        x.PrimaryContact.Email == payload.PrimaryContact.Email &&
                        x.Status != ApplicationStatus.CANCELLED);
                if (!emailAlreadyExist)
                {
                    throw new FlexValidationException("Email", "An email address cannot be modified");
                }

                application.LegalEntityName = payload.LegalEntityName;
                application.Dba = payload.Dba;

                if (payload.PrimaryContact != null)
                {
                    application.PrimaryContact.FirstName = payload.PrimaryContact.FirstName;
                    application.PrimaryContact.Email = payload.PrimaryContact.Email;
                    application.PrimaryContact.LastName = payload.PrimaryContact.LastName;
                    application.PrimaryContact.Phone = payload.PrimaryContact.Phone;
                    application.PrimaryContact.ConfirmedUser = true;
                }

                application.AccountManagerId = payload.AccountManagerId;

                if (payload.IntegrationPartnerId != Guid.Empty)
                {
                    application.IntegrationPartnerId = null;
                }

                application.IntegrationTier = payload.IntegrationTier;

                var salesAgency =
                    await _dbContext.SalesAgencies.SingleOrDefaultAsync(x => x.Id == payload.SalesAgencyId);
                if (salesAgency != null)
                    application.SalesAgencyId = payload.SalesAgencyId;

                application.SpecialTerms = payload.SpecialTerms;

                if (payload.AccountId != Guid.Empty && payload.AccountId != null)
                {
                    var account = await _dbContext.Accounts.FindAsync(payload.AccountId);
                    if (account == null)
                    {
                        throw new FlexValidationException("Account does not exist");
                    }

                    account.Id = (Guid) payload.AccountId;
                }
                else
                {
                    var account = _dbContext.Accounts.Add(new Account()
                    {
                        Name = payload.LegalEntityName,
                    });

                    //Account need to be sign to Partner
                    if (payload.PartnerId != Guid.Empty)
                    {
                        account.Entity.PartnerId = payload.PartnerId;
                    }

                    application.AccountId = account.Entity.Id;
                }

                if (payload.Fees != null)
                {
                    foreach (var fee in payload.Fees.ToList())
                    {
                        var existingFee = application.Fees.FirstOrDefault(x => x.Id == fee.Id);

                        if (existingFee == null)
                        {
                            application.Fees.Add(new ApplicationFee
                            {
                                Name = fee.Name,
                                Type = fee.Type,
                                ChargeType = fee.ChargeType,
                                Amount = fee.Amount,
                                MinimumFeeAmount = fee.MinimumFeeAmount,
                                IsStandard = fee.IsStandard,
                            });
                        }
                        else
                        {
                            existingFee.Name = fee.Name;
                            existingFee.Type = fee.Type;
                            existingFee.ChargeType = fee.ChargeType;
                            existingFee.Amount = fee.Amount;
                            existingFee.MinimumFeeAmount = fee.MinimumFeeAmount;
                            existingFee.IsStandard = fee.IsStandard;
                        }
                    }
                }

                _dbContext.Applications.Update(application);

                await _dbContext.SaveChangesAsync();

                response.ApplicationId = application.Id;

                try
                {
                    await ApplyApplicationActivity(application.Id, new ApplicationActivity
                    {
                        Department = "Application",
                        Category = "Application",
                        Activity = "Application Updated",
                        Meta = JsonConvert.SerializeObject(new
                        {
                            applicationId = application.Id,
                            applicationStatus = application.Status.ToString(),
                            applicationLegalEntityName = application.LegalEntityName
                        }),
                        UpdateMessage = $"Application {application.LegalEntityName} Updated",
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Failed create activity while updating application");
                }


                if (payload.SaveAndSend)
                {
                    await _publisher.Publish(new ApplicationInviteRequestedEvent
                    {
                        Name = $"{payload.PrimaryContact?.FirstName} {payload.PrimaryContact.LastName}",
                        Phone = payload.PrimaryContact.Phone,
                        Email = payload.PrimaryContact.Email,
                        Mid = response.ApplicationId,
                        Pid = payload.PartnerId,
                        IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                        FirstName = payload.PrimaryContact.FirstName,
                        LastName = payload.PrimaryContact.LastName,
                        Group = SuperAdminGroups.MERCHANT_ADMIN,
                        Aid = (Guid) application.AccountId,
                        MerchantState = "boarding"
                    });

                    try
                    {
                        await ApplyApplicationActivity(application.Id, new ApplicationActivity
                        {
                            Department = "Application",
                            Category = "Applicant invitation",
                            Activity = "Invite Sent",
                            UpdateMessage = $"Invite sent to {payload.PrimaryContact.Email}",
                            Meta = JsonConvert.SerializeObject(application),
                        });
                    }
                    catch (Exception e)
                    {
                        workspan.Log.Error(e, "Error while creating Application Activity");
                    }
                }

                return response;
            }
            catch (FlexValidationException ve)
            {
                workspan.Log.Error($"Validation Error: {ve.Message}");

                await ApplyApplicationActivity(payload.Id.Value, new ApplicationActivity
                {
                    Department = "Application",
                    Category = "Application",
                    Activity = "Application Update Failed",
                    UpdateMessage = $"Failed to update application {payload.LegalEntityName}",
                    Note = $"Validation Error - ${ve.Message}",
                });

                throw;
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);

                await ApplyApplicationActivity(payload.Id.Value, new ApplicationActivity
                {
                    Department = "Application",
                    Category = "Application",
                    Activity = "Application Update Failed",
                    UpdateMessage = $"Failed to update application {payload.LegalEntityName}",
                    Note = e.Message,
                });

                throw;
            }
        }
    }

    public async Task<ApplicantGetResponse> ApplicantApplicationGet(Guid id)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Request(id)
            .LogEnterAndExit();

        var entity = await _dbContext.Applications
            .Include(x => x.Partner)
            .Include(x => x.IntegrationPartner)
            .Include(x => x.Address)
            .Include(x => x.PrimaryContact)
            .Include(x => x.DeveloperContact)
            .Include(x => x.Fees)
            .Include(x => x.SalesAgency)
            .Include(x => x.Owners).ThenInclude(owner => owner.Address)
            .Include(x => x.Sites)
            .Include(x => x.DeveloperContact)
            .Include(x => x.Documents)
            .SingleOrDefaultAsync(x => x.Id == id && x.Status != ApplicationStatus.APPROVED);

        if (entity == null)
            throw new FlexNotFoundException($"Application with Id {id} does not exist");

        var response = new ApplicantGetResponse();
        response.Id = entity.Id;
        response.Status = entity.Status;
        response.StatusName = entity.Status.ToString();
        response.SpecialTerms = entity.SpecialTerms;
        response.ContractAlreadySigned = entity.ContractAlreadySigned;
        response.IntegrationPartnerParticipateSale = entity.IntegrationPartnerParticipateSale;
        response.CrmId = entity.CrmId;

        #region Primary contact

        response.PrimaryContact = new PrimaryContact()
        {
            FirstName = entity.PrimaryContact.FirstName,
            LastName = entity.PrimaryContact.LastName,
            Email = entity.PrimaryContact.Email,
            Phone = entity.PrimaryContact.Phone,
            UserCreated = entity.PrimaryContact.UserId != Guid.Empty,
            ConfirmedUser = entity.PrimaryContact.ConfirmedUser,
        };

        #endregion
        
        #region AddressInfo

        response.Address = new AddressDTO()
        {
            Country = entity.Address?.Country,
            Line1 = entity.Address?.Line1,
            Line2 = entity.Address?.Line2,
            City = entity.Address?.City,
            ZipCode = entity.Address?.ZipCode,
            State = entity.Address?.State,
        };

        #endregion

        #region DeveloperContacrInfo

        response.DeveloperContact = new DeveloperContact()
        {
            DeveloperFirstName = entity.DeveloperContact?.FirstName,
            DeveloperLastName = entity.DeveloperContact?.LastName,
            DeveloperEmail = entity.DeveloperContact?.Email,
            DeveloperPhone = entity.DeveloperContact?.Phone
        };

        #endregion

        #region CustomerSupportInfo

        response.CustomerSupportContact = new ApplicationCustomerSupportInformationDTO
        {
            CustomerSupportName = entity.CustomerSupportName,
            CustomerSupportEmail = entity.CustomerSupportEmail,
            CustomerSupportPhone = entity.CustomerSupportPhone,
            CustomerSupportLink = entity.CustomerSupportLink
        };

        #endregion

        #region BankAccountInfo

        response.BankAccountInformation = new BankAccountInformationDTO
        {
            BankName = entity.BankName,
            DdaType = entity.DdaType,
            AccountNumber = entity.AccountNumber,
            RoutingNumber = entity.RoutingNumber,
            BankAccountVerified = entity.BankAccountVerified,
            RoutingType = entity.RoutingType
        };

        #endregion

        #region Owner Info

        if (entity.Owners is {Count: > 0})
        {
            response.Owners = new List<OwnerDTO>();
            response.Owners.AddRange(entity.Owners.Select(owner => new OwnerDTO()
            {
                Id = owner.Id,
                Title = owner.Title,
                FirstName = owner.FirstName,
                LastName = owner.LastName,
                Email = owner.Email,
                Phone = owner.Phone,
                DateOfBirth = owner.DateOfBirth,
                PrincipalType = owner.PrincipalType,
                IndividualFormOfID = owner.IndividualFormOfID,
                IdentificationNumber = owner.IdentificationNumber,
                PercentOwnership = owner.PercentOwnership,
                Address = new AddressDTO()
                {
                    Line1 = owner.Address?.Line1,
                    Line2 = owner.Address?.Line2,
                    City = owner.Address?.City,
                    State = owner.Address?.State,
                    ZipCode = owner.Address?.ZipCode,
                    Country = owner.Address?.Country
                }
            }));
        }

        #endregion

        #region Legal Entity Information

        response.CompanyInformation = new LegalEntityDTO
        {
            LegalEntityName = entity.LegalEntityName,
            LegalEntityCountry = entity.LegalEntityCountry,
            Dba = entity.Dba,
            Mcc = entity.Mcc,
            TaxId = entity.TaxId,
            EcommercePlatform = entity.EcommercePlatform,
            Descriptor = entity.Descriptor,
            Description = entity.Description
        };

        #endregion

        #region ProductInfo

        response.ProductInfo = new ProductInfoDTO
        {
            Healthcare = entity.Healthcare,
            Regulated = entity.Regulated,
        };

        #endregion

        #region Transaction Info

        response.TransactionInformation = new TransactionInformationDTO
        {
            ChargebackPercentage = entity.ChargebackPercentage,
            AvgDeclineRate = entity.AvgDeclineRate,
            AvgDisputeRate = entity.AvgDisputeRate,
            AvgSales = entity.AvgMonthlySales,
            AvgTransactionAmount = entity.AvgTransactionAmount,
            ReturnsPercent = entity.ReturnsPercent
        };

        #endregion

        #region Business Model Info

        response.BusinessModel = new BusinessModelDTO
        {
            UrlsCount = entity.SitesCount,
        };

        if (entity.Sites != null)
        {
            response.BusinessModel.Urls = new List<string>();
            foreach (var site in entity.Sites)
            {
                response.BusinessModel.Urls.Add(site.Url);
            }
        }

        #endregion

        #region FeesInfo

        response.Fees = new List<ApplicationFeeDTO>();
        if (entity.Fees != null)
        {
            foreach (var fee in entity.Fees)
            {
                response.Fees.Add(new ApplicationFeeDTO
                {
                    Id = fee.Id,
                    Name = fee.Name,
                    Type = fee.Type,
                    ChargeType = fee.ChargeType,
                    Amount = fee.Amount,
                    MinimumFeeAmount = fee.MinimumFeeAmount,
                    IsStandard = fee.IsStandard
                });
            }
        }

        #endregion

        response.Status = entity.Status;
        response.StatusName = EnumHelpers.ParseEnum(entity.Status);

        response.TermsAndConditionsUrl = entity.Partner.TermsAndConditionsUrl;
        response.MerchantTermsAndConditionsUrl = entity.Partner.MerchantTermsAndConditionsUrl;
        response.TermsAndConditions = entity.Partner.TermsAndConditions;
        response.ConsentLabel = entity.Partner.ConsentLabel;

        #region Documents

        //documents
        response.UploadedDocuments = entity.Documents?.Select(x => new DocumentDTO
        {
            Id = x.Id,
            Name = x.Name?.Length > 37 ? x.Name.Substring(37) : x.Name,
            Type = x.Type,
            Path = x.Path,
            Description = x.Description,
            CreatedOn = x.CreatedOn,
        }).ToList();

        #endregion

        return response;
    }

    public async Task<ApplicationUpdateResponse> ApplicantUpdateAsync(Application application, ApplicantBoardingRequestDTO payload)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(payload).LogEnterAndExit();
        try
        {
            var response = new ApplicationUpdateResponse();

            #region CompanyInfo

            application.IsSiteValidated = payload.CompanyInformation.IsSiteValidated;
            application.LegalEntityName = payload.CompanyInformation?.LegalEntityName ?? string.Empty;
            application.Dba = payload.CompanyInformation?.Dba ?? string.Empty;
            application.TaxId = payload.CompanyInformation?.TaxId ?? string.Empty;
            application.LegalEntityCountry = payload.CompanyInformation?.LegalEntityCountry ?? string.Empty;
            application.Mcc = payload.CompanyInformation?.Mcc ?? string.Empty;

            application.Descriptor = payload.CompanyInformation?.Descriptor ?? string.Empty;

            #endregion

            #region PrimaryContact

            application.PrimaryContact.FirstName = payload.PrimaryContact.FirstName;
            application.PrimaryContact.LastName = payload.PrimaryContact.LastName;
            application.PrimaryContact.Email = payload.PrimaryContact.Email;
            application.PrimaryContact.Phone = payload.PrimaryContact.Phone;

            #endregion

            #region OwnerInfo

            if (payload.Owners != null && payload.Owners.Any())
            {
                var sumOwnership = payload.Owners.Sum(x => x.PercentOwnership);
                if (sumOwnership > 100)
                {
                    throw new FlexValidationException($"Sum of all owners percentage cannot exceed 100% ");
                }

                var newOwners = new List<Owner>();

                foreach (var owner in payload.Owners)
                {
                    Owner newOwner = new Owner()
                    {
                        Title = owner.Title,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Email = owner.Email,
                        Phone = owner.Phone,
                        DateOfBirth = owner.DateOfBirth,
                        PrincipalType = owner.PrincipalType,
                        IndividualFormOfID = owner.IndividualFormOfID,
                        IdentificationNumber = owner.IdentificationNumber,
                        PercentOwnership = owner.PercentOwnership,
                        Address = new Address()
                        {
                            Line1 = owner.Address?.Line1,
                            Line2 = owner.Address?.Line2,
                            City = owner.Address?.City,
                            State = owner.Address?.State,
                            ZipCode = owner.Address?.ZipCode,
                            Country = owner.Address?.Country
                        }
                    };
                    newOwners.Add(newOwner);
                }


                application.Owners = newOwners;
            }

            #endregion

            #region CustomerSupportInfo

            if (payload.CustomerSupportContact != null)
            {
                application.CustomerSupportName = payload?.CustomerSupportContact.CustomerSupportName;
                application.CustomerSupportEmail = payload?.CustomerSupportContact.CustomerSupportEmail;
                application.CustomerSupportLink = payload?.CustomerSupportContact.CustomerSupportLink;
                application.CustomerSupportPhone = payload?.CustomerSupportContact.CustomerSupportPhone;
            }

            #endregion
            
            #region DeveloperContactInfo
            if (payload.DeveloperContact != null)
            {
                if (application.DeveloperContact == null)
                    application.DeveloperContact = new Contact();
                
                application.DeveloperContact.FirstName = payload.DeveloperContact?.DeveloperFirstName;
                application.DeveloperContact.LastName = payload.DeveloperContact?.DeveloperLastName;
                application.DeveloperContact.Email = payload.DeveloperContact?.DeveloperEmail;
                application.DeveloperContact.Phone = payload.DeveloperContact?.DeveloperPhone;
            }
            #endregion

            #region BusinessModelInfo

            if (payload.BusinessModel != null)
            {
                application.SitesCount = payload.BusinessModel?.UrlsCount;
                if (payload.BusinessModel?.Urls != null)
                {
                    var sites = new List<SiteCandidate>();

                    foreach (var url in payload.BusinessModel.Urls)
                    {
                        sites.Add(new SiteCandidate()
                        {
                            Url = url
                        });
                    }

                    application.Sites = sites;
                }
            }

            #endregion

            #region ProductInfo

            if (payload.ProductInfo != null)
            {
                application.Healthcare = payload.ProductInfo?.Healthcare;
                application.Regulated = payload.ProductInfo?.Regulated;
            }

            #endregion

            #region TransactionInfo

            application.AvgMonthlySales = payload.TransactionInformation?.AvgSales ?? string.Empty; //Check
            application.ChargebackPercentage = payload.TransactionInformation?.ChargebackPercentage ?? string.Empty; //Check
            application.AvgDeclineRate = payload.TransactionInformation?.AvgDeclineRate ?? string.Empty;
            application.ReturnsPercent = payload.TransactionInformation?.ReturnsPercent ?? string.Empty;
            application.AvgDisputeRate = payload.TransactionInformation?.AvgDisputeRate ?? string.Empty;
            application.AvgTransactionAmount = payload.TransactionInformation?.AvgTransactionAmount ?? string.Empty;

            #endregion
            
            #region BankInfo

            if (payload.BankAccountInformation != null)
            {
                application.BankName = payload.BankAccountInformation.BankName;
                application.DdaType = payload.BankAccountInformation.DdaType;
                application.RoutingNumber = payload.BankAccountInformation.RoutingNumber;
                application.AccountNumber = payload.BankAccountInformation.AccountNumber;
                application.RoutingType = payload.BankAccountInformation.RoutingType;
            }

            #endregion

            #region AdressInfo

            if (application.Address == null)
                application.Address = new Address();
            application.Address.Country = payload.Address?.Country;
            application.Address.Line1 = payload.Address?.Line1;
            application.Address.Line2 = payload.Address?.Line2;
            application.Address.City = payload.Address?.City;
            application.Address.ZipCode = payload.Address?.ZipCode;
            application.Address.State = payload.Address?.State;

            #endregion

            if (payload.Next)
            {
                application.Status = ApplicationStatus.IN_REVIEW;
            }
            else
            {
                application.Status = ApplicationStatus.DRAFT;
            }

            _dbContext.Applications.Update(application);
            await _dbContext.SaveChangesAsync();

            await ApplyApplicationActivity(application.Id, new ApplicationActivity
            {
                Department = "Application",
                Category = "Application Updated",
                Activity = "Updated",
                Note = $"Application {application?.LegalEntityName} Updated",
                Meta = JsonConvert.SerializeObject(application),
            });

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to update application");

            await ApplyApplicationActivity(application.Id, new ApplicationActivity
            {
                Department = "Application",
                Category = "Application Update Failed",
                Activity = "Update Failed",
                Note = $"Failed to update application {application.Id}",
                Meta = e.Message,
            });

            throw;
        }
    }


    public async Task<ApplicationResponse> GetByIdAsync(Guid id)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        try
        {
            workspan.Log.Information($"ENTERED: ApplicationService => GetByIdAsync: {id}");

            var response = new ApplicationResponse();
            var entity = await _dbContext.Applications
                .Include(x => x.Partner)
                .Include(x => x.IntegrationPartner)
                .Include(x => x.Address)
                .Include(x => x.PrimaryContact)
                .Include(x => x.DeveloperContact)
                .Include(x => x.Fees)
                .Include(x => x.SalesAgency)
                .Include(x => x.Owners)
                .Include(x => x.Sites)
                .SingleOrDefaultAsync(x => x.Id == id && x.Status != ApplicationStatus.APPROVED);

            if (entity == null)
            {
                throw new FlexNotFoundException($"Application not found for id:{id}");
            }

            var dto = _mapper.Map<ApplicationDTO>(entity);

            #region Obtaining expiring link to the logo image from the cloud storage

            if (!string.IsNullOrWhiteSpace(entity.LogoUrl))
            {
                dto.LogoUrl = entity.LogoUrl;
            }

            #endregion

            // response.Record = dto;
            return response;
        }
        catch (FlexNotFoundException e)
        {
            workspan.Log.Error(e, "NotFoundException");
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to get by id ");
            throw;
        }
    }

    public async Task<ApplicationResponse> GetQuickByIdAsync(Guid id, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        try
        {
            workspan.Log.Information($"ENTERED: ApplicationService => GetByIdAsync: {id}");

            var response = new ApplicationResponse();

            var query = _dbContext.Applications
                .Include(x => x.Partner)
                .Include(x => x.IntegrationPartner)
                .Include(x => x.Address)
                .Include(x => x.PrimaryContact)
                .Include(x => x.DeveloperContact)
                .Include(x => x.Fees)
                .Include(x => x.SalesAgency)
                .Include(x => x.Owners)
                .Include(x => x.Sites)
                .Where(x => x.Id == id && x.Status != ApplicationStatus.APPROVED)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x =>
                    x.Id == id && x.PartnerId == partnerId && x.Status != ApplicationStatus.APPROVED);
            }

            var entity = await query.SingleOrDefaultAsync();

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{id}");

            var dto = new ApplicationQuickGetResponse
            {
                Id = entity.Id,

                LegalEntityName = entity.LegalEntityName,
                Dba = entity.Dba,
                IntegrationPartnerId = entity.IntegrationPartnerId,
                SpecialTerms = entity.SpecialTerms,
                PrimaryContact = new PrimaryContact
                {
                    FirstName = entity.PrimaryContact.FirstName,
                    LastName = entity.PrimaryContact.LastName,
                    Email = entity.PrimaryContact.Email,
                    Phone = entity.PrimaryContact.Phone,
                    UserCreated = entity.PrimaryContact.UserId != Guid.Empty,
                    ConfirmedUser = entity.PrimaryContact.ConfirmedUser,
                },
                AccountManagerId = entity.AccountManagerId,
                PartnerId = entity.PartnerId.Value,
                SalesAgencyId = entity.SalesAgencyId,
                IntegrationTier = entity.IntegrationTier,
                AccountId = entity.AccountId,
                Status = entity.Status,
                StatusName = entity.Status.ToString(),
                Fees = new List<ApplicationFeeQueryDTO>(),
                ContractAlreadySigned = entity.ContractAlreadySigned,
                IntegrationPartnerParticipateSale = entity.IntegrationPartnerParticipateSale,
                AnnualSalesVolume = entity.AnnualSalesVolume,
                CrmId = entity.CrmId,
            };
            if (entity.Fees != null && entity.Fees.Count > 0)
            {
                foreach (var fee in entity.Fees)
                {
                    dto.Fees.Add(new ApplicationFeeQueryDTO
                    {
                        Id = fee.Id,
                        Name = fee.Name,
                        Type = fee.Type,
                        ChargeType = fee.ChargeType,
                        Amount = fee.Amount,
                        MinimumFeeAmount = fee.MinimumFeeAmount,
                        IsStandard = fee.IsStandard,
                    });
                }
            }

            if (dto.Fees != null && dto.Fees.Count > 0)
                dto.StandardFeeSelected = dto.Fees.FirstOrDefault().IsStandard;

            response.Record = dto;

            return response;
        }
        catch (FlexNotFoundException e)
        {
            workspan.Log.Error(e, "NotFoundException");
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to get by id ");
            throw;
        }
    }

    public async Task UpdateRiskCategoryAsync(Guid id, RiskCategory category, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(id).LogEnterAndExit();

        try
        {
            var query = _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Include(application => application.Activities)
                .Where(x => x.Id == id)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x => x.Id == id && x.PartnerId == partnerId);
            }

            var entity = await query.SingleOrDefaultAsync();

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{id}");

            var oldCategory = entity.RiskCategory ?? "";
            if (!string.IsNullOrEmpty(oldCategory))
            {
                oldCategory += " => ";
            }

            await ApplyApplicationActivity(entity.Id, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Risk Category",
                Activity = nameof(category),
                UpdateMessage = $"Updated risk category: {oldCategory}{category}",
                Meta = JsonConvert.SerializeObject(entity),
            });

            entity.RiskCategory = category.ToString();
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "EXCEPTION: ApplicationService => Unable to UpdateRiskCategory ");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Risk Category",
                Activity = "Update Failed",
                UpdateMessage = "Update Risk Category Failed",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task NotifyAccountManager(Guid applicationId, AccountManagerNotificationRequestDTO payload,
        Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        try
        {
            var query = _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Include(application => application.Partner)
                .ThenInclude(partner => partner.Address)
                .Where(x => x.Id == applicationId)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x => x.Id == applicationId && x.PartnerId == partnerId);
            }

            var application = await query.SingleOrDefaultAsync();

            if (application == null)
            {
                throw new FlexChargeException($"Application not found for id:{applicationId}");
            }

            // var partner = await _context.Partners.FindAsync(application.PartnerId);
            //
            // if (partner == null)
            // {
            //     throw new FlexChargeException($"Partner not found for id: {application.PartnerId}");
            // }

            if (application.AccountManagerId == null || application.AccountManagerId == Guid.Empty)
            {
                throw new FlexChargeException($"Account Manager not found for application: {applicationId}");
            }

            // TODO this event will be deprecated
            await _publisher.Publish(new AccountManagerSendEmailEvent
            {
                AccountManagerId = application.AccountManagerId.Value,
                ApplicationId = application.Id,
                Comment = payload.Comment,
                RiskStatus = application.RiskStatus,
                LegalEntityName = application.LegalEntityName,
                Dba = application.Dba,
                LastName = application.PrimaryContact.LastName,
                PartnerAddressLine1 = application.Partner.Address.Line1,
                PartnerAddressLine2 = application.Partner.Address.Line2,
                PartnerCity = application.Partner.Address.City,
                PartnerCountry = application.Partner.Address.Country,
                PartnerSiteUrl = application.Partner.SiteUrl,
                PartnerPrivacyPolicyUrl = application.Partner.PrivacyPolicyUrl,
                PartnerZipCode = application.Partner.Address.ZipCode,
                PartnerState = application.Partner.Address.State,
                PartnerTermsAndConditionsUrl = application.Partner.TermsAndConditionsUrl,
            });

            await ApplyApplicationActivity(application.Id, new ApplicationActivity()
            {
                Department = "Operations",
                Category = "Account Manager Notification",
                UpdateMessage = "Account Manager Notified",
                Activity = "Account Manager Notified",
                Note = $"{payload.Comment}",
            });
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "EXCEPTION: ApplicationService => Unable to NotifyAccountManager ");

            await ApplyApplicationActivity(applicationId, new ApplicationActivity()
            {
                Department = "Operations",
                Category = "Account Manager Notification",
                UpdateMessage = "Account Manager Notification Failed",
                Activity = "Account Manager Notification Failed",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task NotifyApplicant(Guid applicationId, ApplicantNotificationRequestDTO payload, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var query = _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Include(application => application.Partner)
                .ThenInclude(partner => partner.Address)
                .Where(x => x.Id == applicationId)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x => x.Id == applicationId && x.PartnerId == partnerId);
            }

            var application = await query.SingleOrDefaultAsync();

            if (application == null)
            {
                throw new NotFoundException($"Application not found for id: {applicationId}");
            }

            // https://flex-charge.atlassian.net/wiki/spaces/PF/pages/582647816/Self+boarding+-+Application+Review+Notifications+flow#Applicant---Partner---Application-notification
            var templateId = _configuration.GetValue<string>("email:applicantNotifyEmailTemplateId");

            await _emailSender.SendEmailAsync(
                application.PrimaryContact.Email,
                $"{application.Partner.Name}: New message about your application",
                payload.Comment,
                new
                {
                    partner_color = "384248",
                    applicant_first_name = application.PrimaryContact.FirstName,
                    partner_name = application.Partner.Name,
                    applicant_dba = application.Dba,
                    base_url = _configuration.GetValue<string>("email:baseUrl"),
                    path = "/auth/login",
                    notification = payload.Comment,
                    email_from = application.Partner.NotificationSenderEmail,
                    email = application.PrimaryContact.Email,

                    partner_address_line_1 = application.Partner.Address.Line1,
                    partner_address_line_2 = application.Partner.Address.Line2,
                    partner_city = application.Partner.Address.City,
                    partner_state_province_region = application.Partner.Address.State,
                    partner_postal_code = application.Partner.Address.ZipCode,
                    partner_country = application.Partner.Address.Country,
                    partner_site_url = application.Partner.SiteUrl,
                    partner_privacy_policy_url = application.Partner.PrivacyPolicyUrl,
                    partner_terms_url = application.Partner.TermsAndConditionsUrl,

                    distribution = "external",
                    template_id = templateId,
                },
                templateId,
                bcc: application.Partner.BccEmail,
                replyTo: application.Partner.ReplyToEmail,
                senderEmailOverride: application.Partner.NotificationSenderEmail,
                senderNameOverride: application.Partner.Name
            );

            await ApplyApplicationActivity(application.Id, new ApplicationActivity()
            {
                Department = "Operations",
                Category = "Applicant Notification",
                UpdateMessage = "Applicant Notified",
                Activity = "Applicant Notified",
                Note = $"{payload.Comment}",
            });
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "EXCEPTION: ApplicationService => Unable to NotifyApplicant ");

            await ApplyApplicationActivity(applicationId, new ApplicationActivity()
            {
                Department = "Operations",
                Category = "Applicant Notification",
                UpdateMessage = "Applicant Notification Failed",
                Activity = "Applicant Notification Failed",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task DocumentUploadNotificationAsync(Application application, string fileName)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("ApplicationId", application.Id)
            .Baggage("Mid", application.Id)
            .LogEnterAndExit();

        try
        {
            if (application == null)
                throw new FlexChargeException($"Application not found for id: {application.Id}");

            if (application.Partner == null)
                throw new FlexChargeException($"Partner not found for id: {application.PartnerId}");

            var operationsEmail = application.Partner.OperationsEmail;
            var riskEmail = application.Partner.RiskEmail;

            // TODO add real template id
            var templateId = _configuration.GetValue<string>("email:applicant_Generic_EmailTemplate");

            if (operationsEmail != null)
            {
                try
                {
                    await _emailSender.SendEmailAsync(
                        operationsEmail,
                        $"{application.Partner.Name}: New document uploaded",
                        "A new document has been uploaded to the application",
                        new
                        {
                            alert = "New document upload",
                            partner_color = "384248",
                            applicant_first_name = application.PrimaryContact?.FirstName,
                            aplicant_last_name = application.PrimaryContact?.LastName,
                            applicant_dba = application.LegalEntityName,
                            application_status = application.Status.ToString(),
                            team = "Operations",
                            base_url = _configuration.GetValue<string>("email:baseUrl"),
                            path = $"/accounts/applications/",
                            mid = application.Id,
                            slug = "",
                            email_from = "<EMAIL>",
                            email = operationsEmail,

                            partner_address_line_1 = application.Partner.Address?.Line1,
                            partner_address_line_2 = application.Partner.Address?.Line2,
                            partner_city = application.Partner.Address?.City,
                            partner_country = application.Partner.Address?.Country,
                            partner_state_province_region = application.Partner.Address?.State,
                            partner_postal_code = application.Partner.Address?.ZipCode,
                            partner_site_url = application.Partner.SiteUrl,
                            partner_privacy_policy_url = application.Partner.PrivacyPolicyUrl,
                            partner_terms_url = application.Partner.TermsAndConditionsUrl,
                        },
                        templateId,
                        bcc: application.Partner.BccEmail,
                        replyTo: application.Partner.ReplyToEmail,
                        senderEmailOverride: application.Partner.NotificationSenderEmail,
                        senderNameOverride: application.Partner.Name
                    );
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Failed to send email to operations");
                }
            }

            if (riskEmail != null)
            {
                try
                {
                    await _emailSender.SendEmailAsync(
                        riskEmail,
                        $"{application.Partner.Name}: New document uploaded",
                        "A new document has been uploaded to the application",
                        new
                        {
                            alert = "New document upload",
                            partner_color = "384248",
                            applicant_first_name = application.PrimaryContact?.FirstName,
                            aplicant_last_name = application.PrimaryContact?.LastName,
                            applicant_dba = application.LegalEntityName,
                            application_status = application.Status.ToString(),
                            team = "Operations",
                            base_url = _configuration.GetValue<string>("email:baseUrl"),
                            path = $"/accounts/applications/",
                            mid = application.Id,
                            slug = "",
                            email_from = "<EMAIL>",
                            email = riskEmail,

                            partner_address_line_1 = application.Partner.Address?.Line1,
                            partner_address_line_2 = application.Partner.Address?.Line2,
                            partner_city = application.Partner.Address?.City,
                            partner_country = application.Partner.Address?.Country,
                            partner_state_province_region = application.Partner.Address?.State,
                            partner_postal_code = application.Partner.Address?.ZipCode,
                            partner_site_url = application.Partner.SiteUrl,
                            partner_privacy_policy_url = application.Partner.PrivacyPolicyUrl,
                            partner_terms_url = application.Partner.MerchantTermsAndConditionsUrl,
                        },
                        templateId,
                        bcc: application.Partner.BccEmail,
                        replyTo: application.Partner.ReplyToEmail,
                        senderEmailOverride: application.Partner.NotificationSenderEmail,
                        senderNameOverride: application.Partner.Name
                    );
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Failed to send email to risk");
                }
            }
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "EXCEPTION: ApplicationService => Unable to DocumentUploadNotificationAsync ");

            throw;
        }
    }

    public async Task SendStripeMerchantCreatedNotificationAsync(Guid merchantId)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .Baggage("MerchantId", merchantId).LogEnterAndExit();

        try
        {
            var merchant = await _dbContext.Merchants
                .Include(x => x.Partner).ThenInclude(partner => partner.Address)
                .Include(x => x.PrimaryContact)
                .FirstOrDefaultAsync(x => x.Id == merchantId);

            if (merchant == null)
            {
                throw new FlexChargeException($"Merchant not found for id: {merchantId}");
            }

            if (merchant.PrimaryContact == null || merchant.PrimaryContact?.Email == null)
            {
                throw new FlexChargeException($"Primary contact not found for merchant: {merchantId}");
            }

            var templateId = _configuration.GetValue<string>("email:application_StripeMerchantCreated_EmailTemplate");

            await _emailSender.SendEmailAsync(
                merchant.PrimaryContact.Email,
                "Welcome to FlexFactor!",
                "Welcome to FlexFactor!",
                new
                {
                    partner_logo_url = merchant.Partner.LogoUrl,
                    first_name = merchant.PrimaryContact.FirstName,
                    base_url = _configuration.GetValue<string>("email:baseUrl"),
                    path = "",
                    slug = "",
                    email = merchant.PrimaryContact.Email,

                    partner_address_line_1 = merchant.Partner.Address?.Line1,
                    partner_address_line_2 = merchant.Partner.Address?.Line2,
                    partner_city = merchant.Partner.Address?.City,
                    partner_country = merchant.Partner.Address?.Country,
                    partner_state_province_region = merchant.Partner.Address?.State,
                    partner_postal_code = merchant.Partner.Address?.ZipCode,
                    partner_site_url = merchant.Partner.SiteUrl,
                    partner_privacy_policy_url = merchant.Partner.PrivacyPolicyUrl,
                    partner_terms_url = merchant.Partner.TermsAndConditionsUrl,

                    template_id = templateId,
                },
                templateId,
                bcc: merchant.Partner.BccEmail,
                replyTo: merchant.Partner.ReplyToEmail,
                senderEmailOverride: merchant.Partner.NotificationSenderEmail,
                senderNameOverride: merchant.Partner.Name
            );
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Stripe merchant created > Failed to send email to applicant");

            throw;
        }
    }

    public async Task<IList<AplicationActivityDTO>> GetApplicationActivitiesAsync(Guid id, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(id).LogEnterAndExit();

        try
        {
            var query = _dbContext.Applications
                .Include(x => x.Activities)
                .Where(x => x.Id == id)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x => x.Id == id && x.PartnerId == partnerId);
            }

            var entity = await query.SingleOrDefaultAsync();

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{id}");

            var activities = entity.Activities
                .OrderByDescending(x => x.CreatedOn)
                .Select(x => new AplicationActivityDTO
                {
                    Id = x.Id,
                    CreatedOn = x.CreatedOn,
                    Activity = x.Activity,
                    UserId = x.UserId,
                    Department = x.Department,
                    Category = x.Category,
                    Note = x.Note,
                    Meta = x.Meta,
                    UpdateMessage = x.UpdateMessage,
                    User = x.User
                }).ToList();

            return activities;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to get activity log ");
            throw;
        }
    }

    #region Public onboarding

    public async Task<ApplicationNewResponse> PublicCreateAsync(Partner partner,
        PublicApplicantCreateDTO payload)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(payload).LogEnterAndExit();

        try
        {
            var response = new ApplicationNewResponse();

            var emailAlreadyExist = await _dbContext.Applications.Include(x => x.PrimaryContact)
                .AnyAsync(x =>
                    x.PrimaryContact.Email == payload.PrimaryContact.Email &&
                    x.Status != ApplicationStatus.CANCELLED);

            if (emailAlreadyExist)
            {
                throw new FlexValidationException("PrimaryContact.Email",
                    "An email already exist");
            }

            if (partner.RelatedWhitelabelPartnerId == null)
            {
                throw new FlexValidationException("RelatedWhitelabelPartnerId not found");
            }

            var mapped = _mapper.Map<Application>(payload);

            mapped.PartnerId = partner.RelatedWhitelabelPartnerId;
            mapped.IntegrationPartnerId = partner.Id;

            var whitelabelPartner = await _dbContext.Partners
                .Include(x => x.FeeConfigurations)
                .SingleOrDefaultAsync(x => x.Id == partner.RelatedWhitelabelPartnerId);

            if (whitelabelPartner != null)
            {
                var fees = whitelabelPartner.FeeConfigurations.Where(x => x.IsStandard);

                if (mapped.Fees != null)
                {
                    mapped.Fees.Clear();
                }
                else
                {
                    mapped.Fees = new List<ApplicationFee>();
                }

                foreach (var fee in fees)
                {
                    mapped.Fees.Add(new ApplicationFee
                    {
                        Name = fee.Name,
                        Type = fee.Type,
                        ChargeType = fee.ChargeType,
                        Amount = fee.Amount,
                        IsStandard = true
                    });
                }
            }

            if (payload.Owners != null)
            {
                var sumOwnership = payload.Owners.Sum(x => x.PercentOwnership);

                if (sumOwnership > 100)
                {
                    response.AddError(
                        $"The percentage cannot exceed 100",
                        "PercentOwnership",
                        "general");
                    return response;
                }

                var newOwners = new List<Owner>();
                foreach (var owner in payload.Owners)
                {
                    newOwners.Add(new Owner()
                    {
                        Title = owner.Title,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Email = owner.Email,
                        Phone = owner.Phone,
                        DateOfBirth = owner.DateOfBirth,
                        PrincipalType = owner.PrincipalType,
                        IndividualFormOfID = owner.IndividualFormOfID,
                        IdentificationNumber = owner.IdentificationNumber,
                        PercentOwnership = owner.PercentOwnership,
                        Address = new Address()
                        {
                            Line1 = owner.Address?.Line1,
                            Line2 = owner.Address?.Line2,
                            City = owner.Address?.City,
                            State = owner.Address?.State,
                            ZipCode = owner.Address?.ZipCode,
                            Country = owner.Address?.Country
                        }
                    });
                }

                mapped.Owners = newOwners;
            }

            #region BusinessModelInfo

            if (payload.BusinessModel != null)
            {
                mapped.SitesCount = payload.BusinessModel?.UrlsCount;

                if (payload.BusinessModel?.Urls != null)
                {
                    var sites = new List<SiteCandidate>();

                    foreach (var url in payload.BusinessModel.Urls)
                    {
                        sites.Add(new SiteCandidate()
                        {
                            Url = url
                        });
                    }

                    mapped.Sites = sites;
                }
            }

            #endregion

            var newApplication = await _dbContext.Applications.AddAsync(mapped);
            await _dbContext.SaveChangesAsync();

            if (newApplication.Entity.Id == Guid.Empty)
                response.AddError("Unable to create application");

            response.ApplicationId = newApplication.Entity.Id;

            await _publisher.Publish(new ApplicationCreatedEvent
            {
                Id = newApplication.Entity.Id,
                Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                Email = payload.PrimaryContact.Email,
                Mid = response.ApplicationId,
                Pid = newApplication.Entity.PartnerId.Value,
                IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                FirstName = payload.PrimaryContact.FirstName,
                LastName = payload.PrimaryContact.LastName,
                Aid = Guid.Empty, // TODO check if we should create new account
                LegalEntityName = payload.LegalEntityName,
            });

            var shouldSendEmail = true; // TODO should get this from partner configuration

            if (shouldSendEmail)
            {
                await _publisher.Publish(new ApplicationInviteRequestedEvent
                {
                    Name = $"{payload.PrimaryContact.FirstName} {payload.PrimaryContact.LastName}",
                    Phone = payload.PrimaryContact.Phone,
                    Email = payload.PrimaryContact.Email,
                    Mid = response.ApplicationId,
                    Pid = newApplication.Entity.PartnerId.Value,
                    IntegrationPartnerId = payload.IntegrationPartnerId ?? Guid.Empty,
                    FirstName = payload.PrimaryContact.FirstName,
                    LastName = payload.PrimaryContact.LastName,
                    Group = SuperAdminGroups.MERCHANT_ADMIN,
                    Aid = Guid.Empty,
                    MerchantState = ActiveInActive.INACTIVE.ToString(),
                });

                try
                {
                    await ApplyApplicationActivity(mapped.Id, new ApplicationActivity
                    {
                        Department = "Application",
                        Category = "Application Send Invite",
                        Activity = "Invite Sent",
                        Note = $"Invite sent to {payload.PrimaryContact.Email}",
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "Error while creating activity - Application Send Invite");
                }
            }

            workspan.Log.Information(
                $"IN: ApplicationService => Publishing ApplicationCreatedEvent for {newApplication.Entity.Id},To Email: {payload.PrimaryContact.FirstName}");

            var shouldConvertToMerchant = false; // TODO should get this from partner configuration
            // var shouldConvertToMerchant = true; // TODO should get this from partner configuration
            var shouldActivateMerchant = false; // TODO should get this from partner configuration

            if (shouldConvertToMerchant)
            {
                // Needed to convert applicant to merchant 
                // needs to get this property from configuration, create configuration
                mapped.SkipRiskAssessment = true;
                mapped.Status = ApplicationStatus.SUBMITTED;
                mapped.AgreeToTerms = DateTime.Now.ToUniversalTime();

                _dbContext.Applications.Update(mapped);
                await _dbContext.SaveChangesAsync();

                var applicationConvertOptions = new ApplicationConvertOptions
                {
                    IsStripeAppMerchant = false,
                    IsActive = shouldActivateMerchant
                };
                await ConvertAsync(newApplication.Entity.Id, newApplication.Entity.PartnerId,
                    applicationConvertOptions);
            }

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to get by id ");
            throw;
        }
    }

    public async Task<ApplicationNewResponse> PublicUpdateAsync(Guid id, Guid partnerId,
        PublicApplicantUpdateDTO payload)
    {
        using var workspan = Workspan.Start<ApplicationService>().Request(payload).LogEnterAndExit();

        try
        {
            var response = new ApplicationNewResponse();

            var entity = await _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Include(x => x.Partner)
                .Include(x => x.IntegrationPartner)
                .Include(x => x.Address)
                .Include(x => x.Fees)
                .Include(x => x.SalesAgency)
                .Include(x => x.Owners)
                .Include(x => x.Sites)
                .SingleOrDefaultAsync(x => x.Id == id && x.IntegrationPartnerId == partnerId);

            if (entity == null)
            {
                throw new FlexNotFoundException($"Application not found for id:{id}");
            }

            if (entity.Status == ApplicationStatus.APPROVED)
            {
                throw new FlexValidationException("Application was converted to merchant");
            }

            #region ContactInformation

            entity.PrimaryContact.FirstName = payload.PrimaryContact.FirstName;
            entity.PrimaryContact.LastName = payload.PrimaryContact.LastName;
            // entity.PrimaryContact.Email = payload.PrimaryContact.Email;
            entity.PrimaryContact.Phone = payload.PrimaryContact.Phone;

            #endregion

            #region Businessformation

            entity.LegalEntityName = payload.LegalEntityName;
            entity.Dba = payload.Dba;
            entity.TaxId = payload.TaxId;
            entity.Mcc = payload.Mcc;
            entity.Descriptor = payload.Descriptor;

            #region BusinessModelInfo

            if (payload.BusinessModel != null)
            {
                entity.SitesCount = payload.BusinessModel?.UrlsCount;

                entity.Sites.Clear();
                if (payload.BusinessModel?.Urls != null)
                {
                    foreach (var url in payload.BusinessModel.Urls)
                    {
                        entity.Sites.Add(new SiteCandidate()
                        {
                            Url = url
                        });
                    }
                }
            }

            #endregion

            #endregion

            #region Bankformation

            entity.DdaType = payload.BankAccountInformation?.DdaType;
            entity.AccountNumber = payload.BankAccountInformation?.AccountNumber;
            entity.RoutingNumber = payload.BankAccountInformation?.RoutingNumber;
            entity.BankName = payload.BankAccountInformation?.BankName;

            #endregion

            #region CustomerSupportInformation

            entity.CustomerSupportLink = payload.CustomerSupportInformation?.CustomerSupportLink;
            entity.CustomerSupportPhone = payload.CustomerSupportInformation?.CustomerSupportPhone;
            entity.CustomerSupportEmail = payload.CustomerSupportInformation?.CustomerSupportEmail;
            entity.CustomerSupportName = payload.CustomerSupportInformation?.CustomerSupportName;

            #endregion

            #region Address

            entity.CustomerSupportLink = payload.Address?.City;
            entity.CustomerSupportLink = payload.Address?.State;
            entity.CustomerSupportLink = payload.Address?.ZipCode;
            entity.CustomerSupportLink = payload.Address?.Country;
            entity.CustomerSupportLink = payload.Address?.Line1;
            entity.CustomerSupportLink = payload.Address?.Line2;
            entity.CustomerSupportLink = payload.Address?.StateCode;

            #endregion

            _dbContext.Applications.Update(entity);
            await _dbContext.SaveChangesAsync();

            response.ApplicationId = entity.Id;

            return response;
        }
        catch (FlexNotFoundException e)
        {
            workspan.Log.Error(e, "NotFoundException");
            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to get by id ");
            throw;
        }
    }

    public async Task<PublicApplicationResponse> PublicGetApplicationByIdAsync(Guid id, Guid? integrationPartnerId)
    {
        var entity = await _dbContext.Applications
            .Include(x => x.Partner)
            .Include(x => x.IntegrationPartner)
            .Include(x => x.Address)
            .Include(x => x.PrimaryContact)
            .Include(x => x.DeveloperContact)
            .Include(x => x.Fees)
            .Include(x => x.SalesAgency)
            .Include(x => x.Owners).ThenInclude(owner => owner.Address)
            .Include(x => x.Sites)
            .Include(application => application.Documents)
            .SingleOrDefaultAsync(x => x.Id == id && x.Status != ApplicationStatus.APPROVED);

        if (entity == null)
            throw new FlexNotFoundException($"Application with Id {id} does not exist");

        if (integrationPartnerId != null && entity.IntegrationPartnerId != integrationPartnerId)
        {
            throw new FlexValidationException("Application not found");
        }

        var applicantDto = new PublicApplicantQueryDTO();
        var response = new PublicApplicationResponse();

        applicantDto.Id = entity.Id;

        #region PriamaryContact

        applicantDto.PrimaryContact = new PrimaryContact()
        {
            FirstName = entity.PrimaryContact.FirstName,
            LastName = entity.PrimaryContact.LastName,
            Email = entity.PrimaryContact.Email,
            Phone = entity.PrimaryContact.Phone,
            UserCreated = entity.PrimaryContact.UserId != Guid.Empty,
            ConfirmedUser = entity.PrimaryContact.ConfirmedUser,
        };

        #endregion

        applicantDto.Status = entity.Status.ToString();

        #region AddressInfo

        applicantDto.Address = new AddressDTO()
        {
            Country = entity.Address?.Country,
            Line1 = entity.Address?.Line1,
            Line2 = entity.Address?.Line2,
            City = entity.Address?.City,
            ZipCode = entity.Address?.ZipCode,
            State = entity.Address?.State,
        };

        #endregion

        #region CustomerSupportInfo

        applicantDto.CustomerSupportInformation = new ApplicationCustomerSupportInformationDTO
        {
            CustomerSupportName = entity.CustomerSupportName,
            CustomerSupportEmail = entity.CustomerSupportEmail,
            CustomerSupportPhone = entity.CustomerSupportPhone,
            CustomerSupportLink = entity.CustomerSupportLink
        };

        #endregion

        #region BankAccountInfo

        applicantDto.BankAccountInformation = new BankAccountInformationDTO
        {
            BankName = entity.BankName,
            DdaType = entity.DdaType,
            AccountNumber = entity.AccountNumber,
            RoutingNumber = entity.RoutingNumber,
            BankAccountVerified = entity.BankAccountVerified,
            RoutingType = entity.RoutingType
        };

        #endregion

        #region BusinessModelInfo

        applicantDto.BusinessModel = new BusinessModelDTO
        {
            UrlsCount = entity.SitesCount,
            Urls = entity.Sites?.Select(x => x.Url).ToList()
        };

        #endregion

        #region Company Info

        applicantDto.LegalEntityName = entity.LegalEntityName;
        applicantDto.Dba = entity.Dba;
        applicantDto.TaxId = entity.TaxId;
        applicantDto.Mcc = entity.Mcc;
        applicantDto.Descriptor = entity.Descriptor;

        #endregion

        #region AccountInfo

        if (entity.PartnerId.HasValue)
        {
            applicantDto.PartnerId = entity.PartnerId.Value;
        }

        #endregion

        #region OwnerInfo

        if (entity.Owners != null && entity.Owners.Count > 0)
        {
            var newOwners = new List<OwnerDTO>();
            foreach (var owner in entity.Owners)
            {
                newOwners.Add(new OwnerDTO
                {
                    Id = owner.Id,
                    Title = owner.Title,
                    FirstName = owner.FirstName,
                    LastName = owner.LastName,
                    Email = owner.Email,
                    Phone = owner.Phone,
                    DateOfBirth = owner.DateOfBirth,
                    PrincipalType = owner.PrincipalType,
                    IndividualFormOfID = owner.IndividualFormOfID,
                    IdentificationNumber = owner.IdentificationNumber,
                    PercentOwnership = owner.PercentOwnership,
                    Address = new AddressDTO()
                    {
                        Line1 = owner.Address?.Line1,
                        Line2 = owner.Address?.Line2,
                        City = owner.Address?.City,
                        State = owner.Address?.State,
                        ZipCode = owner.Address?.ZipCode,
                        Country = owner.Address?.Country
                    }
                });
            }

            applicantDto.Owners = newOwners;
        }

        #endregion

        response.Record = applicantDto;

        return response;
    }

    // get all applications 
    public async Task<PublicApplicationQueryResponse> PublicGetAsync(Guid integrationPartnerId, DateTime? from,
        DateTime? to,
        string orderBy,
        string sortField, int pageSize, int pageNumber)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var response = new PublicApplicationQueryResponse();
            var dbset = _dbContext.Applications
                .Include(x => x.Address)
                .Include(x => x.PrimaryContact)
                .Include(x => x.Fees)
                .Include(x => x.SalesAgency)
                .Include(x => x.Partner)
                .Include(x => x.IntegrationPartner)
                .Include(x => x.Owners)
                .Include(x => x.Sites)
                .Where(x => x.IntegrationPartnerId == integrationPartnerId && x.Status != ApplicationStatus.APPROVED);

            var fromDate = from ?? DateTime.MinValue;
            var toDate = to ?? DateTime.MaxValue;
            var entity = await FilterAndSortApplication(dbset, null, fromDate, toDate, null, null, null, orderBy,
                sortField, pageSize, pageNumber, null);

            if (entity == null)
            {
                response.AddError($"No applications");
                return response;
            }

            response.Results = _mapper.Map<IPagedList<Application>, PagedDTO<PublicApplicantQueryDTO>>(entity);

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to submit application ");
            throw;
        }
    }

    #endregion

    private string GetEmailFromJwt()
    {
        if (_httpContextAccessor.HttpContext == null || _httpContextAccessor.HttpContext.User == null)
        {
            return null;
        }

        var email = _httpContextAccessor.HttpContext.User.Claims
            .FirstOrDefault(c => c.Type.ToString().Contains(ClaimTypes.Email))?.Value;
        return email;
    }

    private Guid? GetUserIdFromJwt()
    {
        if (_httpContextAccessor.HttpContext == null || _httpContextAccessor.HttpContext.User == null)
        {
            return null;
        }

        var IsValid = Guid.TryParse(_httpContextAccessor.HttpContext.User.Claims
            .Where(x => x.Type == ClaimTypes.NameIdentifier)
            .Select(x => x.Value).SingleOrDefault(), out Guid uid);

        return uid;
    }


    #region Admin functions

    public async Task<ApplicationQueryResponse> AdminGetAsync(string q, DateTime from, DateTime to,
        ApplicationStatus?[] statuses,
        OperationsStatus?[] operationsStatuses,
        RiskAssessmentStatus?[] riskStatuses,
        string orderBy,
        string sortField, int pageSize, int pageNumber, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var response = new ApplicationQueryResponse();
            var dbset = _dbContext.Applications
                .Include(x => x.Address)
                .Include(x => x.PrimaryContact)
                .Include(x => x.Fees)
                .Include(x => x.SalesAgency)
                .Include(x => x.Partner)
                .Include(x => x.IntegrationPartner)
                .Include(x => x.Owners)
                .Include(x => x.Sites)
                .Where(x => x.Status != ApplicationStatus.APPROVED);

            var entity =
                await FilterAndSortApplication(dbset, q, from, to, statuses, operationsStatuses, riskStatuses, orderBy,
                    sortField, pageSize, pageNumber, partnerId);

            if (entity == null)
            {
                response.AddError($"No applications");
                return response;
            }

            //response.Applications =  _mapper.Map<IPagedList<Application>, IPagedList<ApplicationDTO>>(entity);
            response.Results = _mapper.Map<IPagedList<Application>, PagedDTO<ApplicationDTO>>(entity);
            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to submit application ");
            throw;
        }
    }

    public async Task<ApplicationSummaryResponse> AdminGetSummaryAsync(Guid? pid, Guid? ipid)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var response = new ApplicationSummaryResponse();
            var dbset = _dbContext.Applications
                .Where(x => x.Status != ApplicationStatus.APPROVED);

            if (pid != null)
                dbset = dbset.Where(x => x.PartnerId == pid);

            if (ipid != null)
                dbset = dbset.Where(x => x.IntegrationPartnerId == ipid);

            var queryResult = await dbset.ToListAsync();
            if (!queryResult.Any())
                return response;

            var riskUnderReview = queryResult.Count(x =>
                x.RiskStatus != nameof(RiskAssessmentStatus.Approved) &&
                x.RiskStatus != nameof(RiskAssessmentStatus.Rejected));
            response.PendingRiskReview = new ApplicationSummaryResponse.SummaryItem()
            {
                Title = "Applications in Underwriting",
                Label = "applications waiting for Risk Review",
                Value = riskUnderReview.ToString(),
                Icon = "fa fa-spinner",
                Color = "warning",
                Link = "/applications",
            };

            //operations
            var operationsReview = queryResult.Count(x => x.OperationsStatus != nameof(OperationsStatus.Completed));
            response.PendingOperationsReview = new ApplicationSummaryResponse.SummaryItem()
            {
                Title = "Applications in Operations",
                Label = "applications waiting for Operations Review",
                Value = operationsReview.ToString(),
                Icon = "fa fa-spinner",
                Color = "info",
                Link = "/applications",
            };

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to get summary ");
            throw;
        }
    }

    public async Task<ApplicationQueryResponse> PartnerGetAsync(Guid pid, bool isIntegrationPartner, string q,
        DateTime from, DateTime to,
        ApplicationStatus?[] statuses,
        OperationsStatus?[] operationsStatuses,
        RiskAssessmentStatus?[] riskStatuses,
        string orderBy,
        string sortField, int pageSize, int pageNumber)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var response = new ApplicationQueryResponse();
            var dbset = _dbContext.Applications
                .Include(x => x.Address)
                .Include(x => x.PrimaryContact)
                .Include(x => x.Fees)
                .Include(x => x.SalesAgency)
                .Include(x => x.Sites)
                .Where(x => (isIntegrationPartner ? x.IntegrationPartnerId == pid : x.PartnerId == pid) &&
                            x.Status != ApplicationStatus.APPROVED);

            var entity =
                await FilterAndSortApplication(dbset, q, from, to, statuses, operationsStatuses, riskStatuses, orderBy,
                    sortField, pageSize, pageNumber, null);
            if (entity == null)
            {
                response.AddError($"No applications");
                return response;
            }

            //response.Applications =  _mapper.Map<IPagedList<Application>, IPagedList<ApplicationDTO>>(entity);
            response.Results = _mapper.Map<IPagedList<Application>, PagedDTO<ApplicationDTO>>(entity);
            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to submit application ");
            throw;
        }
    }

    protected async Task<IPagedList<Application>> FilterAndSortApplication(IQueryable<Application> dbset, string q,
        DateTime from, DateTime to,
        ApplicationStatus?[] statuses,
        OperationsStatus?[] operationsStatuses,
        RiskAssessmentStatus?[] riskStatuses,
        string orderBy,
        string sortField,
        int pageSize,
        int pageNumber,
        Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        try
        {
            dbset = ApplyPartnerFilter(dbset, partnerId);
            dbset = ApplyStatusFilter(dbset, statuses);
            dbset = ApplyOperationsStatusFilter(dbset, operationsStatuses);
            dbset = ApplyRiskStatusFilter(dbset, riskStatuses);
            dbset = ApplyDateFilter(dbset, from, to);
            dbset = ApplySearchFilter(dbset, q);
            dbset = ApplySorting(dbset, orderBy, sortField);

            return await dbset.ToPagedListAsync(pageNumber, pageSize);
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to filter and sort applications");
            throw;
        }
    }

    private IQueryable<Application> ApplyPartnerFilter(IQueryable<Application> dbset, Guid? partnerId)
    {
        if (partnerId != null)
        {
            dbset = dbset.Where(x => x.PartnerId == partnerId);
        }

        return dbset;
    }

    private IQueryable<Application> ApplyStatusFilter(IQueryable<Application> dbset, ApplicationStatus?[] statuses)
    {
        if (statuses != null && statuses.Any())
        {
            dbset = dbset.Where(x => statuses.Contains(x.Status));
        }

        return dbset;
    }

    private IQueryable<Application> ApplyOperationsStatusFilter(IQueryable<Application> dbset,
        OperationsStatus?[] operationsStatuses)
    {
        if (operationsStatuses != null && operationsStatuses.Any())
        {
            var operationsStatusStrings = operationsStatuses.Select(s => s.ToString()).ToArray();
            dbset = dbset.Where(x => operationsStatusStrings.Contains(x.OperationsStatus));
        }

        return dbset;
    }

    private IQueryable<Application> ApplyRiskStatusFilter(IQueryable<Application> dbset,
        RiskAssessmentStatus?[] riskStatuses)
    {
        if (riskStatuses != null && riskStatuses.Any())
        {
            var riskStatusStrings = riskStatuses.Select(s => s.ToString()).ToArray();
            dbset = dbset.Where(x => riskStatusStrings.Contains(x.RiskStatus));
        }

        return dbset;
    }

    private IQueryable<Application> ApplyDateFilter(IQueryable<Application> dbset, DateTime from, DateTime to)
    {
        if (from > DateTime.MinValue && to > DateTime.MinValue)
        {
            dbset = dbset.Where(x => x.CreatedOn.Date >= from.Date && x.CreatedOn.Date <= to.Date);
        }

        return dbset;
    }

    private IQueryable<Application> ApplySearchFilter(IQueryable<Application> dbset, string q)
    {
        if (!string.IsNullOrEmpty(q))
        {
            dbset = dbset.Where(x =>
                x.CompanyName.ToLower().Contains(q.ToLower()) ||
                x.LegalEntityName.ToLower().Contains(q.ToLower()) ||
                x.Dba.ToLower().Contains(q.ToLower()) ||
                x.PrimaryContact.FirstName.ToLower().Contains(q.ToLower()) ||
                x.PrimaryContact.LastName.ToLower().Contains(q.ToLower()));
        }

        return dbset;
    }

    private IQueryable<Application> ApplySorting(IQueryable<Application> dbset, string? orderBy, string? sortField)
    {
        orderBy = orderBy ?? (string.IsNullOrEmpty(sortField) ? "desc" : "asc");

        // Normalize sortField to match the property name in the entity
        sortField = sortField?.ToLower() switch
        {
            "status" => "Status",
            "riskstatus" => "RiskStatus",
            "operationsstatus" => "OperationsStatus",
            "createdon" => "CreatedOn",
            "agreetoterms" => "AgreeToTerms",
            _ => "CreatedOn" // By default we want to sort CreatedOn field in descending order
        };

        dbset = orderBy.ToLower() switch
        {
            "asc" => dbset.OrderBy(x => EF.Property<object>(x, sortField)),
            "desc" => dbset.OrderByDescending(x => EF.Property<object>(x, sortField)),
            _ => dbset
        };

        return dbset;
    }

    public async Task<ApplicationSubmitResponse> SubmitAsync(Guid userId, Guid id, ApplicationSubmitRequest payload)
    {
        using var workspan = Workspan.Start<ApplicationService>()
            .LogEnterAndExit();

        try
        {
            var response = new ApplicationSubmitResponse();

            var entity = await _dbContext.Applications
                .Include(x => x.PrimaryContact)
                .Include(x => x.Documents)
                .Include(x => x.IntegrationPartner)
                .SingleOrDefaultAsync(x => x.Id == id && x.PrimaryContact.UserId == userId);

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{id}");

            if (!payload.AgreeToTerms && !entity.ContractAlreadySigned)
                throw new FlexValidationException("AgreeToTerms", $"Need to agree to terms & conditions");

            // now we use validator on applicant update request to check if documents are attached
            // if (entity.Documents == null || entity.Documents.Count == 0)
            //     throw new FlexValidationException("Documents",
            //         $"You have not attached your processing statements. Processing statements must be attached to every application.");

            if (entity.AgreeToTerms.HasValue && entity.Status == ApplicationStatus.SUBMITTED)
                throw new FlexChargeException($"Application already submitted");

            entity.AgreeToTerms = DateTime.Now.ToUniversalTime();
            entity.Status = ApplicationStatus.SUBMITTED;

            _dbContext.Applications.Update(entity);
            await _dbContext.SaveChangesAsync();

            // Publish application submitted event
            response.Status = ApplicationStatus.SUBMITTED.ToString();
            response.StatusCode = ((int) ApplicationStatus.SUBMITTED).ToString();

            await _publisher.Publish(new ApplicationSubmittedEvent
                {ApplicationId = entity.Id, AgreeToTermsTimeStamp = entity.AgreeToTerms});

            return response;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Error(ve, "ValidationException");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Application",
                Category = "Application Submit Failed",
                Activity = "Submit Failed",
                UpdateMessage = $"Failed to submit application: {id}",
                Note = ve.Message,
            });

            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to submit application");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Application",
                Category = "Application Submit Failed",
                Activity = "Submit Failed",
                UpdateMessage = $"Failed to submit application: {id}",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task<ApplicationCancelResponse> CancelAsync(Guid id, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        try
        {
            workspan.Log.Information($"ENTERED: ApplicationService => CancelAsync: {id}");

            var response = new ApplicationCancelResponse();

            var entity = await GetApplication(id, partnerId);

            if (entity == null)
            {
                response.AddError($"Application not found for id:{id}");
                return response;
            }

            entity.Status = ApplicationStatus.CANCELLED;

            _dbContext.Applications.Update(entity);
            await _dbContext.SaveChangesAsync();

            await _publisher.Publish<ApplicationCancelledEvent>(new
            {
                ApplicationId = entity.Id,
                PrimaryContactEmail = entity.PrimaryContact.Email
            });

            await ApplyApplicationActivity(entity.Id, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Application Cancelled",
                Activity = "Application Cancelled",
                UpdateMessage = $"Application {entity?.LegalEntityName} was cancelled",
            });

            return response;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Error(ve, "FlexChargeValidationException");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Application Cancel Failed",
                Activity = "Application Cancel Failed",
                UpdateMessage = $"Failed to cancel application - {id}",
                Note = ve.Message,
            });

            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e,
                "EXCEPTION: ApplicationService => CancelAsync => Unable to cancel application {Id}",
                id);

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Risk",
                Category = "Application Cancel Failed",
                Activity = "Application Cancel Failed",
                UpdateMessage = $"Failed to cancel application - {id}",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task<ApplicationDeclineResponse> DeclineAsync(Guid id, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        try
        {
            workspan.Log.Information($"ENTERED: ApplicationService => DeclineAsync: {id}");

            var response = new ApplicationDeclineResponse();

            var entity = await GetApplication(id, partnerId);

            if (entity == null)
            {
                response.AddError($"Application not found for id:{id}");
                return response;
            }

            entity.Status = ApplicationStatus.DECLINED;

            _dbContext.Applications.Update(entity);
            await _dbContext.SaveChangesAsync();

            await _publisher.Publish<ApplicationDeclinedEvent>(new
            {
                ApplicationId = entity.Id,
                PrimaryContactEmail = entity.PrimaryContact.Email
            });

            await ApplyApplicationActivity(entity.Id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Application Declined",
                Activity = "Application Declined",
                UpdateMessage = $"Application {entity.LegalEntityName} was declined",
            });

            return response;
        }
        catch (FlexValidationException ve)
        {
            workspan.Log.Error(ve, "FlexChargeValidationException");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Application Decline Failed",
                Activity = "Application Decline Failed",
                UpdateMessage = $"Failed to decline application - {id}",
                Note = ve.Message,
            });

            throw;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => DeclineAsync => Unable to decline application {Id}",
                id);

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Application Decline Failed",
                Activity = "Application Decline Failed",
                UpdateMessage = e.Message,
            });

            throw;
        }
    }

    public async Task<ApplicationConvertResponse> ConvertAsync(Guid id, Guid? partnerId,
        ApplicationConvertOptions convertOptions)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            ApplicationConvertResponse response = new ApplicationConvertResponse();

            var query = _dbContext.Applications
                .Include(x => x.Address)
                .Include(x => x.PrimaryContact)
                .Include(x => x.DeveloperContact)
                .Include(x => x.Fees)
                .Include(x => x.Documents)
                .Include(x => x.SalesAgency)
                .Include(x => x.Sites)
                .Include(x => x.Owners)
                .Include(x => x.Activities)
                .Where(x => x.Id == id)
                .AsQueryable();

            if (partnerId != null && partnerId != Guid.Empty)
            {
                query = query.Where(x => x.Id == id && x.PartnerId == partnerId);
            }

            var entity = await query.SingleOrDefaultAsync();

            if (!entity.SkipRiskAssessment && entity.RiskStatus != nameof(RiskAssessmentStatus.Approved) &&
                entity.RiskStatus != RiskAssessmentStatus.ProvisionallyApproved.ToString())
            {
                throw new FlexValidationException("General",
                    "Application must be approved by risk before converting application");
            }

            if (!entity.SkipRiskAssessment && entity.OperationsStatus != nameof(OperationsStatus.Completed))
            {
                throw new FlexValidationException("General",
                    "Application must be approved by operations before converting application");
            }

            if (entity == null)
                throw new FlexValidationException($"Application not found for id:{id}");

            if (entity.Status != ApplicationStatus.SUBMITTED || !entity.AgreeToTerms.HasValue)
                throw new FlexValidationException(nameof(entity.Status),
                    "Only submitted application can be converted (Save and send to applicant for submission)");

            var newMerchant = _mapper.Map<Merchant>(entity);

            if (entity.AccountId == null)
            {
                var account = _dbContext.Accounts.Add(new Account()
                {
                    Name = entity.LegalEntityName,
                    PartnerId = entity.PartnerId,
                    IntegrationPartnerId = entity.IntegrationPartnerId,
                });
                entity.AccountId = account.Entity.Id;
            }

            // newMerchant.Account = new Account() {Name = newMerchant.CompanyName};
            newMerchant.ApplicationId = entity.Id;
            newMerchant.Status = ActiveInActive.ACTIVE;
            newMerchant.PrimaryColor = newMerchant.SecondaryColor = "#000000";
            newMerchant.GhostModeThrottlePercentage = 1;
            newMerchant.OfferRequestsThrottlePercentage = 0;
            newMerchant.Offer_NSF_RequestsThrottle_Percentage = 0;
            newMerchant.DynamicAuthorizationDiscountThrottlePercentage = 0;
            newMerchant.OfferRequestsMaxPerDay = null;
            newMerchant.OfferRequestsRateLimitCount = null;
            newMerchant.OfferRequestsRateLimitIntervalMS = null;
            newMerchant.Orders_MaxMonthlyAmount = null;
            newMerchant.UIWidgetOptional = true;
            newMerchant.AccountId = entity.AccountId;
            newMerchant.MinOrderAmount = 900;
            newMerchant.MaxOrderAmount = 30000;
            newMerchant.RiskLevel = 1;
            newMerchant.RiskLevel_Visa = 1;
            newMerchant.SupportedCountries = "US";
            newMerchant.CustomerSupportLink = entity.CustomerSupportLink;
            newMerchant.Documents = entity.Documents;
            newMerchant.IpAddress = entity.IpAddress;
            newMerchant.UserAgent = entity.UserAgent;
            newMerchant.IntegrationPartnerParticipateSale = entity.IntegrationPartnerParticipateSale;
            newMerchant.AnnualSalesVolume = entity.AnnualSalesVolume;
            newMerchant.CrmId = entity.CrmId;

            newMerchant.Status = convertOptions.IsActive
                ? ActiveInActive.ACTIVE
                : ActiveInActive.INACTIVE;


            if (convertOptions.IsStripeAppMerchant == true)
            {
                newMerchant.Status = ActiveInActive.INACTIVE;
                newMerchant.Locked = false;
                newMerchant.PayoutsEnabled = true;
                newMerchant.AccountUpdaterEnabled = true;
                newMerchant.EnableGlobalNetworkTokenization = true;
                newMerchant.IsCrawlingEnabled = true;
                newMerchant.IsIframeMessagesCollectEnabled = false;
                newMerchant.MITEvaluateAsync = true;
                newMerchant.CITEvaluateAsync = false;
                newMerchant.VirtualTerminalEnabled = false;
                newMerchant.BillingInformationOptional = true;
                newMerchant.AllowBinCheckOnTokenization = true;
                newMerchant.UseDefaultSiteForUnknownMerchantUrlsEnabled = true;
                newMerchant.IsMitEnabled = true;
                newMerchant.MITGetSiteByDynamicDescriptorEnabled = false;
                newMerchant.IsSenseJsOptional = false;
                newMerchant.UIWidgetOptional = true;
                newMerchant.CITConsumerNotificationsEnabled = false;
                newMerchant.MITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerCuresEnabled = false;
                newMerchant.CITClickToRefundEnabled = false;
                newMerchant.MITClickToRefundEnabled = false;
                newMerchant.SchemeTransactionIdEnabled = false;
                newMerchant.MITImmediateRetryEnabled = true; // to help with review
                //EnvironmentHelper.IsInSandboxOrStagingOrDevelopment; // to help with testing

                newMerchant.GhostModeThrottlePercentage = 0;
                newMerchant.OfferRequestsThrottlePercentage = 1;
                newMerchant.OfferRequestsMaxPerDay = 10000000;
                newMerchant.OfferRequestsRateLimitCount = 10000;
                newMerchant.OfferRequestsRateLimitIntervalMS = 1000;
                newMerchant.Orders_MaxMonthlyAmount = 10000000;
                newMerchant.Offer_NSF_RequestsThrottle_Percentage = 1;
                newMerchant.MITAgreedExpiryHours = 24;
                newMerchant.MinOrderAmount = 1; //???
                newMerchant.MaxOrderAmount = 30000; //???

                newMerchant.IsAvsRequired = false;
                newMerchant.IsCvvRequired = false;

                newMerchant.IsMitEnabled = true;
                newMerchant.SupportedCountries = "US"; //???

                newMerchant.ConsumerOrderNotificationChannel = ConsumerNotificationChannel.Email;

                newMerchant.RecyclingStrategyWorkflowId = new Guid("2f23f4df-c11f-458f-97e3-e9c40d2b517a");
                newMerchant.EligibilityStrategyWorkflowId = new Guid("c4d39df4-01a8-406d-9c75-d283ad62a0e8");
                newMerchant.NotEligibleOrderProcessingWorkflowId = new Guid("17e9354b-f722-49de-ad42-14ee181a8ef2");
                newMerchant.NotEligibleEverOrderProcessingWorkflowId = new Guid("dae3e65d-d836-4945-b55d-d147cbd092a5");
            }
            else if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment &&
                     newMerchant.IntegrationType != nameof(MerchantIntegrationTypes.TERMINAL))
            {
                newMerchant.Locked = false;
                newMerchant.PayoutsEnabled = true;
                newMerchant.AccountUpdaterEnabled = true;
                newMerchant.EnableGlobalNetworkTokenization = true;
                newMerchant.IsCrawlingEnabled = true;
                newMerchant.IsIframeMessagesCollectEnabled = false;
                newMerchant.MITEvaluateAsync = true;
                newMerchant.CITEvaluateAsync = false;
                newMerchant.VirtualTerminalEnabled = false;
                newMerchant.BillingInformationOptional = false;
                newMerchant.AllowBinCheckOnTokenization = true;
                newMerchant.UseDefaultSiteForUnknownMerchantUrlsEnabled = false;
                newMerchant.IsMitEnabled = true;
                newMerchant.MITGetSiteByDynamicDescriptorEnabled = false;
                newMerchant.IsSenseJsOptional = false;
                newMerchant.UIWidgetOptional = true;
                newMerchant.CITConsumerNotificationsEnabled = false;
                newMerchant.MITConsumerNotificationsEnabled = false;
                newMerchant.MITConsumerCuresEnabled = false;
                newMerchant.CITClickToRefundEnabled = false;
                newMerchant.MITClickToRefundEnabled = false;
                newMerchant.SchemeTransactionIdEnabled = false;
                newMerchant.MITImmediateRetryEnabled = false;

                newMerchant.GhostModeThrottlePercentage = 0;
                newMerchant.OfferRequestsThrottlePercentage = 1;
                newMerchant.OfferRequestsMaxPerDay = 10000000;
                newMerchant.OfferRequestsRateLimitCount = 10000;
                newMerchant.OfferRequestsRateLimitIntervalMS = 1000;
                newMerchant.Orders_MaxMonthlyAmount = 10000000;
                newMerchant.Offer_NSF_RequestsThrottle_Percentage = 1;
                newMerchant.MITAgreedExpiryHours = 24;
                newMerchant.MinOrderAmount = 1;
                newMerchant.MaxOrderAmount = 30000;

                newMerchant.IsAvsRequired = false;
                newMerchant.IsCvvRequired = false;
            }
            else if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment &&
                     newMerchant.IntegrationType == nameof(MerchantIntegrationTypes.TERMINAL))
            {
                newMerchant.Locked = false;
                newMerchant.PayoutsEnabled = true;
                newMerchant.AccountUpdaterEnabled = false;
                newMerchant.EnableGlobalNetworkTokenization = true;
                newMerchant.IsCrawlingEnabled = false;
                newMerchant.IsIframeMessagesCollectEnabled = false;
                newMerchant.MITEvaluateAsync = true;
                newMerchant.CITEvaluateAsync = false;
                newMerchant.VirtualTerminalEnabled = false;
                newMerchant.BillingInformationOptional = false;
                newMerchant.AllowBinCheckOnTokenization = true;
                newMerchant.UseDefaultSiteForUnknownMerchantUrlsEnabled = false;
                newMerchant.IsMitEnabled = false;
                newMerchant.MITGetSiteByDynamicDescriptorEnabled = false;
                newMerchant.IsSenseJsOptional = false;
                newMerchant.UIWidgetOptional = true;
                newMerchant.CITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerCuresEnabled = false;
                newMerchant.CITClickToRefundEnabled = false;
                newMerchant.MITClickToRefundEnabled = false;
                newMerchant.SchemeTransactionIdEnabled = false;
                newMerchant.MITImmediateRetryEnabled = false;

                newMerchant.GhostModeThrottlePercentage = 0;
                newMerchant.OfferRequestsThrottlePercentage = 1;
                newMerchant.OfferRequestsMaxPerDay = 10000000;
                newMerchant.OfferRequestsRateLimitCount = 10000;
                newMerchant.OfferRequestsRateLimitIntervalMS = 1000;
                newMerchant.Orders_MaxMonthlyAmount = 10000000;
                newMerchant.Offer_NSF_RequestsThrottle_Percentage = 1;
                newMerchant.MITAgreedExpiryHours = 24;
                newMerchant.MinOrderAmount = 1;
                newMerchant.MaxOrderAmount = 30000;

                newMerchant.IsAvsRequired = false;
                newMerchant.IsCvvRequired = false;
            }
            else if (EnvironmentHelper.IsInProduction &&
                     newMerchant.IntegrationType != nameof(MerchantIntegrationTypes.TERMINAL))
            {
                newMerchant.Locked = false;
                newMerchant.PayoutsEnabled = true;
                newMerchant.AccountUpdaterEnabled = true;
                newMerchant.EnableGlobalNetworkTokenization = true;
                newMerchant.IsCrawlingEnabled = true;
                newMerchant.IsIframeMessagesCollectEnabled = false;
                newMerchant.MITEvaluateAsync = true;
                newMerchant.CITEvaluateAsync = false;
                newMerchant.VirtualTerminalEnabled = false;
                newMerchant.BillingInformationOptional = false;
                newMerchant.AllowBinCheckOnTokenization = true;
                newMerchant.UseDefaultSiteForUnknownMerchantUrlsEnabled = false;
                newMerchant.IsMitEnabled = true;
                newMerchant.MITGetSiteByDynamicDescriptorEnabled = false;
                newMerchant.IsSenseJsOptional = false;
                newMerchant.UIWidgetOptional = true;
                newMerchant.CITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerCuresEnabled = false;
                newMerchant.CITClickToRefundEnabled = false;
                newMerchant.MITClickToRefundEnabled = false;
                newMerchant.SchemeTransactionIdEnabled = false;
                newMerchant.MITImmediateRetryEnabled = false;
                newMerchant.IsEnforceMFAEnabled = true;

                newMerchant.GhostModeThrottlePercentage = 0;
                newMerchant.OfferRequestsThrottlePercentage = 1;
                newMerchant.OfferRequestsMaxPerDay = 10000;
                newMerchant.OfferRequestsRateLimitCount = 1000;
                newMerchant.OfferRequestsRateLimitIntervalMS = 1000;
                newMerchant.Orders_MaxMonthlyAmount = 100000;
                newMerchant.Offer_NSF_RequestsThrottle_Percentage = 1;
                newMerchant.MITAgreedExpiryHours = 504;

                newMerchant.IsAvsRequired = false;
                newMerchant.IsCvvRequired = false;
            }
            else if (EnvironmentHelper.IsInProduction &&
                     newMerchant.IntegrationType == nameof(MerchantIntegrationTypes.TERMINAL))
            {
                newMerchant.Locked = false;
                newMerchant.PayoutsEnabled = true;
                newMerchant.AccountUpdaterEnabled = false;
                newMerchant.EnableGlobalNetworkTokenization = true;
                newMerchant.IsCrawlingEnabled = false;
                newMerchant.IsIframeMessagesCollectEnabled = false;
                newMerchant.MITEvaluateAsync = true;
                newMerchant.CITEvaluateAsync = false;
                newMerchant.VirtualTerminalEnabled = false;
                newMerchant.BillingInformationOptional = false;
                newMerchant.AllowBinCheckOnTokenization = true;
                newMerchant.UseDefaultSiteForUnknownMerchantUrlsEnabled = false;
                newMerchant.IsMitEnabled = false;
                newMerchant.MITGetSiteByDynamicDescriptorEnabled = false;
                newMerchant.IsSenseJsOptional = false;
                newMerchant.UIWidgetOptional = true;
                newMerchant.CITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerNotificationsEnabled = true;
                newMerchant.MITConsumerCuresEnabled = false;
                newMerchant.CITClickToRefundEnabled = false;
                newMerchant.MITClickToRefundEnabled = false;
                newMerchant.SchemeTransactionIdEnabled = false;
                newMerchant.MITImmediateRetryEnabled = false;
                newMerchant.IsEnforceMFAEnabled = true;

                newMerchant.GhostModeThrottlePercentage = 0;
                newMerchant.OfferRequestsThrottlePercentage = 1;
                newMerchant.OfferRequestsMaxPerDay = 10000;
                newMerchant.OfferRequestsRateLimitCount = 1000;
                newMerchant.OfferRequestsRateLimitIntervalMS = 1000;
                newMerchant.Orders_MaxMonthlyAmount = 100000;
                newMerchant.Offer_NSF_RequestsThrottle_Percentage = 1;
                newMerchant.MITAgreedExpiryHours = 504;

                newMerchant.IsAvsRequired = false;
                newMerchant.IsCvvRequired = false;
            }


            newMerchant.Sites = new List<Site>();

            if (entity.Sites == null || entity.Sites.Count == 0)
            {
                newMerchant.Sites.Add(new Site
                {
                    MerchantId = newMerchant.Id,
                    Name = newMerchant.Dba ?? "default",
                    Descriptor = newMerchant.Descriptor,
                    DescriptorCity = newMerchant.Address?.City,
                    CustomerSupportName = newMerchant.CustomerSupportName,
                    CustomerSupportEmail = newMerchant.CustomerSupportEmail,
                    CustomerSupportPhone = newMerchant.CustomerSupportPhone,
                    CustomerSupportLink = newMerchant.CustomerSupportLink,
                    WhitelistedUrls = new List<SiteWhitelistedUrl>
                    {
                        new()
                        {
                            Link = String.IsNullOrEmpty(entity.Website) ? null : new Uri(entity.Website)
                        }
                    }
                });
            }
            else
            {
                foreach (var site in entity.Sites)
                {
                    var newSite = new Site
                    {
                        MerchantId = newMerchant.Id,
                        Name = newMerchant.Dba ?? "default",
                        Descriptor = newMerchant.Descriptor,
                        DescriptorCity = newMerchant.Address?.City,
                        CustomerSupportName = newMerchant.CustomerSupportName,
                        CustomerSupportEmail = newMerchant.CustomerSupportEmail,
                        CustomerSupportPhone = newMerchant.CustomerSupportPhone,
                        CustomerSupportLink = newMerchant.CustomerSupportLink,
                        WhitelistedUrls = new List<SiteWhitelistedUrl>
                        {
                            new()
                            {
                                Link = String.IsNullOrEmpty(site.Url) ? null : new Uri(site.Url)
                            }
                        }
                    };

                    newMerchant.Sites.Add(newSite);
                }
            }

            try
            {
                if (newMerchant.Sites.Count > 0)
                {
                    foreach (var site in newMerchant.Sites)
                    {
                        await _publisher.Publish(new MerchantSiteCreatedEvent
                        {
                            Mid = entity.Id,
                            SiteId = site.Id,
                            Name = newMerchant.Dba ?? "default",
                            Descriptor = site.Descriptor,
                            DescriptorCity = newMerchant.Address?.City,
                            CustomerSupportName = site.CustomerSupportName,
                            CustomerSupportEmail = site.CustomerSupportEmail,
                            CustomerSupportPhone = site.CustomerSupportPhone,
                            CustomerSupportLink = site.CustomerSupportLink,
                            WhitelistedUrls = site.WhitelistedUrls.Select(x => x.Link),
                        });

                        await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                        {
                            Department = "Operations",
                            Category = "Application",
                            Activity = "Merchant Site Created",
                            UpdateMessage = $"Site for application {entity.LegalEntityName} was created",
                        });
                    }
                }
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: ApplicationService => ConvertAsync => Unable to publish MerchantSiteCreatedEvent");

                try
                {
                    await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                    {
                        Department = "Operations",
                        Category = "Application",
                        Activity = "Merchant site creation failed",
                        UpdateMessage = $"Failed to create site for application {entity.LegalEntityName}",
                        Note = e.Message,
                    });
                }
                catch (Exception ex)
                {
                    workspan.Log.Error(ex,
                        "EXCEPTION: ApplicationService => ConvertAsync => Unable to create Application Activity");
                }
            }


            entity.Status = ApplicationStatus.APPROVED;

            _dbContext.Update(entity);
            await _dbContext.AddAsync(newMerchant);
            await _dbContext.SaveChangesAsync();

            response.MerchantId = newMerchant.Id;

            try
            {
                try
                {
                    await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                    {
                        Department = "Operations",
                        Category = "Application",
                        Activity = "Application Converted",
                        UpdateMessage = $"Application {entity.LegalEntityName} was converted to merchant",
                    });
                }
                catch (Exception e)
                {
                    workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to create Application Activity");
                }
            }
            catch (Exception e)
            {
                workspan.Log.Fatal(e,
                    $"ApplicationService => Unable to publish ApplicationConvertedEvent; merchantId: {entity.Id}");

                await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                {
                    Department = "Operations",
                    Category = "Application",
                    Activity = "Application Converted Event Failed",
                    UpdateMessage =
                        $"Failed to publish ApplicationConvertedEvent for application {entity.LegalEntityName}",
                    Note = e.Message,
                });
            }

            await _publisher.Publish(MerchantUpdateEventsFactory.ConstructMerchantCreatedEvent(newMerchant, _mapper));

            try
            {
                await ApplyApplicationActivity(entity.Id, new ApplicationActivity
                {
                    Department = "Operations",
                    Category = "Application",
                    Activity = "Merchant Created",
                    UpdateMessage = $"Merchant {entity.LegalEntityName} Created",
                });
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to create Application Activity");
            }

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to convert application ");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Application Convert Failed",
                Activity = "Application Convert Failed",
                UpdateMessage = $"Failed to convert application: {id}",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task<ApplicationStatusResponse> StatusAsync(Guid id)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var response = new ApplicationStatusResponse();
            var entity = await _dbContext.FindAsync<Application>(id);
            if (entity == null)
                return null;

            response.Status = entity.Status;

            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e,
                $"EXCEPTION: ApplicationService => Unable to get the status of an application {id}");
            throw;
        }
    }

    public async Task<ApplicationReopenResponse> ReopenAsync(Guid id, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var response = new ApplicationReopenResponse();

            var entity = await GetApplication(id, partnerId);

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{id}");

            entity.Status = ApplicationStatus.DRAFT;

            await ApplyApplicationActivity(entity.Id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Application",
                Activity = "Application Reopened",
                UpdateMessage = $"Reopened application {entity.LegalEntityName}",
            });

            await _dbContext.SaveChangesAsync();
            response.StatusName = entity.Status.ToString();
            return response;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to reopen application");

            await ApplyApplicationActivity(id, new ApplicationActivity
            {
                Department = "Operations",
                Category = "Application Reopen Failed",
                Activity = "Application Reopen Failed",
                UpdateMessage = $"Failed to reopen application: {id}",
                Note = e.Message,
            });

            throw;
        }
    }

    public async Task<IEnumerable<MccCodesDTO>> MccCodes()
    {
        var mccCodes = await _dbContext.MccCodes.ToListAsync();
        return _mapper.Map<IEnumerable<MccCodesDTO>>(mccCodes);
    }

    public async Task ApplyApplicationActivity(Guid applicationId, ApplicationActivity activity)
    {
        using var workspan = Workspan.Start<ApplicationService>().LogEnterAndExit();

        try
        {
            var userId = GetUserIdFromJwt();

            if (userId != null)
            {
                activity.UserId = userId.Value;
            }

            activity.User = GetEmailFromJwt();

            var entity = await _dbContext.Applications
                .Include(x => x.Activities)
                .SingleOrDefaultAsync(x => x.Id == applicationId);

            if (entity == null)
                throw new FlexNotFoundException($"Application not found for id:{applicationId}");

            entity.Activities.Add(activity);
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: ApplicationService => Unable to apply activity");
            throw;
        }
    }


    // Id is merchantId for merchant or partnerId for partner
    public async Task<string> StoreLogoImageAsync(Guid id, BaseResponse? response, string logoFileBase64)
    {
        using var workspan = Workspan.Start<ApplicationService>();

        string logoUrl;

        string logoStorageName = null, logoStorageFolder = null, logoFileName = null;


        // Removing "data:image/png;base64," data format prefix, if exists
        if (logoFileBase64.StartsWith("data:"))
        {
            int commaIndex = logoFileBase64.IndexOf(',');
            if (commaIndex > 0)
            {
                logoFileBase64 = logoFileBase64.Substring(commaIndex + 1);
            }
        }

        var logoValidationOptions = _logoImageValidationOptions.Value;
        (byte[] image, int Width, int Height) logoImage;
        try
        {
            var imageBytes = Convert.FromBase64String(logoFileBase64);
            using (var readStream = new MemoryStream(imageBytes))
            {
                logoImage = await ImageValidation.LoadAndVerifyImageAsync(readStream, logoValidationOptions,
                    sanitize: true);
            }

            logoStorageName = Environment.GetEnvironmentVariable("AWS_S3_STATIC_FILES_BUCKET");
            logoStorageFolder = $"{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}/{id}";

            logoFileName = $"logo_w{logoImage.Width}_h{logoImage.Height}.png";
            using (var image_stream = new MemoryStream(logoImage.image))
            {
                await _cloudStorage.UploadFileAsync(image_stream, logoStorageName,
                    logoFileName,
                    logoStorageFolder,
                    allowPublicAccess: false);
            }

            //logoUrl = $"{logoStorageName}/{logoStorageFolder}/{logoFileName}";
            logoUrl = await _cloudStorage.CreatePublicFileUrlThroughCDNAsync(logoStorageName, logoFileName,
                logoStorageFolder);
        }
        catch (Exception e)
        {
            workspan.Log.Information(
                $"VALIDATION: ApplicationService => CreateAsync > Unable to upload logo > {e.Message} ");

            string userFriendlyMessage = e is ImageValidation.ImageValidationException ? $": {e.Message}" : "";

            if (response != null)
            {
                response.AddError(
                    $"Unable to upload logo image{userFriendlyMessage}",
                    nameof(ApplicationNewRequest.LogoFile));
            }


            #region Removing logo from cloud storage

            if (!string.IsNullOrWhiteSpace(logoFileName))
            {
                await _cloudStorage.DeleteFileAsync(logoStorageName, logoFileName, logoStorageFolder);
            }

            #endregion

            return null;
        }

        return logoUrl;
    }

    #endregion
}