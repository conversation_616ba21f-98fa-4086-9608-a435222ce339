using FlexCharge.Merchants.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.HTTP;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Mvc;
using FlexCharge.Common.Recaptcha;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Services.ApplicationServices;
using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace FlexCharge.Merchants.Controllers
{
    [Route("[controller]")]
    [JwtAuth]
    [ApiController]
    public partial class OnboardingController : BaseController
    {
        private readonly IApplicationService _applicationService;
        private readonly AppOptions _globalData;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IRecaptchaService recaptchaService;
        private readonly IPublishEndpoint _publisher;
        private readonly IRequestClient<GenerateProviderOAuthLinkCommand> _generateProviderOAuthLinkCommand;

        public OnboardingController(PostgreSQLDbContext context, IApplicationService applicationService,
            IOptions<AppOptions> globalData, IUrlShortenerService urlShortenerService,
            IRecaptchaService recaptchaService, IPublishEndpoint publisher,
            IRequestClient<GenerateProviderOAuthLinkCommand> generateProviderOAuthLinkCommand)
        {
            _globalData = globalData.Value;
            _applicationService = applicationService;
            _dbContext = context;
            this.recaptchaService = recaptchaService;
            _publisher = publisher;
            _generateProviderOAuthLinkCommand = generateProviderOAuthLinkCommand;
        }


        /// <summary>
        /// Sign up - used for sandbox only when we create
        /// the merchants without any admin involvment
        /// </summary>
        /// <param name="request"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost("signup")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SignUpResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> QuickSignUpPost(SignUpRequestDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                if (EnvironmentHelper.IsInProduction)
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var result = await this.recaptchaService.CheckRecaptchaAsync(request.RecaptchaToken, request.Email);
                if (!result)
                {
                    ModelState.AddModelError("Captcha", "Captcha is not valid");
                    return ValidationProblem();
                }

                await _applicationService.QuickSignupAsync(request);

                return Ok(new SignUpResponseDTO());
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }

        [HttpPost("stripe-signup")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(SignUpResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> StripeSignUpPost(StripeSignUpRequestDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, request, _globalData);
            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                if (!request.StripeAgreeToTerms)
                {
                    ModelState.AddModelError("stripeAgreeToTerms", "You must agree to the terms");
                    return ValidationProblem();
                }

                var result = await this.recaptchaService.CheckRecaptchaAsync(request.RecaptchaToken, request.Email);
                if (!result)
                {
                    ModelState.AddModelError("Captcha", "Captcha is not valid");
                    return ValidationProblem();
                }

                var ipAddress = HttpContextHelper.GetConsumerIP(HttpContext);
                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

                request.IpAddress = ipAddress;
                request.UserAgent = userAgent;

                var application = await _applicationService.StripeQuickSignupAsync(request);
                var response = new SignUpResponseDTO
                {
                    ApplicationId = application.ApplicationId
                };

                var generatedProviderOAuthLinkCommandResponse = (await _generateProviderOAuthLinkCommand
                    .RunCommandAsync<GenerateProviderOAuthLinkCommand, GenerateProviderOAuthLinkCommandResponse>(
                        new GenerateProviderOAuthLinkCommand(
                            OAuthProvider.Stripe,
                            RelatedEntityType.Merchant,
                            application.ApplicationId
                        ), token));

                if (generatedProviderOAuthLinkCommandResponse == null ||
                    generatedProviderOAuthLinkCommandResponse.Message == null)
                {
                    throw new InvalidOperationException("Failed to generate OAuth link.");
                }

                response.Links = new List<HateoasLink>();
                response.Links.Add(new HateoasLink
                {
                    Href = generatedProviderOAuthLinkCommandResponse.Message.OAuthLink,
                    Rel = "external",
                    Method = HttpVerb.GET.ToString(),
                    Category = "Stripe",
                    ActionName = "RedirectToStripeConnect",
                    Description = "Redirect to Stripe Connect Onboarding"
                });

                return Ok(response);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a application");
            }
        }


        /// <summary>
        /// Full form that the applicant needs to fill
        /// and submit for Risk and Ops review
        /// </summary>
        /// <param name="request"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPut("applicant/{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(ApplicationUpdateResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ApplicantBoardingPut([FromRoute] Guid id, ApplicantBoardingRequestDTO request,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);

            try
            {
                var applicant = await _dbContext.Applications
                    .Include(x => x.Fees)
                    .Include(x => x.Address)
                    .Include(x => x.PrimaryContact)
                    .Include(x => x.DeveloperContact)
                    .Include(x => x.SalesAgency)
                    .Include(x => x.Owners)
                    .Include(x => x.Sites)
                    .Include(x => x.Documents)
                    .Include(x => x.IntegrationPartner)
                    .SingleOrDefaultAsync(a => a.Id == GetMID(), token);
    
                if (applicant == null)
                {
                    return NotFound("Applicant not found.");
                }

                AbstractValidator<ApplicantBoardingRequestDTO> validator = new ApplicantBoardingRequestValidator();
                
                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN))
                {
                    var onboardingConfiguration = applicant.IntegrationPartner.OnboardingConfiguration;

                    if (onboardingConfiguration != null)
                    {
                        var onboardingConfig = onboardingConfiguration.RootElement;
                        var applicantUpdateValidation = onboardingConfig.GetProperty("applicantBoardingUpdateValidation").GetString();
                        if (applicantUpdateValidation == "ValorPayTechApplicantBoardingUpdateRequestValidator")
                        {
                            validator = new ValorPayTechApplicantBoardingRequestValidator();
                        }
                    }
                }

                var validationResult = await validator.ValidateAsync(request, token);

                if (!validationResult.IsValid)
                    ModelState.AddFluentValidationErrors(validationResult);

                return !ModelState.IsValid
                    ? ValidationProblem()
                    : Ok(await _applicationService.ApplicantUpdateAsync(applicant, request));
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating application");
            }
        }

        /// <summary>
        /// User Application Get
        /// </summary>
        /// <param name="request"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet("applicant/{id:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(ApplicantGetResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ApplicantApplicationGet(Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);
            try
            {
                return Ok(await _applicationService.ApplicantApplicationGet(id));
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status404NotFound, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching a application");
            }
        }


        // //TODO Check PostMan This EndPoint
        // [HttpGet("applicant/{id:guid}/summary")]
        // [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        // [ProducesResponseType(typeof(ApplicantGetResponse), StatusCodes.Status200OK)]
        // [ProducesResponseType(StatusCodes.Status400BadRequest)]
        // [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        // public async Task<IActionResult> ApplicantApplicationSummaryGet([FromRoute] Guid id, CancellationToken token)
        // {
        //     using var workspan = Workspan.StartEndpoint<OnboardingController>(this, id, _globalData);
        //     try
        //     {
        //         return !ModelState.IsValid
        //             ? ValidationProblem()
        //             : Ok(await _applicationService.AdminGetApplicationByIdAsync(id));
        //     }
        //     catch (FlexValidationException vex)
        //     {
        //         ModelState.AddModelError(vex.Property, vex.Message);
        //         return ValidationProblem(ModelState);
        //     }
        //     catch (FlexNotFoundException e)
        //     {
        //         workspan.RecordFatalException(e);
        //         return StatusCode(StatusCodes.Status404NotFound, e.Message);
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.RecordFatalException(e);
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching a application");
        //     }
        // }


        [HttpPost("applicant/{id:guid}/submit")]
        [Authorize(MyPolicies.MERCHANT_ADMINS)]
        [ProducesResponseType(typeof(ApplicationSubmitResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Submit([FromRoute] Guid id, ApplicationSubmitRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>()
                .Baggage("ApplicationId", id)
                .LogEnterAndExit();

            try
            {
                return ReturnResponse(await _applicationService.SubmitAsync(GetUserId(), id, payload));
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }


        [HttpGet()]
        [Route("{id:guid}/status")]
        [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        [ProducesResponseType(typeof(ApplicationStatusResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetStatus([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>();
            try
            {
                workspan.Log.Information(
                    "ENTERED: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return ReturnResponse(await _applicationService.StatusAsync(id));
            }
            catch (FlexNotFoundException e)
            {
                workspan.Log.Error(e,
                    "NOT FOUND: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return NotFound();
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        [HttpGet()]
        [Route("mcc")]
        [ProducesResponseType(typeof(IEnumerable<MccCodesDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMccCodes(CancellationToken token)
        {
            using var workspan = Workspan.Start<OnboardingController>()
                .LogEnterAndExit();
            try
            {
                return Ok(await _applicationService.MccCodes());
            }
            catch (FlexNotFoundException e)
            {
                workspan.Log.Error(e,
                    "NOT FOUND: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return NotFound();
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
            }
        }

        // [HttpPut("documents")]
        // [Authorize(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS)]
        // [ProducesResponseType(StatusCodes.Status200OK)]
        // [ProducesResponseType(StatusCodes.Status400BadRequest)]
        // [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        // public async Task<IActionResult> UploadDocuments([FromForm] List<IFormFile> files, CancellationToken token)
        // {
        //     using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
        //     try
        //     {
        //         if (files == null || !files.Any())
        //         {
        //             return BadRequest("No files received.");
        //         }
        //
        //         var documents = new List<Document>();
        //         foreach (var file in files)
        //         {
        //             var result = await _applicationService.StoreDocumentAsync(null, file);
        //             
        //             var document = new Document
        //             {
        //                 Name = result.DocumentName,
        //                 Path = result.DocumentPath,
        //                 Type = result.DocumentType,
        //             };
        //             documents.Add(document);
        //         }
        //
        //         _dbContext.Documents.AddRange(documents);
        //         await _dbContext.SaveChangesAsync(token);
        //
        //         return Ok("Files uploaded successfully.");
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.RecordFatalException(e);
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed to upload documents");
        //     }
        // }


        [HttpPut("{id:guid}/documents")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateDocuments([FromRoute] Guid id,
            [FromForm] string documents,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var request = JsonConvert.DeserializeObject<DocumentsUpdateDTO>(documents);
                var files = new List<IFormFile>();
                foreach (IFormFile file in Request.Form.Files)
                {
                    files.Add(file);
                }

                await using var transaction = await _dbContext.Database.BeginTransactionAsync();

                var application = await _dbContext.Applications
                    .Include(x => x.Documents)
                    .SingleOrDefaultAsync(a => a.Id == id, token);
                if (application == null)
                {
                    return NotFound("Application not found.");
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && application.PartnerId != GetPID())
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && application.Id != GetMID())
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                // Remove unused documents
                var documentIds = request.Documents.Select(d => d.Id).ToList();
                var documentsToRemove = application.Documents.Where(d => !documentIds.Contains(d.Id)).ToList();
                _dbContext.Documents.RemoveRange(documentsToRemove);

                // Add new documents from IFormFile list
                foreach (var file in files)
                {
                    await _applicationService.StoreDocumentAsync(id, file);
                }

                await _dbContext.SaveChangesAsync(token);

                await transaction.CommitAsync();

                return Ok("Documents updated successfully.");
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update documents");
            }
        }

        [HttpGet("{id:guid}/document/{documentId:guid}")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetDocumentLink([FromRoute] Guid id, [FromRoute] Guid documentId,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OnboardingController>(this, null, _globalData);
            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var application = await _dbContext.Applications
                    .Include(x => x.Documents)
                    .SingleOrDefaultAsync(a => a.Id == id, token);
                if (application == null)
                {
                    return NotFound("Application not found.");
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) && application.PartnerId != GetPID())
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) && application.Id != GetMID())
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                var url = await _applicationService.GetDocumentPublicUrlAsync(documentId);
                return Ok(url);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update document");
            }
        }
    }
}