{"app": {"name": "merchants-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "3v4ll30aqfb435trbds7447524", "Region": "us-east-1", "UserPoolId": "us-east-1_xHv2n5DhZ", "AppClientId": "3v4ll30aqfb435trbds7447524"}, "basicOauth": {"expiryMinutes": 60, "issuer": "Api-Client-Service", "SecretKey": "TAKEN FROM SECRETS", "Provider": "Api-Client-Service", "ValidateLifetime": true, "ValidateAudience": true, "ValidateIssuer": true, "ValidAudience": "merchants"}, "cache": {"connectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "idempotency.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "tracking.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379"}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "supportEmailTemplateId": "d-f2d69b4bb58348b099ecad5f50424688", "operationsRiskStatusUpdatedId": "d-42d6d861e0f841aa97d05456f8fca0eb", "application_Submitted_EmailTemplateId": "d-64e73a2e29334fd7bedcf60356554020", "application_Submitted_Internal_EmailTemplateId": "d-e64e57d492d348fd96da2ed79676a812", "application_Converted_Internal_EmailTemplateId": "d-42d6d861e0f841aa97d05456f8fca0eb", "applicant_Converted_Internal_EmailTemplateId": "d-c51a9ce91603495c87e473cac103f6cd", "applicant_Generic_EmailTemplate": "d-4e25550b13a74de9b1a81371b1f8d114", "application_StripeMerchantCreated_EmailTemplate": "d-b940c760edd94f9bafed97eeb733df57", "merchantActivatedEmailTemplateId": "d-bdb4309e154f4ab187af306f68246c27", "applicantReviewLink": "https://portal.flex-charge.com/auth/confirm-application", "senderEmail": "<EMAIL>", "applicationSenderEmail": "<EMAIL>", "senderName": "FlexFactor", "baseURL": "https://portal.flexfactor.io", "applicantNotifyEmailTemplateId": "d-d1d5e83e14ff4001990969b922210594"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/merchants-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "Merchants", "version": "v1", "routePrefix": "", "includeSecurity": true}, "io": {"logoImageValidation": {"minFileSize": 1024, "maxFileSize": 1048576, "minWidth": 32, "maxWidth": 2048, "minHeight": 32, "maxHeight": 2048, "minAspectRatio": 0.01, "maxAspectRatio": 100}}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "amazonS3Storage": {"publicFilesCDN": "https://deiofthelmp8.cloudfront.net"}, "documentUpload": {"bucket": "uploaded-documents-production", "maxFileSize": 20971520}, "AllowedHosts": "*"}