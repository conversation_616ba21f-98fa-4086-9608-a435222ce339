using System;
using System.Linq;
using FluentValidation;

namespace FlexCharge.Merchants.DTO;

public class ApplicationCreateRequestValidator : AbstractValidator<AdminApplicationCreateRequest>
{
    public ApplicationCreateRequestValidator()
    {
        RuleFor(x => x.PrimaryContact)
            .SetValidator(new PrimaryContactValidator())
            .When(x => x.PrimaryContact != null);
        
        RuleFor(x => x.DeveloperContact)
            .SetValidator(new DeveloperContactValidator())
            .When(x => x.DeveloperContact != null);

        RuleFor(x => x.CustomerSupportInformation)
            .SetValidator(new ApplicationCustomerSupportInformationDTOValidator())
            .When(x => x.CustomerSupportInformation != null);

        RuleFor(x => x.AccountManagerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("AccountManagerId must be a valid GUID.");;

        RuleFor(x => x.StandardFeeSelected)
            .Must(value => value == true || value == false)
            .WithMessage("StandardFeeSelected must be a valid boolean.");

        RuleForEach(x => x.Fees)
            .SetValidator(new ApplicationFeeDTOValidator(false))
            .When(x => x.Fees != null);

        RuleFor(x => x.AccountId)
            .Must(id => id == null || id != Guid.Empty)
            .WithMessage("AccountId must be a valid GUID.");

        RuleFor(x => x.Dba)
            .NotEmpty().WithMessage("Dba is required.");

        RuleFor(x => x.PartnerId)
            .NotEmpty().WithMessage("PartnerId is required.");
        
        RuleFor(x => x.IntegrationPartnerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("IntegrationPartnerId must be a valid GUID.");

        RuleFor(x => x.IntegrationTier)
            .NotEmpty().WithMessage("IntegrationTier is required.");

        RuleFor(x => x.SaveAndSend)
            .Must(value => value == true || value == false)
            .WithMessage("SaveAndSend must be a valid boolean.");

        RuleFor(x => x.TaxId)
            .MaximumLength(50).WithMessage("TaxId cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.TaxId));

        RuleFor(x => x.Descriptor)
            .MaximumLength(50).WithMessage("Descriptor cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Descriptor));

        RuleFor(x => x.BusinessModel)
            .SetValidator(new BusinessModelDTOValidator())
            .When(x => x.BusinessModel != null);

        RuleFor(x => x.BankAccountInformation)
            .SetValidator(new BankAccountInformationDTOValidator())
            .When(x => x.BankAccountInformation != null);

        RuleFor(x => x.TransactionInformation)
            .NotNull().WithMessage("TransactionInformation is required.")
            .SetValidator(new TransactionInformationDTOValidator())
            .When(x => x.TransactionInformation != null);

        RuleFor(x => x.Address)
            .SetValidator(new AddressDTOValidator())
            .When(x => x.Address != null);

        RuleFor(x => x.CustomFields)
            .ChildRules(fields =>
            {
                fields.RuleFor(f => f.Key)
                    .NotEmpty().WithMessage("CustomFields Key is required.");

                fields.RuleFor(f => f.Value)
                    .NotEmpty().WithMessage("CustomFields Value is required.");
            })
            .When(x => x.CustomFields != null);

        RuleFor(x => x.ProductInfo)
            .SetValidator(new ProductInfoDTOValidator())
            .When(x => x.ProductInfo != null);

        RuleForEach(x => x.Owners)
            .SetValidator(new OwnerDTOValidator())
            .When(x => x.Owners != null);
        
        RuleFor(x => x.ContractAlreadySigned)
            .Must(value => value == true || value == false || value == null)
            .WithMessage("ContractAlreadySigned must be true, false, or null.");
        
        RuleFor(x => x.IntegrationPartnerParticipateSale)
            .NotNull().WithMessage("IntegrationPartnerParticipateSale must be specified.")
            .When(x => x.IntegrationPartnerParticipateSale.HasValue);
        
        RuleFor(x => x.CrmId)
            .MaximumLength(50).WithMessage("CrmId cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.CrmId));

        RuleFor(x => x.CrmId)
            .MaximumLength(50).WithMessage("CrmId cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.CrmId));
    }
}

public class ApplicationUpdateRequestValidator : AbstractValidator<AdminApplicationUpdateRequest>
{
    public ApplicationUpdateRequestValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Id is required.");

        RuleFor(x => x.PrimaryContact)
            .NotNull().WithMessage("PrimaryContact is required.")
            .SetValidator(new PrimaryContactValidator());

        RuleFor(x => x.AccountManagerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("AccountManagerId must be a valid GUID.");

        RuleFor(x => x.StandardFeeSelected)
            .Must(value => value == true || value == false)
            .WithMessage("StandardFeeSelected must be a valid boolean.");

        RuleForEach(x => x.Fees)
            .SetValidator(new ApplicationFeeDTOValidator(true));

        RuleFor(x => x.AccountId)
            .Must(id => id == null || id != Guid.Empty)
            .WithMessage("AccountId must be a valid GUID.");

        RuleFor(x => x.Dba)
            .NotEmpty().WithMessage("Dba is required.");

        RuleFor(x => x.PartnerId)
            .NotEmpty().WithMessage("PartnerId is required.");
        
        RuleFor(x => x.IntegrationPartnerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("IntegrationPartnerId must be a valid GUID.");

        RuleFor(x => x.IntegrationTier)
            .NotEmpty().WithMessage("IntegrationTier is required.");
        
        RuleFor(x => x.DeveloperContact)
            .SetValidator(new DeveloperContactValidator())
            .When(x => x.DeveloperContact != null);
        
        RuleFor(x => x.CustomerSupportInformation)
            .SetValidator(new ApplicationCustomerSupportInformationDTOValidator())
            .When(x => x.CustomerSupportInformation != null);

        RuleFor(x => x.Address)
            .SetValidator(new AddressDTOValidator())
            .When(x => x.Address != null);

        RuleFor(x => x.TransactionInformation)
            .SetValidator(new TransactionInformationDTOValidator())
            .When(x => x.TransactionInformation != null);

        RuleFor(x => x.BankAccountInformation)
            .SetValidator(new BankAccountInformationDTOValidator())
            .When(x => x.BankAccountInformation != null);

        RuleFor(x => x.BusinessModel)
            .SetValidator(new BusinessModelDTOValidator())
            .When(x => x.BusinessModel != null);

        RuleFor(x => x.ProductInfo)
            .SetValidator(new ProductInfoDTOValidator())
            .When(x => x.ProductInfo != null);

        RuleForEach(x => x.Owners)
            .SetValidator(new OwnerDTOValidator());
        
        RuleFor(x => x.Send)
            .Must(value => value == true || value == false)
            .WithMessage("Send must be a valid boolean value.");
        
        RuleFor(x => x.ContractAlreadySigned)
            .Must(value => value == true || value == false || value == null)
            .WithMessage("ContractAlreadySigned must be true, false, or null.");
        
        RuleFor(x => x.IntegrationPartnerParticipateSale)
            .NotNull().WithMessage("IntegrationPartnerParticipateSale must be specified.")
            .When(x => x.IntegrationPartnerParticipateSale.HasValue);
        
        RuleFor(x => x.CrmId)
            .MaximumLength(50).WithMessage("CrmId cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.CrmId));
    }
}

public class ApplicationQuickCreateRequestValidator : AbstractValidator<ApplicationQuickCreateRequest>
{
    public ApplicationQuickCreateRequestValidator()
    {
        
        RuleFor(x => x.PrimaryContact)
            .NotNull().WithMessage("PrimaryContact is required.")
            .SetValidator(new PrimaryContactValidator());

        RuleFor(x => x.AccountManagerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("AccountManagerId must be a valid GUID");

        RuleFor(x => x.StandardFeeSelected)
            .Must(value => value == true || value == false)
            .WithMessage("StandardFeeSelected must be a valid boolean.");

        RuleForEach(x => x.Fees)
            .SetValidator(new ApplicationFeeDTOValidator(false));

        RuleFor(x => x.AccountId)
            .Must(id => id == null || id != Guid.Empty)
            .WithMessage("AccountId must be a valid GUID.");

        RuleFor(x => x.Dba)
            .NotEmpty().WithMessage("Dba is required.");

        RuleFor(x => x.PartnerId)
            .NotEmpty().WithMessage("PartnerId is required.");

        RuleFor(x => x.IntegrationTier)
            .NotEmpty().WithMessage("IntegrationTier is required.");
        
        RuleFor(x => x.SaveAndSend)
            .NotNull().WithMessage("SaveAndSend is required.");

        RuleFor(x => x.IntegrationPartnerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("IntegrationPartnerId must be a valid GUID.");

        RuleFor(x => x.SpecialTerms)
            .MaximumLength(500).WithMessage("SpecialTerms cannot exceed 500 characters.");
        
        RuleFor(x => x.CrmId)
            .MaximumLength(50).WithMessage("CrmId cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.CrmId));
    }
}

public class ApplicantBoardingRequestValidator : AbstractValidator<ApplicantBoardingRequestDTO>
{
    public ApplicantBoardingRequestValidator()
    {
        RuleFor(x => x.Next)
            .Must(value => value == true || value == false)
            .WithMessage("Next must be a valid boolean.");
        
        RuleFor(x => x.CompanyInformation)
            .SetValidator(new LegalEntityDTOValidator())
            .When(x => x.CompanyInformation != null);
        
        RuleFor(x => x.PrimaryContact)
            .SetValidator(new PrimaryContactValidator())
            .When(x => x.PrimaryContact != null);
        
        RuleFor(x => x.DeveloperContact)
            .SetValidator(new DeveloperContactValidator())
            .When(x => x.DeveloperContact != null);

        RuleFor(x => x.CustomerSupportContact)
            .SetValidator(new ApplicationCustomerSupportInformationDTOValidator())
            .When(x => x.CustomerSupportContact != null);
        
        RuleForEach(x => x.Owners)
            .SetValidator(new OwnerDTOValidator())
            .When(x => x.Owners != null);
        
        RuleFor(x => x.ProductInfo)
            .NotNull().WithMessage("ProductInfo is required.")
            .SetValidator(new ProductInfoDTOValidator())
            .When(x => x.Next);
        
        RuleFor(x => x.BusinessModel)
            .NotNull().WithMessage("BusinessModel is required.")
            .SetValidator(new BusinessModelDTOValidator())
            .When(x => x.Next);
        
        RuleFor(x => x.TransactionInformation)
            .NotNull().WithMessage("TransactionInformation is required.")
            .SetValidator(new TransactionInformationDTOValidator())
            .When(x => x.Next);
        
        RuleFor(x => x.Address)
            .SetValidator(new AddressDTOValidator())
            .When(x => x.Address != null);
        
        RuleFor(x => x.BankAccountInformation)
            .SetValidator(new BankAccountInformationDTOValidator())
            .When(x => x.BankAccountInformation != null);
        
        RuleFor(x => x.Documents)
            .NotNull().WithMessage("Documents are required.")
            .Must(documents => documents != null && documents.Any()).WithMessage("At least one document is required.")
            .When(x => x.Next)
            .ForEach(document => document.SetValidator(new DocumentDTOValidator()));
    }
}

public class ApplicationQuickUpdateRequestValidator : AbstractValidator<ApplicationQuickCreateRequest>
{
    public ApplicationQuickUpdateRequestValidator()
    {
        RuleFor(x => x.SaveAndSend)
            .NotNull().WithMessage("SaveAndSend is required.");

        RuleFor(x => x.IntegrationPartnerId)
            .Must(id => id == null || id != Guid.Empty).WithMessage("IntegrationPartnerId must be a valid GUID.");

        RuleFor(x => x.SpecialTerms)
            .MaximumLength(500).WithMessage("SpecialTerms cannot exceed 500 characters.");
        
        RuleFor(x => x.CrmId)
            .MaximumLength(50).WithMessage("CrmId cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.CrmId));
    }
}

public class
    ApplicationCustomerSupportInformationDTOValidator : AbstractValidator<ApplicationCustomerSupportInformationDTO>
{
    public ApplicationCustomerSupportInformationDTOValidator()
    {
        RuleFor(x => x.CustomerSupportName)
            .MaximumLength(100).WithMessage("CustomerSupportName cannot exceed 100 characters.")
            .When(x => !string.IsNullOrEmpty(x.CustomerSupportName));

        RuleFor(x => x.CustomerSupportEmail)
            .EmailAddress().WithMessage("CustomerSupportEmail must be a valid email address.")
            .When(x => !string.IsNullOrEmpty(x.CustomerSupportEmail));

        RuleFor(x => x.CustomerSupportPhone)
            .Matches(@"^\+?[1-9]\d{1,14}$").WithMessage("CustomerSupportPhone must be a valid phone number.")
            .When(x => !string.IsNullOrEmpty(x.CustomerSupportPhone));

        RuleFor(x => x.CustomerSupportLink)
            .Must(link => string.IsNullOrEmpty(link) || Uri.IsWellFormedUriString(link, UriKind.Absolute))
            .WithMessage("CustomerSupportLink must be a valid URL.");
    }
}

public class AddressDTOValidator : AbstractValidator<AddressDTO>
{
    public AddressDTOValidator()
    {
        RuleFor(x => x.Line1)
            .MaximumLength(500).WithMessage("Address line 1 cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.Line1));

        RuleFor(x => x.ZipCode)
            .MaximumLength(20).WithMessage("Zip code cannot exceed 20 characters.")
            .When(x => !string.IsNullOrEmpty(x.ZipCode));

        RuleFor(x => x.City)
            .MaximumLength(50).WithMessage("City cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.City));

        RuleFor(x => x.Country)
            .MaximumLength(50).WithMessage("Country cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Country));

        RuleFor(x => x.StateCode)
            .Length(2).WithMessage("State code must be 2 characters long.")
            .When(x => !string.IsNullOrEmpty(x.StateCode));
    }
}

public class TransactionInformationDTOValidator : AbstractValidator<TransactionInformationDTO>
{
    public TransactionInformationDTOValidator()
    {}
}

public class BankAccountInformationDTOValidator : AbstractValidator<BankAccountInformationDTO>
{
    public BankAccountInformationDTOValidator()
    {
        RuleFor(x => x.DdaType)
            .MaximumLength(50).WithMessage("DdaType cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.DdaType));

        RuleFor(x => x.AccountNumber)
            .MinimumLength(9).WithMessage("Account # cannot be less than 9 digits.")
            .MaximumLength(19).WithMessage("Account # cannot be greater than 19 digits.")
            .Matches(@"^\d+$").WithMessage("Account # must contain digits only.")
            .When(x => !string.IsNullOrEmpty(x.AccountNumber));

        RuleFor(x => x.RoutingNumber)
            .Matches(@"^\d+$").WithMessage("Routing # must contain digits only.")
            .When(x => !string.IsNullOrEmpty(x.RoutingNumber));

        RuleFor(x => x.BankName)
            .MaximumLength(500).WithMessage("Bank name cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.BankName));

        RuleFor(x => x.Currency)
            .MaximumLength(10).WithMessage("Currency cannot exceed 10 characters.")
            .When(x => !string.IsNullOrEmpty(x.Currency));
    }
}

public class BusinessModelDTOValidator : AbstractValidator<BusinessModelDTO>
{
    public BusinessModelDTOValidator()
    {
        RuleFor(x => x.Urls)
            .Must(urls => urls == null || urls.All(url => Uri.IsWellFormedUriString(url, UriKind.Absolute)))
            .WithMessage("All URLs must be valid.")
            .When(x => x.Urls != null);
    }
}

public class ProductInfoDTOValidator : AbstractValidator<ProductInfoDTO>
{
    public ProductInfoDTOValidator()
    {
        RuleFor(x => x.Regulated)
            .NotEmpty().WithMessage("Regulated field is required.")
            .MaximumLength(500).WithMessage("Regulated cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Regulated));

        RuleFor(x => x.Healthcare)
            .NotEmpty().WithMessage("Healthcare field is required.")
            .MaximumLength(500).WithMessage("Healthcare cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Healthcare));
    }
}

public class DeveloperContactValidator : AbstractValidator<DeveloperContact>
{
    public DeveloperContactValidator()
    {
        RuleFor(x => x.DeveloperFirstName)
            .NotEmpty().WithMessage("DeveloperFirstName is required.");

        RuleFor(x => x.DeveloperLastName)
            .NotEmpty().WithMessage("DeveloperLastName is required.");

        RuleFor(x => x.DeveloperEmail)
            .NotEmpty().WithMessage("DeveloperEmail is required.")
            .EmailAddress().WithMessage("DeveloperEmail must be a valid email address.");

        RuleFor(x => x.DeveloperPhone)
            .NotEmpty().WithMessage("DeveloperPhone is required.")
            .Matches(@"^\+?[1-9]\d{1,14}$").WithMessage("DeveloperPhone must be a valid phone number.");
    }
}

public class PrimaryContactValidator : AbstractValidator<PrimaryContact>
{
    public PrimaryContactValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First Name is required.");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last Name is required.");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required.")
            .EmailAddress().WithMessage("Invalid email format.");

        RuleFor(x => x.Phone)
            .NotEmpty().WithMessage("Phone is required.")
            .Matches(@"^\+?[1-9]\d{1,14}$").WithMessage("Invalid phone number format.");
    }
}

public class OwnerDTOValidator : AbstractValidator<OwnerDTO>
{
    public OwnerDTOValidator()
    {
        RuleFor(x => x.Id)
            .Must(id => id == null || id != Guid.Empty).WithMessage("Id must be a valid GUID.");

        RuleFor(x => x.Title)
            .MaximumLength(500).WithMessage("Title cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.Title));

        RuleFor(x => x.FirstName)
            .MaximumLength(50).WithMessage("First Name cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.FirstName));

        RuleFor(x => x.LastName)
            .MaximumLength(50).WithMessage("Last Name cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.LastName));

        RuleFor(x => x.Email)
            .EmailAddress().WithMessage("Invalid email format.")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.Phone)
            .Matches(@"^\+?[1-9]\d{1,14}$").WithMessage("Invalid phone number format.")
            .When(x => !string.IsNullOrEmpty(x.Phone));

        RuleFor(x => x.DateOfBirth)
            .LessThan(DateTime.Now).WithMessage("Date of Birth must be in the past.")
            .When(x => x.DateOfBirth.HasValue);

        RuleFor(x => x.PrincipalType)
            .MaximumLength(50).WithMessage("Principal Type cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.PrincipalType));

        RuleFor(x => x.IndividualFormOfID)
            .MaximumLength(50).WithMessage("Individual Form of ID cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.IndividualFormOfID));

        RuleFor(x => x.IdentificationNumber)
            .MaximumLength(50).WithMessage("Identification Number cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.IdentificationNumber));

        RuleFor(x => x.Address)
            .SetValidator(new AddressDTOValidator())
            .When(x => x.Address != null);

        RuleFor(x => x.PercentOwnership)
            .InclusiveBetween(0, 100).WithMessage("Percent Ownership must be between 0 and 100.")
            .When(x => x.PercentOwnership > 0);
    }
}

public class ApplicationFeeDTOValidator : AbstractValidator<ApplicationFeeDTO>
{
    public ApplicationFeeDTOValidator(bool isUpdateRequest)
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Id is required.")
            .When(_ => isUpdateRequest);

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Name is required.")
            .MaximumLength(100).WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Description is required.")
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters.");

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Type must be a valid enum value.");

        RuleFor(x => x.ChargeType)
            .IsInEnum().WithMessage("ChargeType must be a valid enum value.");

        RuleFor(x => x.Interchange)
            .MaximumLength(50).WithMessage("Interchange cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Interchange));

        RuleFor(x => x.Amount)
            .GreaterThan(0).WithMessage("Amount must be greater than 0.");

        RuleFor(x => x.MinimumFeeAmount)
            .GreaterThanOrEqualTo(0).WithMessage("MinimumFeeAmount must be greater than or equal to 0.")
            .When(x => x.MinimumFeeAmount.HasValue);
    }
}

public class LegalEntityDTOValidator : AbstractValidator<LegalEntityDTO>
{
    public LegalEntityDTOValidator()
    {
        RuleFor(x => x.LegalEntityName)
            .MaximumLength(500).WithMessage("Legal Entity Name cannot exceed 500 characters.");

        RuleFor(x => x.LegalEntityCountry)
            .MaximumLength(500).WithMessage("Legal Entity Country cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.LegalEntityCountry));

        RuleFor(x => x.Dba)
            .MaximumLength(500).WithMessage("DBA cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.Dba));

        RuleFor(x => x.Mcc)
            .MaximumLength(10).WithMessage("DBA cannot exceed 10 characters.") // TODO add MCC validation
            .When(x => !string.IsNullOrEmpty(x.Mcc));

        RuleFor(x => x.TaxId)
            .MaximumLength(50).WithMessage("TaxId cannot exceed 50 characters.") // TODO add EIN/TIN  validation
            .When(x => !string.IsNullOrEmpty(x.TaxId));

        RuleFor(x => x.EcommercePlatform)
            .MaximumLength(100).WithMessage("Ecommerce Platform cannot exceed 100 characters.")
            .When(x => !string.IsNullOrEmpty(x.EcommercePlatform));

        RuleFor(x => x.Descriptor)
            .MaximumLength(50).WithMessage("Descriptor cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Descriptor));

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters.")
            .When(x => !string.IsNullOrEmpty(x.Description));
    }
}

public class DocumentDTOValidator : AbstractValidator<DocumentDTO>
{
    public DocumentDTOValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Document ID is required.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Document name is required.")
            .MaximumLength(100).WithMessage("Document name cannot exceed 100 characters.");

        RuleFor(x => x.Path)
            .NotEmpty().WithMessage("Document path is required.")
            .MaximumLength(200).WithMessage("Document path cannot exceed 200 characters.");

        RuleFor(x => x.Type)
            .NotEmpty().WithMessage("Document type is required.")
            .MaximumLength(50).WithMessage("Document type cannot exceed 50 characters.");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Document description cannot exceed 500 characters.");

        RuleFor(x => x.CreatedOn)
            .LessThanOrEqualTo(DateTime.Now).WithMessage("CreatedOn must be in the past or present.");
    }
}