using FlexCharge.ApiClient.Authorization;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.ApiClient.Services;
using FlexCharge.ApiClient.DTO;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.ApiClient.Controllers.Public
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = AuthenticationSchemas.BasicAuthentication)]
    public class PartnerApiKeysController : BaseController
    {
        private readonly IPartnerApiKeysService _partnerApiKeysService;
        private readonly AppOptions _globalOptions;

        public PartnerApiKeysController(
            IOptions<AppOptions> globalOptions,
            IPartnerApiKeysService partnerApiKeysService)
        {
            _globalOptions = globalOptions.Value;
            _partnerApiKeysService = partnerApiKeysService;
        }

        [HttpGet("merchant/{mid:guid}")]
        [Authorize(ApiClientPolicies.CanRead)]
        [ProducesResponseType(typeof(PartnerApiKeysResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetMerchantApiKeys(Guid mid)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, null, _globalOptions)
                .LogEnterAndExit();

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();


                //Integration partner ID
                var partnerId = GetPID();

                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                    return BadRequest("Partner ID not found in token claims");

                return Ok(await _partnerApiKeysService.GetMerchantKeysByMid(partnerId.Value, mid));
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }


        /// <summary>
        /// Get API keys for the authenticated partner
        /// </summary>
        /// <param name="pageSize">Number of items per page (default: 10, max: 100)</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="type">Optional filter by key type (e.g., "production", "sandbox")</param>
        /// <param name="revoked">Optional filter by revoked status</param>
        /// <returns>Paginated list of API keys for the partner</returns>
        [HttpGet]
        [Authorize(ApiClientPolicies.CanRead)]
        [ProducesResponseType(typeof(PartnerApiKeysResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApiKeys(
            [FromQuery] int pageSize = 10,
            [FromQuery] int pageNumber = 1,
            [FromQuery] string? type = null,
            [FromQuery] bool? revoked = null)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, null, _globalOptions)
                .Baggage("pageSize", pageSize)
                .Baggage("pageNumber", pageNumber)
                .Baggage("type", type)
                .Baggage("revoked", revoked);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                // Validate pagination parameters
                if (pageSize <= 0 || pageSize > 100)
                {
                    return BadRequest("Page size must be between 1 and 100");
                }

                if (pageNumber <= 0)
                {
                    return BadRequest("Page number must be greater than 0");
                }

                //Integration partner ID
                var partnerId = GetPID();

                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var apiKeys = await _partnerApiKeysService.GetPartnerApiKeys(
                    partnerId.Value,
                    pageSize,
                    pageNumber,
                    type,
                    revoked);

                if (!apiKeys.Success)
                    return ReturnResponse(apiKeys);

                workspan.Log.Information(
                    $"Partner {partnerId} accessed API keys list - Page: {pageNumber}, Size: {pageSize}");
                return Ok(apiKeys);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        /// <summary>
        /// Create a new API key for the authenticated partner
        /// </summary>
        /// <param name="payload">API key creation request</param>
        /// <returns>Created API key with the secret value (shown only once)</returns>
        [HttpPost]
        [Authorize(ApiClientPolicies.CanWrite)]
        [ProducesResponseType(typeof(ApiKeyGenerationResult), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CreateApiKeys([FromBody] PartnerApiKeyCreateDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, payload, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var userId = GetUID();
                var partnerId = GetPID();

                workspan
                    .Baggage("UserId", userId)
                    .Baggage("PartnerId", partnerId);

                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                // Validate that the client ID belongs to the authenticated partner if provided
                if (payload.ClientId.HasValue)
                {
                    var hasAccess =
                        await _partnerApiKeysService.ValidatePartnerClientAccess(partnerId.Value,
                            payload.ClientId.Value);
                    if (!hasAccess)
                    {
                        workspan.Log.Warning(
                            $"Partner {partnerId} attempted to create API key for unauthorized client {payload.ClientId}");
                        return Forbid("Access denied: Client does not belong to your partner account");
                    }
                }

                var result = await _partnerApiKeysService.CreatePartnerApiKey(
                    partnerId.Value,
                    userId,
                    payload);

                workspan.Log.Information($"Partner {partnerId} created new API key for client {payload.ClientId}");
                return CreatedAtAction(nameof(GetApiKeys), new { }, result);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        /// <summary>
        /// Update an existing API key (description, note, type)
        /// </summary>
        /// <param name="keyId">API key ID</param>
        /// <param name="payload">Update request</param>
        /// <returns>Success response</returns>
        [HttpPut("{keyId:guid}")]
        [Authorize(ApiClientPolicies.CanManage)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateApiKey(Guid keyId, [FromBody] PartnerApiKeyUpdateDTO payload)
        {
            using var workspan =
                Workspan.StartEndpoint<PartnerApiKeysController>(this, new {keyId, payload}, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var partnerId = GetPID();
                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var result = await _partnerApiKeysService.UpdatePartnerApiKey(partnerId.Value, keyId, payload);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Partner {partnerId} updated API key {keyId}");
                return Ok(result);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        /// <summary>
        /// Revoke an API key
        /// </summary>
        /// <param name="keyId">API key ID to revoke</param>
        /// <returns>Success response</returns>
        [HttpDelete("{keyId:guid}")]
        [Authorize(ApiClientPolicies.CanManage)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RevokeApiKey(Guid keyId)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, keyId, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var partnerId = GetPID();
                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var result = await _partnerApiKeysService.RevokePartnerApiKey(partnerId.Value, keyId);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Partner {partnerId} revoked API key {keyId}");
                return Ok(result);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }
    }
}