using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.ApiClient.Services;
using FlexCharge.ApiClient.DTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using FlexCharge.ApiClient.Entities;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.ApiClient.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class ApiClientsController : BaseController
    {
        private readonly IApiIdentityService _apiIdentityService;
        private readonly AppOptions _globalOptions;
        private readonly PostgreSQLDbContext _dbContext;


        public ApiClientsController(
            IOptions<AppOptions> globalOptions,
            IApiIdentityService apiIdentityService, PostgreSQLDbContext dbContext)
        {
            _globalOptions = globalOptions.Value;
            _apiIdentityService = apiIdentityService;
            _dbContext = dbContext;
        }

        [HttpPost("/oauth2/token")]
        [AllowAnonymous]
        public async Task<IActionResult> Auth(ApiKeyVerify command)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, command, _globalOptions);

            if (!ModelState.IsValid)
            {
                return ValidationProblem();
            }

            JsonWebToken jsonWebToken = await _apiIdentityService.Authenticate(command.AppKey, command.AppSecret);

            if (!jsonWebToken.Success)
            {
                return ReturnResponse(jsonWebToken);
            }

            // if (!string.IsNullOrEmpty(jsonWebToken.RefreshToken))
            // {
            //     setTokenCookie(jsonWebToken.RefreshToken);
            // }

            return Ok(jsonWebToken);
        }

        [HttpGet("resources")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetResources(Guid? apiClient)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, null, _globalOptions);

            try
            {
                var apiClientClaims = new List<Guid>();
                if (apiClient != null)
                {
                    apiClientClaims = await _dbContext.ApiClientClaims
                        .Where(x => x.ApiClientId == apiClient)
                        .Select(x => x.ApiScopeClaimID)
                        .ToListAsync();
                }

                // match apiCliientClaims to resource claims and return
                var all = await _dbContext.ApiResources
                    .Include(x => x.Scopes)
                    .ThenInclude(x => x.ApiScopeClaims)
                    .Select(x => new
                    {
                        Name = x.Name,
                        DisplayName = x.DisplayName,
                        Description = x.Description,
                        Scopes = x.Scopes
                            .OrderBy(x => x.Name)
                            .Select(y => new
                            {
                                Id = y.Id,
                                Name = y.Name,
                                DisplayName = y.DisplayName,
                                Description = y.Description,

                                Claims = y.ApiScopeClaims
                                    .Select(z => new
                                    {
                                        Id = z.Id,
                                        Name = z.Type,
                                        DisplayName = z.Value,
                                        Description = z.Type,
                                        // ClientHasClaim = apiClientClaims.Any(x=>x.ApiScopeClaimID == z.Id)
                                        ClientHasClaim = apiClientClaims.Contains(z.Id)
                                    })
                            })
                    })
                    .ToListAsync();

                return Ok(all);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        [HttpPut]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Put(UpdateApiClientSecretsDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, payload, _globalOptions)
                .Baggage("apiClient", payload.apiClient.ToString());

            try
            {
                if (payload.description != null || payload.note != null)
                {
                    var clientSecret = await _dbContext.ApiClientSecrets
                        .FirstOrDefaultAsync(x => x.ClientId == payload.apiClient);

                    if (clientSecret != null)
                    {
                        if (payload.description != null)
                        {
                            clientSecret.Description = payload.description;
                        }

                        if (payload.note != null)
                        {
                            clientSecret.Note = payload.note;
                        }

                        _dbContext.ApiClientSecrets.Update(clientSecret);
                        await _dbContext.SaveChangesAsync();
                    }
                }

                var clientClaims = await _dbContext.ApiClientClaims.Where(x => x.ApiClientId == payload.apiClient)
                    .ToListAsync();

                var all = await _dbContext.ApiResources
                    .Include(x => x.Scopes)
                    .ThenInclude(x => x.ApiScopeClaims)
                    .Select(x => new
                    {
                        Name = x.Name,
                        Scopes = x.Scopes.Select(y => new
                        {
                            Id = y.Id,
                            Name = y.Name,
                        })
                    })
                    .ToListAsync();

                var existingScopes = all.SelectMany(x =>
                    x.Scopes.Where(y => payload.scopes.Contains(y.Id))
                        .Select(y => new {Name = x.Name + "." + y.Name, Id = y.Id})).ToList();
                // var scopesQuery = await _dbContext.ApiScopes
                //     .Include(c => c.ApiScopeClaims)
                //     .SelectMany(x => x.ApiScopeClaims.Select(y => y))
                //     .ToListAsync();

                foreach (var item in existingScopes)
                {
                    if (clientClaims.All(x =>
                            x.ApiScopeId != item.Id || x.ApiScopeId == item.Id && x.Value != item.Name))
                    {
                        var newClaim = new ApiClientClaim
                        {
                            Value = item.Name,
                            ApiClientId = payload.apiClient.Value,
                            Type = MyClaimTypes.SCOPE,
                            ApiScopeId = item.Id
                        };

                        _dbContext.ApiClientClaims.Add(newClaim);
                    }
                }

                foreach (var item in clientClaims)
                {
                    if (item.Type == MyClaimTypes.SCOPE && existingScopes.All(x => x.Name != item.Value))
                    {
                        _dbContext.ApiClientClaims.Remove(item);
                    }
                }

                await _dbContext.SaveChangesAsync();

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        [HttpPost("createApiKey")]
        //[ClaimRequirement(MyClaimTypes.PERMISSION, IdentityClaims.IDENTITY_MANAGE)]
        public async Task<IActionResult> CreateKeys([FromBody] ApiKeyCreateDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, null, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                ApiKeyGenerationResult key;
                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.SUPER_ADMIN))
                {
                    key = await _apiIdentityService.AdminGenerateKey(null, payload.Pid, GetUID(),
                        payload.Expiration, payload.Note, payload.Description);
                }
                else if (HttpContext.User.Claims.Any(x =>
                             x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.MERCHANT_ADMIN))
                {
                    //Will create api client if doesnt exist
                    key = await _apiIdentityService.AdminGenerateKey(GetMID(), GetPID(), GetUID(),
                        payload.Expiration, payload.Note, payload.Description);
                }
                else
                {
                    key = await _apiIdentityService.GenerateKey(payload.Expiration, payload.Note, payload.Description);
                }

                return Ok(key);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        [HttpPost("revoke")]
        //[ClaimRequirement(MyClaimTypes.PERMISSION, IdentityClaims.IDENTITY_MANAGE)]
        public async Task<IActionResult> RevokeKey(Guid keyId)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, keyId, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                await _apiIdentityService.RevokeKey(GetUID(), GetMID(), keyId);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetKeys(Guid? mid, Guid? pid)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, mid, _globalOptions)
                .Baggage("mid", mid)
                .Baggage("pid", pid)
                .LogEnterAndExit();

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                ApiKeysResult keys = new ApiKeysResult();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.MERCHANT_ADMIN) ||
                    HttpContext.IsUserInGroup(MerchantGroups.MERCHANT_DEVELOPER))
                {
                    workspan.Log.Information($"Generating new keys: {_globalOptions.Name} => Identity => GetKeys POST");

                    var existingKeysResult = await _apiIdentityService.GetKeys(GetMID(), pid, GetUID());
                    if (!existingKeysResult.Success)
                    {
                        //Will create api client if doesnt exist
                        await _apiIdentityService.AdminGenerateKey(GetMID(), GetPID(), GetUID(), 0,
                            "MerchantGenerated");
                    }

                    keys = await _apiIdentityService.GetKeys(GetMID(), pid, GetUID());
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    workspan.Log.Information(
                        $"Partner get keys: {_globalOptions.Name} => ApiClient => GetKeys POST");

                    var existingKeysResult = await _apiIdentityService.GetKeys(mid, GetPID(), null);
                    if (!existingKeysResult.Success)
                    {
                        //Will create api client if doesnt exist
                        await _apiIdentityService.AdminGenerateKey(mid, GetPID(), GetUID(), 0, "SystemGenerated");
                    }

                    keys = await _apiIdentityService.GetKeys(mid, GetPID(), null);
                }

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    workspan.Log.Information("Super admin get keys: {GlobalOptionsName} => ApiClient => GetKeys POST",
                        _globalOptions.Name);

                    var existingKeysResult = await _apiIdentityService.GetKeys(mid, pid, null);
                    if (!existingKeysResult.Success)
                    {
                        //Will create api client if doesnt exist
                        await _apiIdentityService.AdminGenerateKey(mid, null, GetUID(), 0, "SystemGenerated");
                    }

                    keys = await _apiIdentityService.GetKeys(mid, pid, null);
                }

                if (!keys.Success)
                    return ReturnResponse(keys);

                workspan.Log.Information("EXIT: {GlobalOptionsName} => Identity => GetKeys POST", _globalOptions.Name);
                return Ok(keys);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        [HttpGet("api-client-keys")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(ApiClientKeysResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApiClientKeys(Guid? pid, int pageSize = 10, int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, null, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var apiClients = await _apiIdentityService.GetApiClientKeys(
                    pageSize,
                    pageNumber,
                    pid);

                if (!apiClients.Success)
                    return ReturnResponse(apiClients);

                return Ok(apiClients);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        [HttpGet]
        [Route("{id:guid}/api-client-key")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(ApiClientKeysResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApiClientKey(Guid id)
        {
            using var workspan = Workspan.StartEndpoint<ApiClientsController>(this, id, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var apiClient = await _apiIdentityService.GetApiClientKey(id);

                return Ok(apiClient);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        private void setTokenCookie(string token)
        {
            var cookieOptions = new CookieOptions
            {
                Domain = "",
                HttpOnly = true,
                Secure = true,
                Expires = DateTime.UtcNow.AddDays(7)
            };
            Response.Cookies.Append("RefreshToken", token, cookieOptions);
        }
    }
}