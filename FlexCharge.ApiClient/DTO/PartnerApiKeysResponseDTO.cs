using FlexCharge.Common.Response;

namespace FlexCharge.ApiClient.DTO
{
    public class PartnerApiKeysResponseDTO : BaseResponse
    {
        public PartnerApiKeysPagedResult ApiKeys { get; set; } = new();
    }

    public class PartnerApiKeysPagedResult
    {
        public List<PartnerApiKeyItemDTO> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int) Math.Ceiling((double) TotalCount / PageSize);
        public bool HasNextPage => PageNumber < TotalPages;
        public bool HasPreviousPage => PageNumber > 1;
    }

    public class PartnerApiKeyItemDTO
    {
        public Guid Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// Always null for security - value is only shown during creation
        /// </summary>
        public string? Value => null;

        public TimeSpan Expiration { get; set; }
        public DateTime? RevokedAt { get; set; }
        public bool IsRevoked => RevokedAt.HasValue;
        public string? Type { get; set; }
        public Guid ClientId { get; set; }
        public DateTime LastUsed { get; set; }
        public string? Note { get; set; }
        public List<Guid> Scopes { get; set; } = new();
        public DateTime CreatedOn { get; set; }
        public DateTime ModifiedOn { get; set; }

        /// <summary>
        /// Indicates if the key is expired
        /// </summary>
        public bool IsExpired => Expiration != TimeSpan.Zero && DateTime.UtcNow > CreatedOn.Add(Expiration);

        /// <summary>
        /// Status of the API key
        /// </summary>
        public string Status
        {
            get
            {
                if (IsRevoked) return "Revoked";
                if (IsExpired) return "Expired";
                return "Active";
            }
        }
    }
}