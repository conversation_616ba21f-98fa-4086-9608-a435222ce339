using FlexCharge.ApiClient.DTO;
using FlexCharge.Common.Response;

namespace FlexCharge.ApiClient.Services;

public interface IPartnerApiKeysService
{
    /// <summary>
    /// Get paginated API keys for a specific partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="type">Optional filter by key type</param>
    /// <param name="revoked">Optional filter by revoked status</param>
    /// <returns>Paginated list of API keys</returns>
    Task<PartnerApiKeysResponseDTO> GetPartnerApiKeys(Guid partnerId, int pageSize, int pageNumber,
        string? type = null, bool? revoked = null);

    /// <summary>
    /// Create a new API key for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="userId">User ID creating the key</param>
    /// <param name="payload">API key creation request</param>
    /// <returns>Generated API key with secret (shown only once)</returns>
    Task<ApiKeyGenerationResult> CreatePartnerApiKey(Guid partnerId, Guid userId, PartnerApiKeyCreateDTO payload);

    /// <summary>
    /// Update an existing API key for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="keyId">API key ID</param>
    /// <param name="payload">Update request</param>
    /// <returns>Success response</returns>
    Task<SimpleResponse> UpdatePartnerApiKey(Guid partnerId, Guid keyId, PartnerApiKeyUpdateDTO payload);

    /// <summary>
    /// Revoke an API key for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="keyId">API key ID</param>
    /// <returns>Success response</returns>
    Task<SimpleResponse> RevokePartnerApiKey(Guid partnerId, Guid keyId);

    /// <summary>
    /// Validate that a partner has access to a specific client
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="clientId">Client ID</param>
    /// <returns>True if partner has access to the client</returns>
    Task<bool> ValidatePartnerClientAccess(Guid partnerId, Guid clientId);

    /// <summary>
    /// Validate that a partner has access to a specific merchant
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="merchantId">Merchant ID</param>
    /// <returns>True if partner is the integration partner for the merchant</returns>
    Task<bool> ValidatePartnerMerchantAccess(Guid partnerId, Guid merchantId);

    /// <summary>
    /// Get API keys for a specific merchant (used by partner endpoints)
    /// </summary>
    /// <param name="mid">Merchant ID</param>
    /// <returns>List of API keys for the merchant</returns>
    Task<ApiKeysResult> GetMerchantKeysByMid(Guid pid, Guid mid);

    /// <summary>
    /// Create a new API key for a merchant on behalf of a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="merchantId">Merchant ID</param>
    /// <param name="userId">User ID creating the key</param>
    /// <param name="payload">API key creation request</param>
    /// <returns>Generated API key with secret (shown only once)</returns>
    Task<ApiKeyGenerationResult> CreateMerchantApiKey(Guid partnerId, Guid merchantId, Guid userId, PartnerMerchantApiKeyCreateDTO payload);
}