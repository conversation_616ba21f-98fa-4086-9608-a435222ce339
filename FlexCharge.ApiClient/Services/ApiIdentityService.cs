using FlexCharge.Common.Authentication;
using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using FlexCharge.Common;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using MassTransit;

namespace FlexCharge.ApiClient.Services
{
    public class ApiIdentityService : IApiIdentityService
    {
        private readonly IJwtHandler _basicAuthenticationHandler;
        private readonly IApiKeyService _apikeyService;
        private readonly IClaimsProvider _claimsProvider;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IPublishEndpoint _publisher;
        private readonly IPasswordHasher<Entities.ApiClient> _passwordHasher;

        public ApiIdentityService(
            IJwtHandler basicAuthenticationHandler,
            IApiKeyService apikeyService,
            IClaimsProvider claimsProvider,
            IPasswordHasher<Entities.ApiClient> passwordHasher,
            PostgreSQLDbContext dbContext,
            IPublishEndpoint publisher)
        {
            _basicAuthenticationHandler = basicAuthenticationHandler;
            _apikeyService = apikeyService;
            //_refreshTokenRepository = refreshTokenRepository;
            //_refreshTokenService = refreshTokenService;
            _claimsProvider = claimsProvider;
            _passwordHasher = passwordHasher;
            _dbContext = dbContext;
            _publisher = publisher;
        }

        public async Task<JsonWebToken> Authenticate(string appKey, string appSecret)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("AppKey", appKey);

            var response = new JsonWebToken();

            try
            {
                var result = await _apikeyService.Verify(appKey, appSecret);
                if (result.verificationResult == ApiKeyService.ApiKeyVerificationResult.Failed)
                {
                    response.AddError(Consts.ApiKeyError);
                    return response;
                }

                var apiResources = await _dbContext.ApiResources.Select(x => x.Name).ToArrayAsync();
                var apiClientClaims = await _claimsProvider.GetApiClientClaimsAsync(result.apiClient.Id);
                //var claims = await _claimsProvider.GetApiScopesClaimsAsync();

                var mid = apiClientClaims.FirstOrDefault(x => x.Type == MyClaimTypes.MERCHANT_ID)?.Value;
                var pid = apiClientClaims.FirstOrDefault(x => x.Type == MyClaimTypes.PARTNER_ID)?.Value;

                var isPartner = !string.IsNullOrEmpty(pid) &&
                                (string.IsNullOrEmpty(mid) || pid == Guid.Empty.ToString());
                var expires = isPartner ? DateTime.Now.AddYears(10) : DateTime.Now.AddMinutes(30);

                response = _basicAuthenticationHandler.CreateToken(result.apiClient.Id.ToString(), apiResources, null,
                    apiClientClaims, expires);
                response.RefreshToken = CreateTokenForApiClient(result.apiClient, _passwordHasher);

                //Revoke old refresh tokens because a new login was performed
                //await _refreshTokenService.RevokeAllClientsAsync(result.apiClient.Id);
                //Add newly created token to db
                //await _refreshTokenRepository.AddAsync(refreshToken);

                return response;
            }
            catch (FormatException e)
            {
                workspan.RecordFatalException(e);
                response.AddError("Keys validation failed either AppKey or AppSecret is invalid", "ApiKeys");
            }

            return response;
        }

        private static string CreateTokenForApiClient(Entities.ApiClient client,
            IPasswordHasher<Entities.ApiClient> passwordHasher)
            => passwordHasher.HashPassword(client, Guid.NewGuid().ToString("N"))
                .Replace("=", string.Empty)
                .Replace("+", string.Empty)
                .Replace("/", string.Empty);

        public async Task<ApiKeyGenerationResult> AdminGenerateKey(Guid? mid, Guid? pid, Guid? userId, int expiration,
            string? note, string description = "Default Keys")
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Mid", mid)
                .Baggage("Pid", pid)
                .Baggage("UserId", userId)
                .Baggage("Expiration", expiration)
                .Baggage("Note", note)
                .Baggage("Description", description)
                .LogEnterAndExit();

            var response = new ApiKeyGenerationResult();

            try
            {
                var apiClient = await this.CreateApiClient(userId, mid, pid, new ApiClientRequest
                {
                    Name = description,
                    UniqueName = description,
                    Description = description,
                    Uri = new Uri("https://www.flexfactor.io")
                });

                workspan
                    .Tag("ApiClient", apiClient);

                if (apiClient.ClientId == Guid.Empty)
                {
                    workspan.Log.Error("ERROR Creating apiClient");
                    throw new FlexChargeException("Failed to create API client");
                }

                var result =
                    await _apikeyService.Generate(apiClient.ClientId, description, TimeSpan.FromMinutes(expiration),
                        note);


                if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                    string.IsNullOrEmpty(result.ApiClientSecret.Value))
                {
                    workspan.Log.Error("apikeyService Failed to generate API key");
                    throw new FlexChargeException("Failed to generate API key");
                }

                response.ClientId = result.ApiClientSecret.Key;
                response.Secret = result.ApiClientSecret.Value;

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }


        public async Task<ApiKeyGenerationResult> GenerateKey(int expiration, string? note,
            string description = "Default Keys"
        )
        {
            ApiKeyGenerationResult response = new ApiKeyGenerationResult();

            var result = await _apikeyService.Generate(Guid.NewGuid(), description,
                expiration == 0 ? TimeSpan.Zero : TimeSpan.FromMinutes(expiration), note);

            if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                string.IsNullOrEmpty(result.ApiClientSecret.Value))
            {
                throw new FlexChargeException(Consts.ApiKeyError);
            }

            response.ClientId = result.ApiClientSecret.Key;
            response.Secret = result.ApiClientSecret.Value;

            return response;
        }

        public async Task<ApiClientResult> CreateApiClient(Guid? uid, Guid? mid, Guid? pid, ApiClientRequest payload)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Uid", uid)
                .Baggage("Mid", mid)
                .Baggage("Pid", pid)
                .Payload(payload)
                .LogEnterAndExit();

            var response = new ApiClientResult();

            try
            {
                var newClient = new Entities.ApiClient
                {
                    Name = payload.Name,
                    UniqueName = payload.Description,
                    Description = payload.Description,
                    Mid = mid,
                    Pid = pid,
                    Uri = payload.Uri,
                    UserId = uid
                };

                await _dbContext.AddAsync(newClient);


                if (pid.HasValue && pid.Value != Guid.Empty)
                {
                    workspan.Log.Information(
                        "IN: ApiIdentityService > Adding Claim to ApiClientClaims");

                    _dbContext.ApiClientClaims.Add(new ApiClientClaim
                    {
                        Value = pid.ToString(),
                        Type = MyClaimTypes.PARTNER_ID,
                        ApiClientId = newClient.Id
                    });
                }

                if (mid != Guid.Empty)
                {
                    workspan.Log.Information(
                        "IN: ApiIdentityService > Adding Claim to ApiClientClaims");

                    _dbContext.ApiClientClaims.Add(new ApiClientClaim
                    {
                        Value = mid.ToString(),
                        Type = MyClaimTypes.MERCHANT_ID,
                        ApiClientId = newClient.Id
                    });
                }

                try
                {
                    foreach (var claim in payload.Claims)
                    {
                        _dbContext.ApiClientClaims.Add(new ApiClientClaim
                        {
                            Value = claim.Value,
                            Type = claim.Key,
                            ApiClientId = newClient.Id
                        });
                    }
                }
                catch (Exception e)
                {
                    workspan.Log.Fatal("Error adding claims to ApiClientClaims");
                }

                workspan
                    .Response(response);

                await _dbContext.SaveChangesAsync();

                await _publisher.Publish<ApiClientCreatedEvent>(new
                {
                    Id = newClient.Id
                });

                //TODO add api client claims/scopes

                response.ClientId = newClient.Id;
                return response;
            }
            catch (Exception e)
            {
                await _publisher.Publish<ApiClientCreateFailedEvent>(new
                {
                    Message = JsonConvert.SerializeObject(e)
                });

                workspan.RecordException(e);
                throw;
            }
        }

        public async Task RevokeKey(Guid? uid, Guid mid, Guid keyId)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Uid", uid)
                .Baggage("Mid", mid)
                .Baggage("KeyId", keyId)
                .LogEnterAndExit();

            try
            {
                var keyToRevoke = await _dbContext.ApiClientSecrets.Include(x => x.Client)
                    .Where(x => x.Client.Mid == mid && !x.RevokedAt.HasValue && x.Id == keyId)
                    .SingleOrDefaultAsync();

                ArgumentNullException.ThrowIfNull(keyToRevoke);

                await _apikeyService.RevokeAsync(keyToRevoke.Key, keyToRevoke.ClientId);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiKeysResult> GetKeys(Guid? mid, Guid? pid, Guid? userId)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Mid", mid)
                .Baggage("UserId", userId)
                .LogEnterAndExit();

            var response = new ApiKeysResult();

            try
            {
                var keys = _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .Where(x => !x.RevokedAt.HasValue)
                    .AsQueryable();

                if (mid != null && mid != Guid.Empty && (pid == null || pid == Guid.Empty))
                {
                    keys = keys.Where(x => x.Client.Mid == mid);
                }
                else if (mid != null && mid != Guid.Empty && pid != null && pid != Guid.Empty)
                {
                    keys = keys.Where(x => x.Client.Mid == mid && x.Client.Pid == pid);
                }
                else if (pid != null && pid != Guid.Empty)
                {
                    keys = keys.Where(x => x.Client.Pid == pid && (x.Client.Mid == null || x.Client.Mid == Guid.Empty));
                }
                else
                {
                    throw new FlexNotFoundException("Mid or Pid is required");
                }

                // if (userId != Guid.Empty)
                //     keys = keys.Where(x => x.Client.UserId == userId);

                response.Keys = await keys.Select(key => new ApiKeyDTO
                {
                    //Name = key.Description,
                    Key = key.Key,
                    Secret = key.Value
                }).ToListAsync();

                if (!response.Keys.Any())
                    throw new FlexNotFoundException("Keys not found");


                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiClientKeysResponseDTO> GetApiClientKeys(int pageSize, int pageNumber, Guid? pid)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .LogEnterAndExit();

            var response = new ApiClientKeysResponseDTO();

            try
            {
                var dbset = _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .Where(x => !x.IsDeleted)
                    .OrderByDescending(x => x.CreatedOn)
                    .AsQueryable();

                if (pid.HasValue)
                {
                    dbset =
                        dbset.Where(x => x.Client.Pid == pid && (x.Client.Mid == null || x.Client.Mid == Guid.Empty));
                }

                var apiClientKeys = await dbset
                    .GroupJoin(_dbContext.ApiClientClaims, x => x.ClientId, y => y.ApiClientId,
                        (apiClientSecret, apiClientClaim) => new {apiClientSecret, apiClientClaim})
                    .Select(x => new ApiClientSecretDTO
                    {
                        Id = x.apiClientSecret.Id,
                        Description = x.apiClientSecret.Description,
                        Key = x.apiClientSecret.Key,
                        Value = x.apiClientSecret.Value,
                        Expiration = x.apiClientSecret.Expiration,
                        Type = x.apiClientSecret.Type,
                        ClientId = x.apiClientSecret.ClientId,
                        LastUsed = x.apiClientSecret.LastUsed,
                        Note = x.apiClientSecret.Note,
                        Scopes = x.apiClientClaim
                            .Where(x => x.ApiScopeId != null)
                            .Select(y => y.ApiScopeId.Value)
                            .ToList()
                    })
                    .ToPagedListAsync(pageNumber, pageSize);

                // Manual mapping to replace AutoMapper
                response.ApiClients = new PagedDTO<ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO>
                {
                    rows = apiClientKeys.Select(x => new ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO
                    {
                        Id = x.Id,
                        Description = x.Description,
                        Key = x.Key,
                        Value = null, // Always exclude Value for security
                        Expiration = x.Expiration,
                        Type = x.Type,
                        ClientId = x.ClientId,
                        LastUsed = x.LastUsed,
                        Note = x.Note,
                        Scopes = x.Scopes
                    }).ToList(),
                    TotalItemCount = apiClientKeys.TotalItemCount,
                    PageNumber = apiClientKeys.PageNumber,
                    PageSize = apiClientKeys.PageSize
                };

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO> GetApiClientKey(Guid id)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Id", id)
                .LogEnterAndExit();

            try
            {
                var apiClientKeys = await _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .FirstOrDefaultAsync(x => x.Id == id);

                var scopes = await _dbContext.ApiClientClaims
                    .Where(x => x.IsDeleted == false && x.ApiClientId == apiClientKeys.ClientId && x.ApiScopeId != null)
                    .Select(x => x.ApiScopeId.Value)
                    .ToListAsync();

                var response = new ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO
                {
                    Id = apiClientKeys.Id,
                    Description = apiClientKeys.Description,
                    Key = apiClientKeys.Key,
                    Value = apiClientKeys.Value, // Always exclude Value for security - only shown during creation
                    Expiration = apiClientKeys.Expiration,
                    Type = apiClientKeys.Type,
                    ClientId = apiClientKeys.ClientId,
                    LastUsed = apiClientKeys.LastUsed,
                    Note = apiClientKeys.Note,
                    Scopes = scopes
                };

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }
    }
}