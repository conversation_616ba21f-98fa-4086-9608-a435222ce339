using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Entities;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Merchants;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.ApiClient.Services;

public class PartnerApiKeysService : IPartnerApiKeysService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IApiKeyService _apiKeyService;
    private readonly IRequestClient<GetMerchantCommand> _getMerchantRequestClient;

    public PartnerApiKeysService(
        PostgreSQLDbContext dbContext,
        IApiKeyService apiKeyService,
        IRequestClient<GetMerchantCommand> getMerchantRequestClient)
    {
        _dbContext = dbContext;
        _apiKeyService = apiKeyService;
        _getMerchantRequestClient = getMerchantRequestClient;
    }

    public async Task<PartnerApiKeysResponseDTO> GetPartnerApiKeys(Guid partnerId, int pageSize, int pageNumber,
        string? type = null, bool? revoked = null)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("PageSize", pageSize)
            .Baggage("PageNumber", pageNumber)
            .Baggage("Type", type)
            .Baggage("Revoked", revoked)
            .LogEnterAndExit();

        var response = new PartnerApiKeysResponseDTO();

        try
        {
            if (partnerId == Guid.Empty)
            {
                response.AddError("Partner ID not found in token claims");
                return response;
            }

            //Search if has IPID claim 
            var apiClient = await _dbContext.ApiClientClaims
                .Include(x => x.ApiClient)
                .AnyAsync(x => x.ApiClientId == partnerId && x.Type == MyClaimTypes.INTEGRATION_PARTNER_ID);

            var dbset = _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .Where(x => !x.IsDeleted && x.Client.Pid == partnerId)
                .OrderByDescending(x => x.CreatedOn)
                .AsQueryable();

            // Apply optional filters
            if (!string.IsNullOrEmpty(type))
            {
                dbset = dbset.Where(x => x.Type == type);
            }

            if (revoked.HasValue)
            {
                if (revoked.Value)
                {
                    dbset = dbset.Where(x => x.RevokedAt.HasValue);
                }
                else
                {
                    dbset = dbset.Where(x => !x.RevokedAt.HasValue);
                }
            }

            var apiKeys = await dbset
                .GroupJoin(_dbContext.ApiClientClaims, x => x.ClientId, y => y.ApiClientId,
                    (apiClientSecret, apiClientClaim) => new {apiClientSecret, apiClientClaim})
                .Select(x => new PartnerApiKeyItemDTO
                {
                    Id = x.apiClientSecret.Id,
                    Description = x.apiClientSecret.Description,
                    Key = x.apiClientSecret.Key,
                    Expiration = x.apiClientSecret.Expiration,
                    RevokedAt = x.apiClientSecret.RevokedAt,
                    Type = x.apiClientSecret.Type,
                    ClientId = x.apiClientSecret.ClientId,
                    LastUsed = x.apiClientSecret.LastUsed,
                    Note = x.apiClientSecret.Note,
                    Scopes = x.apiClientClaim
                        .Where(claim => claim.ApiScopeId != null)
                        .Select(claim => claim.ApiScopeId.Value)
                        .ToList(),
                    CreatedOn = x.apiClientSecret.CreatedOn,
                    ModifiedOn = x.apiClientSecret.ModifiedOn
                })
                .ToPagedListAsync(pageNumber, pageSize);

            response.ApiKeys = new PartnerApiKeysPagedResult
            {
                Items = apiKeys.ToList(),
                TotalCount = apiKeys.TotalItemCount,
                PageNumber = apiKeys.PageNumber,
                PageSize = apiKeys.PageSize
            };

            workspan.Log.Information($"Retrieved {apiKeys.Count} API keys for partner {partnerId}");
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            response.AddError(e.Message);
            return response;
        }
    }

    public async Task<ApiKeyGenerationResult> CreatePartnerApiKey(Guid partnerId, Guid userId,
        PartnerApiKeyCreateDTO payload)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("UserId", userId)
            .Baggage("ClientId", payload.ClientId)
            .Baggage("Description", payload.Description)
            .Baggage("Expiration", payload.Expiration)
            .LogEnterAndExit();

        var response = new ApiKeyGenerationResult();

        try
        {
            Guid clientId;

            // If client ID is provided, validate it belongs to the partner
            if (payload.ClientId.HasValue)
            {
                var hasAccess = await ValidatePartnerClientAccess(partnerId, payload.ClientId.Value);
                if (!hasAccess)
                {
                    workspan.Log.Error("Access denied: Client does not belong to your partner account");
                    throw new UnauthorizedAccessException(
                        "Access denied: Client does not belong to your partner account");
                }

                clientId = payload.ClientId.Value;
            }
            else
            {
                // Create a new API client for the partner
                var newClient = new Entities.ApiClient
                {
                    Name = payload.Description,
                    UniqueName = payload.Description,
                    Description = payload.Description,
                    Pid = partnerId,
                    UserId = userId,
                    Uri = new Uri("https://www.flexfactor.io")
                };

                await _dbContext.AddAsync(newClient);

                // Add partner ID claim
                _dbContext.ApiClientClaims.Add(new ApiClientClaim
                {
                    Value = partnerId.ToString(),
                    Type = MyClaimTypes.PARTNER_ID,
                    ApiClientId = newClient.Id
                });

                await _dbContext.SaveChangesAsync();
                clientId = newClient.Id;
            }

            // Generate the API key
            var expiration = payload.Expiration == 0 ? TimeSpan.Zero : TimeSpan.FromMinutes(payload.Expiration);
            var result = await _apiKeyService.Generate(clientId, payload.Description, expiration, payload.Note);

            if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                string.IsNullOrEmpty(result.ApiClientSecret.Value))
            {
                workspan.Log.Error("ERROR Generating API key for partner");
                throw new FlexChargeException("Failed to generate API key");
            }

            // Set the type if provided
            if (!string.IsNullOrEmpty(payload.Type))
            {
                var apiClientSecret = await _dbContext.ApiClientSecrets
                    .FirstOrDefaultAsync(x => x.Key == result.ApiClientSecret.Key);
                if (apiClientSecret != null)
                {
                    apiClientSecret.Type = payload.Type;
                    _dbContext.ApiClientSecrets.Update(apiClientSecret);
                    await _dbContext.SaveChangesAsync();
                }
            }

            // Add scopes if provided
            if (payload.Scopes != null && payload.Scopes.Any())
            {
                var scopeClaims = payload.Scopes.Select(scopeId => new ApiClientClaim
                {
                    Value = scopeId.ToString(),
                    Type = MyClaimTypes.SCOPE,
                    ApiClientId = clientId,
                    ApiScopeId = scopeId
                }).ToList();

                _dbContext.ApiClientClaims.AddRange(scopeClaims);
                await _dbContext.SaveChangesAsync();
            }

            response.ClientId = result.ApiClientSecret.Key;
            response.Secret = result.ApiClientSecret.Value;

            workspan.Log.Information($"Successfully created API key for partner {partnerId}");
            return response;
        }
        catch (FlexChargeException e)
        {
            workspan.RecordException(e);
            throw;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<SimpleResponse> UpdatePartnerApiKey(Guid partnerId, Guid keyId, PartnerApiKeyUpdateDTO payload)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("KeyId", keyId)
            .LogEnterAndExit();

        var response = new SimpleResponse();

        try
        {
            // Find the API key and validate it belongs to the partner
            var apiKey = await _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .FirstOrDefaultAsync(x => x.Id == keyId && x.Client.Pid == partnerId && !x.IsDeleted);

            if (apiKey == null)
            {
                response.AddError("API key not found or access denied");
                return response;
            }

            // Update fields if provided
            bool hasChanges = false;

            if (!string.IsNullOrEmpty(payload.Description))
            {
                apiKey.Description = payload.Description;
                hasChanges = true;
            }

            if (payload.Note != null)
            {
                apiKey.Note = payload.Note;
                hasChanges = true;
            }

            if (!string.IsNullOrEmpty(payload.Type))
            {
                apiKey.Type = payload.Type;
                hasChanges = true;
            }

            if (hasChanges)
            {
                _dbContext.ApiClientSecrets.Update(apiKey);
                await _dbContext.SaveChangesAsync();
            }

            // Update scopes if provided
            if (payload.Scopes != null)
            {
                // Remove existing scope claims
                var existingClaims = await _dbContext.ApiClientClaims
                    .Where(x => x.ApiClientId == apiKey.ClientId && x.Type == MyClaimTypes.SCOPE)
                    .ToListAsync();

                _dbContext.ApiClientClaims.RemoveRange(existingClaims);

                // Add new scope claims
                var newClaims = payload.Scopes.Select(scopeId => new ApiClientClaim
                {
                    Value = scopeId.ToString(),
                    Type = MyClaimTypes.SCOPE,
                    ApiClientId = apiKey.ClientId,
                    ApiScopeId = scopeId
                }).ToList();

                _dbContext.ApiClientClaims.AddRange(newClaims);
                await _dbContext.SaveChangesAsync();
            }

            workspan.Log.Information($"Successfully updated API key {keyId} for partner {partnerId}");
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            response.AddError(e.Message);
            return response;
        }
    }

    public async Task<SimpleResponse> RevokePartnerApiKey(Guid partnerId, Guid keyId)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("KeyId", keyId)
            .LogEnterAndExit();

        var response = new SimpleResponse();

        try
        {
            // Find the API key and validate it belongs to the partner
            var apiKey = await _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .FirstOrDefaultAsync(x => x.Id == keyId && x.Client.Pid == partnerId && !x.IsDeleted);

            if (apiKey == null)
            {
                response.AddError("API key not found or access denied", "api-keys");
                return response;
            }

            if (apiKey.RevokedAt.HasValue)
            {
                response.AddError("API key is already revoked", "api-keys");
                return response;
            }

            // Revoke the key
            apiKey.Revoke();
            _dbContext.ApiClientSecrets.Update(apiKey);
            await _dbContext.SaveChangesAsync();

            workspan.Log.Information($"Successfully revoked API key {keyId} for partner {partnerId}");
            return response;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            response.AddError(e.Message);
            return response;
        }
    }

    public async Task<bool> ValidatePartnerClientAccess(Guid partnerId, Guid clientId)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("ClientId", clientId)
            .LogEnterAndExit();

        try
        {
            var client = await _dbContext.ApiClients
                .FirstOrDefaultAsync(x => x.Id == clientId && x.Pid == partnerId && !x.IsDeleted);

            return client != null;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<bool> ValidatePartnerMerchantAccess(Guid partnerId, Guid merchantId)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("MerchantId", merchantId)
            .LogEnterAndExit();

        try
        {
            // Use MassTransit to get merchant information from the Merchants microservice
            var response = await _getMerchantRequestClient.GetResponse<GetMerchantCommandResponse>(
                new GetMerchantCommand {MerchantId = merchantId});

            var merchantInfo = response.Message;

            if (!string.IsNullOrEmpty(merchantInfo.Error))
            {
                workspan.Log.Warning($"Error getting merchant info: {merchantInfo.Error}");
                return false;
            }

            // Check if the partner is the integration partner for this merchant
            return merchantInfo.IntegrationPartnerId == partnerId;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<ApiKeysResult> GetMerchantKeysByMid(Guid pid, Guid mid)
    {
        using var workspan = Workspan.Start<PartnerApiKeysService>()
            .Baggage("Mid", mid)
            .Baggage("Pid", pid)
            .LogEnterAndExit();

        var response = new ApiKeysResult();

        try
        {
            var getMerchantResponse = await _getMerchantRequestClient.GetResponse<GetMerchantCommandResponse>(
                new GetMerchantCommand {MerchantId = mid});

            if (getMerchantResponse.Message.Error != null)
                throw new FlexChargeException(getMerchantResponse.Message.Error, "api-keys");

            if (pid == Guid.Empty && getMerchantResponse.Message.IntegrationPartnerId != pid)
                throw new UnauthorizedAccessException("Unauthorized access");

            var keys = _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .Where(x => !x.RevokedAt.HasValue)
                .AsQueryable();

            if (mid != Guid.Empty)
                keys = keys.Where(x => x.Client.Mid == mid);
            else
                throw new FlexChargeException("api-keys", "invalid merchant id");


            response.Keys = await keys.Select(key => new ApiKeyDTO
            {
                Key = key.Key,
                Secret = key.Value
            }).ToListAsync();

            if (!response.Keys.Any())
                throw new FlexChargeException("api-keys", "No keys found");

            return response;
        }
        catch (FlexChargeException e)
        {
            workspan.RecordException(e);
            throw;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}