{"app": {"name": "api-client-service", "version": "0.0.1"}, "AWS": {"Region": "us-east-1", "UserPoolClientId": "4n0i8a4i9o1vk3g3tf64o6blg7", "UserPoolClientSecret": "<your user pool client secret goes here>", "UserPoolId": "us-east-1_rCUpTgXY4"}, "jwt": {"Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "expiryMinutes": 60, "ValidAudience": "4n0i8a4i9o1vk3g3tf64o6blg7", "Region": "us-east-1", "UserPoolId": "us-east-1_rCUpTgXY4", "AppClientId": "4n0i8a4i9o1vk3g3tf64o6blg7"}, "cache": {"connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "basicOauth": {"expiryMinutes": 3600, "issuer": "Api-Client-Service", "SecretKey": "TAKEN FROM SECRETS", "Provider": "Api-Client-Service", "ValidateLifetime": true, "ValidateAudience": true, "ValidateIssuer": true, "ValidAudiences": ["api-clients"]}, "stripe": {"ClientId": "ca_Rmj9WFqo4MWz7sS5XRW6tL2g8Leljw5N", "OAuthChannelLink": "chnlink_61S2vjvQUjNzRGPjR41ASvoa56SvFW5g", "OAuthRedirectUrl": "https://localhost:7079/StripeOAuth", "SuccessRedirectUrl": "http://localhost:3011/stripe-success", "FailureRedirectUrl": "http://localhost:3011/stripe-decline"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/api-client-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "servers": ["https://api-sandbox.flex-charge.com", "https://api.flex-charge.com"], "reDocEnabled": false, "name": "v1", "title": "api-client-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "localhost", "agentPort": 6831}, "AllowedHosts": "*"}